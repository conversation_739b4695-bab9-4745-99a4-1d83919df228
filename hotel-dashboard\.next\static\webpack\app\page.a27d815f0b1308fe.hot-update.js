"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx":
/*!***********************************************!*\
  !*** ./app/(dashboard)/log-analysis/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogAnalysisPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/log-analysis/LogDisplayArea */ \"(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\");\n/* harmony import */ var _components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/log-analysis/LogChartView */ \"(app-pages-browser)/./components/log-analysis/LogChartView.tsx\");\n/* harmony import */ var _components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/log-analysis/LogFileUpload */ \"(app-pages-browser)/./components/log-analysis/LogFileUpload.tsx\");\n/* harmony import */ var _components_log_analysis_SnSearchBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/log-analysis/SnSearchBar */ \"(app-pages-browser)/./components/log-analysis/SnSearchBar.tsx\");\n/* harmony import */ var _components_log_analysis_ImageNameSearch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/log-analysis/ImageNameSearch */ \"(app-pages-browser)/./components/log-analysis/ImageNameSearch.tsx\");\n/* harmony import */ var _components_log_analysis_BatchExportCSV__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/log-analysis/BatchExportCSV */ \"(app-pages-browser)/./components/log-analysis/BatchExportCSV.tsx\");\n/* harmony import */ var _utils_snHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/snHelper */ \"(app-pages-browser)/./utils/snHelper.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_exportUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/exportUtils */ \"(app-pages-browser)/./lib/exportUtils.ts\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LogAnalysisPage() {\n    _s();\n    const [dataChunks, setDataChunks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Unified selection state\n    const [selectedBlockIds, setSelectedBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // SN Search states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSnSearching, setIsSnSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Image Name Search states\n    const [isImageNameSearching, setIsImageNameSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // --- Queued export states ---\n    const [exportQueue, setExportQueue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentlyExportingBlockId, setCurrentlyExportingBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedImages, setGeneratedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exportProgress, setExportProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const exportTargetContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const logParserWorker = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    // --- Worker Initialization and Message Handling ---\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            logParserWorker.current = new Worker(__webpack_require__.tu(new URL(/* worker import */ __webpack_require__.p + __webpack_require__.u(\"_app-pages-browser_workers_logParser_worker_ts-_0cdd0\"), __webpack_require__.b)));\n            logParserWorker.current.onmessage = ({\n                \"LogAnalysisPage.useEffect\": (event)=>{\n                    const { type, payload, error } = event.data;\n                    switch(type){\n                        case 'PARSE_LOG_RESULT':\n                            setIsLoading(false);\n                            if (error) {\n                                handleError(error);\n                            } else {\n                                handleDataProcessed(payload);\n                            }\n                            break;\n                        case 'MATCH_BY_TIMESTAMP_RESULT':\n                            setIsImageNameSearching(false);\n                            if (error) {\n                                toast({\n                                    title: \"图片名称搜索失败\",\n                                    description: error,\n                                    variant: \"destructive\"\n                                });\n                            } else {\n                                const { matchedBlockIds } = payload;\n                                setSelectedBlockIds({\n                                    \"LogAnalysisPage.useEffect\": (prevIds)=>new Set([\n                                            ...Array.from(prevIds),\n                                            ...matchedBlockIds\n                                        ])\n                                }[\"LogAnalysisPage.useEffect\"]);\n                                toast({\n                                    title: \"图片名称搜索完成\",\n                                    description: \"匹配到 \".concat(matchedBlockIds.length, \" 个新的数据块。\")\n                                });\n                            }\n                            break;\n                        default:\n                            break;\n                    }\n                }\n            })[\"LogAnalysisPage.useEffect\"];\n            return ({\n                \"LogAnalysisPage.useEffect\": ()=>{\n                    var _logParserWorker_current;\n                    (_logParserWorker_current = logParserWorker.current) === null || _logParserWorker_current === void 0 ? void 0 : _logParserWorker_current.terminate();\n                }\n            })[\"LogAnalysisPage.useEffect\"];\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        toast\n    ]);\n    const handleProcessingStart = ()=>{\n        setIsLoading(true);\n        setError(null);\n        setSelectedBlockIds(new Set());\n        setDataChunks([]);\n    };\n    const handleDataProcessed = (workerData)=>{\n        const processedData = workerData.map((block)=>({\n                ...block,\n                data: []\n            }));\n        setDataChunks(processedData);\n    };\n    const handleError = (errorMessage)=>{\n        setError(errorMessage);\n        setIsLoading(false);\n    };\n    const handleBlockSelectionChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogAnalysisPage.useCallback[handleBlockSelectionChanged]\": (selectedIds)=>{\n            setSelectedBlockIds(selectedIds);\n        }\n    }[\"LogAnalysisPage.useCallback[handleBlockSelectionChanged]\"], []);\n    const selectedBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[selectedBlocks]\": ()=>{\n            return dataChunks.filter({\n                \"LogAnalysisPage.useMemo[selectedBlocks]\": (block)=>selectedBlockIds.has(block.block_id)\n            }[\"LogAnalysisPage.useMemo[selectedBlocks]\"]);\n        }\n    }[\"LogAnalysisPage.useMemo[selectedBlocks]\"], [\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const chartDataForView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[chartDataForView]\": ()=>{\n            return selectedBlocks;\n        }\n    }[\"LogAnalysisPage.useMemo[chartDataForView]\"], [\n        selectedBlocks\n    ]);\n    const handleSnSearch = (query)=>{\n        setSearchQuery(query);\n        setIsSnSearching(true);\n        const snsToSearch = (0,_utils_snHelper__WEBPACK_IMPORTED_MODULE_8__.parseSnInput)(query).map((sn)=>sn.toUpperCase());\n        if (snsToSearch.length === 0) {\n            setSelectedBlockIds(new Set());\n            setIsSnSearching(false);\n            return;\n        }\n        const results = new Set();\n        dataChunks.forEach((block)=>{\n            const snFromId = block.block_id.split('_')[0].toUpperCase();\n            for (const sn of snsToSearch){\n                const isSnInSnsArray = Array.isArray(block.sns) && block.sns.some((blockSn)=>blockSn.toUpperCase() === sn);\n                if (snFromId === sn || isSnInSnsArray) {\n                    results.add(block.block_id);\n                    break;\n                }\n            }\n        });\n        setSelectedBlockIds(results);\n        toast({\n            title: \"SN搜索完成\",\n            description: \"找到 \".concat(results.size, \" 个相关数据块。\")\n        });\n    };\n    const handleClearSearch = ()=>{\n        setSearchQuery('');\n        setSelectedBlockIds(new Set());\n        setIsSnSearching(false);\n    };\n    const handleImageNameSearch = (timestamps)=>{\n        if (!logParserWorker.current) {\n            toast({\n                title: \"错误\",\n                description: \"日志解析器未初始化。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (timestamps.length === 0) {\n            toast({\n                title: \"提示\",\n                description: \"没有从文件名中解析出有效的时间戳。\",\n                variant: \"default\"\n            });\n            return;\n        }\n        setIsImageNameSearching(true);\n        toast({\n            title: \"正在搜索...\",\n            description: \"根据 \".concat(timestamps.length, \" 个时间戳进行匹配。\")\n        });\n        const message = {\n            type: 'MATCH_BY_TIMESTAMP',\n            payload: {\n                timestamps\n            }\n        };\n        logParserWorker.current.postMessage(message);\n    };\n    const displayedDataChunks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[displayedDataChunks]\": ()=>{\n            if (!isSnSearching) {\n                return dataChunks;\n            }\n            return dataChunks.filter({\n                \"LogAnalysisPage.useMemo[displayedDataChunks]\": (chunk)=>selectedBlockIds.has(chunk.block_id)\n            }[\"LogAnalysisPage.useMemo[displayedDataChunks]\"]);\n        }\n    }[\"LogAnalysisPage.useMemo[displayedDataChunks]\"], [\n        isSnSearching,\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const initiateExportProcess = (exportIds)=>{\n        if (exportIds.length === 0) {\n            toast({\n                title: \"没有内容可导出\",\n                description: \"请选择至少一个数据块进行导出。\",\n                variant: \"default\"\n            });\n            return;\n        }\n        setExportProgress({\n            completed: 0,\n            total: exportIds.length\n        });\n        setGeneratedImages([]);\n        setExportQueue([\n            ...exportIds\n        ]);\n        setCurrentlyExportingBlockId(exportIds[0]);\n        toast({\n            title: \"导出已开始\",\n            description: \"准备导出 \".concat(exportIds.length, \" 个图表...\")\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (!currentlyExportingBlockId) return;\n            const processBlock = {\n                \"LogAnalysisPage.useEffect.processBlock\": async ()=>{\n                    await new Promise({\n                        \"LogAnalysisPage.useEffect.processBlock\": (resolve)=>setTimeout(resolve, 100)\n                    }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                    const container = exportTargetContainerRef.current;\n                    if (!container) {\n                        return;\n                    }\n                    const chartElement = container.querySelector('[data-block-id=\"'.concat(currentlyExportingBlockId, '\"]'));\n                    if (chartElement) {\n                        try {\n                            await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_11__.waitForChartReady)(chartElement);\n                            const blob = await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_11__.generateSingleImageBlob)(chartElement);\n                            setGeneratedImages({\n                                \"LogAnalysisPage.useEffect.processBlock\": (prev)=>[\n                                        ...prev,\n                                        {\n                                            filename: \"chart_\".concat(currentlyExportingBlockId, \".png\"),\n                                            blob\n                                        }\n                                    ]\n                            }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                        } catch (error) {\n                            toast({\n                                title: \"图表生成失败\",\n                                description: \"无法为数据块 \".concat(currentlyExportingBlockId, \" 生成图片。\"),\n                                variant: \"destructive\"\n                            });\n                        }\n                    }\n                    setExportQueue({\n                        \"LogAnalysisPage.useEffect.processBlock\": (prevQueue)=>{\n                            const newQueue = prevQueue.slice(1);\n                            setCurrentlyExportingBlockId(newQueue[0] || null);\n                            return newQueue;\n                        }\n                    }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                }\n            }[\"LogAnalysisPage.useEffect.processBlock\"];\n            processBlock();\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        currentlyExportingBlockId,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (exportProgress) {\n                setExportProgress({\n                    \"LogAnalysisPage.useEffect\": (prev)=>({\n                            ...prev,\n                            completed: generatedImages.length\n                        })\n                }[\"LogAnalysisPage.useEffect\"]);\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        generatedImages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (exportProgress && !currentlyExportingBlockId && exportQueue.length === 0) {\n                if (generatedImages.length > 0 && generatedImages.length === exportProgress.total) {\n                    const zipAndDownload = {\n                        \"LogAnalysisPage.useEffect.zipAndDownload\": async ()=>{\n                            try {\n                                await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_11__.zipAndDownloadImages)(generatedImages, 'exported_log_charts');\n                                toast({\n                                    title: \"导出成功\",\n                                    description: \"已将 \".concat(generatedImages.length, \" 个图表导出为压缩包。\")\n                                });\n                            } catch (error) {\n                                toast({\n                                    title: \"导出失败\",\n                                    description: \"无法创建或下载ZIP文件。\",\n                                    variant: \"destructive\"\n                                });\n                            } finally{\n                                setExportQueue([]);\n                                setCurrentlyExportingBlockId(null);\n                                setGeneratedImages([]);\n                                setExportProgress(null);\n                            }\n                        }\n                    }[\"LogAnalysisPage.useEffect.zipAndDownload\"];\n                    zipAndDownload();\n                } else if (exportProgress.total > 0) {\n                    toast({\n                        title: \"导出完成\",\n                        description: \"成功导出 \".concat(generatedImages.length, \" 个图表，\").concat(exportProgress.total - generatedImages.length, \" 个失败。\"),\n                        variant: \"default\"\n                    });\n                    setExportQueue([]);\n                    setCurrentlyExportingBlockId(null);\n                    setGeneratedImages([]);\n                    setExportProgress(null);\n                }\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        currentlyExportingBlockId,\n        exportProgress,\n        generatedImages,\n        exportQueue.length,\n        toast\n    ]);\n    const blockToRenderOffscreen = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[blockToRenderOffscreen]\": ()=>{\n            if (!currentlyExportingBlockId) return null;\n            return dataChunks.find({\n                \"LogAnalysisPage.useMemo[blockToRenderOffscreen]\": (b)=>b.block_id === currentlyExportingBlockId\n            }[\"LogAnalysisPage.useMemo[blockToRenderOffscreen]\"]) || null;\n        }\n    }[\"LogAnalysisPage.useMemo[blockToRenderOffscreen]\"], [\n        currentlyExportingBlockId,\n        dataChunks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"日志分析与查询\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-1 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                onProcessingStart: handleProcessingStart,\n                                onDataProcessed: handleDataProcessed,\n                                onError: handleError,\n                                disabled: isLoading || !!exportProgress || isImageNameSearching\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_SnSearchBar__WEBPACK_IMPORTED_MODULE_5__.SnSearchBar, {\n                                onSearch: handleSnSearch,\n                                onClear: handleClearSearch,\n                                isLoading: isLoading || isImageNameSearching\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 12\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_ImageNameSearch__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                onSearch: handleImageNameSearch,\n                                isLoading: isImageNameSearching,\n                                disabled: dataChunks.length === 0 || isLoading || !!exportProgress\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            dataChunks: displayedDataChunks,\n                            onSelectionChange: handleBlockSelectionChanged,\n                            onStartExport: initiateExportProcess,\n                            selectedBlockIds: selectedBlockIds,\n                            isSearching: isSnSearching\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this),\n            exportProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_12__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_12__.AlertTitle, {\n                        children: \"正在导出图表...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_12__.AlertDescription, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_13__.Progress, {\n                                    value: exportProgress.completed / exportProgress.total * 100,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\".concat(exportProgress.completed, \" / \").concat(exportProgress.total)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_BatchExportCSV__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    selectedBlocks: selectedBlocks\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow mt-4\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"正在处理文件...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                    className: \"h-full flex items-center justify-center bg-destructive/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-destructive font-semibold\",\n                                children: \"发生错误\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 11\n                }, this) : isSnSearching && displayedDataChunks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                '未找到与 \"',\n                                searchQuery,\n                                '\" 相关的日志块。'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 16\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 14\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 12\n                }, this) : chartDataForView.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    dataChunks: chartDataForView,\n                    selectedBlockIds: Array.from(selectedBlockIds),\n                    onBlockSelect: ()=>{},\n                    isHighlighted: isSnSearching\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: dataChunks.length > 0 ? \"请从左侧选择数据块以显示图表\" : \"请先上传日志文件\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: exportTargetContainerRef,\n                style: {\n                    position: 'absolute',\n                    left: '-9999px',\n                    top: '-9999px',\n                    width: '1200px',\n                    height: '800px'\n                },\n                children: blockToRenderOffscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    dataChunks: [\n                        blockToRenderOffscreen\n                    ],\n                    selectedBlockIds: [\n                        blockToRenderOffscreen.block_id\n                    ],\n                    onBlockSelect: ()=>{}\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_s(LogAnalysisPage, \"R1fGctfGnV+52OYaOD91BrHcg+Y=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = LogAnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"LogAnalysisPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx\n"));

/***/ })

});