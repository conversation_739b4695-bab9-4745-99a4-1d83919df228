"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx":
/*!***********************************************!*\
  !*** ./app/(dashboard)/log-analysis/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogAnalysisPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/log-analysis/LogDisplayArea */ \"(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\");\n/* harmony import */ var _components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/log-analysis/LogChartView */ \"(app-pages-browser)/./components/log-analysis/LogChartView.tsx\");\n/* harmony import */ var _components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/log-analysis/LogFileUpload */ \"(app-pages-browser)/./components/log-analysis/LogFileUpload.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_exportUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/exportUtils */ \"(app-pages-browser)/./lib/exportUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LogAnalysisPage() {\n    _s();\n    const [dataChunks, setDataChunks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedBlocksForChart, setSelectedBlocksForChart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExportingMode, setIsExportingMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [blocksToRenderForExport, setBlocksToRenderForExport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const exportTargetContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const handleProcessingStart = ()=>{\n        console.log('[LogAnalysisPage] handleProcessingStart - START');\n        setIsLoading(true);\n        setError(null);\n        setSelectedBlocksForChart([]);\n        console.log('[LogAnalysisPage] handleProcessingStart - END');\n    };\n    const handleDataProcessed = (workerData)=>{\n        console.log('[LogAnalysisPage] handleDataProcessed - START. Received data count:', workerData.length);\n        const processedData = workerData.map((block)=>({\n                ...block,\n                data: [] // Initialize empty data array, can be populated later if needed\n            }));\n        setDataChunks(processedData);\n        setIsLoading(false);\n        toast({\n            title: \"数据已加载\",\n            description: \"成功处理了 \".concat(workerData.length, \" 个数据块。\")\n        });\n        console.log('[LogAnalysisPage] handleDataProcessed - END');\n    };\n    const handleError = (errorMessage)=>{\n        console.log('[LogAnalysisPage] handleError - START. Received errorMessage:', errorMessage);\n        setError(errorMessage);\n        setIsLoading(false);\n        console.log('[LogAnalysisPage] handleError - END');\n    };\n    const handleBlockSelectionChanged = (selectedBlocks)=>{\n        console.log('[LogAnalysisPage] handleBlockSelectionChanged - START. Received selectedBlocks count:', selectedBlocks.length);\n        setSelectedBlocksForChart(selectedBlocks);\n        console.log('[LogAnalysisPage] handleBlockSelectionChanged - END');\n    };\n    const handleBlockSelect = (blockId)=>{\n        console.log('[LogAnalysisPage] handleBlockSelect - START. Received blockId:', blockId);\n        const block = dataChunks.find((b)=>b.block_id === blockId);\n        if (block) {\n            setSelectedBlocksForChart([\n                block\n            ]);\n        }\n        console.log('[LogAnalysisPage] handleBlockSelect - END');\n    };\n    const chartDataForView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[chartDataForView]\": ()=>{\n            console.log('[LogAnalysisPage] Recalculating chartDataForView. Selected blocks count:', selectedBlocksForChart.length);\n            return selectedBlocksForChart;\n        }\n    }[\"LogAnalysisPage.useMemo[chartDataForView]\"], [\n        selectedBlocksForChart\n    ]);\n    const initiateExportProcess = (exportIds)=>{\n        console.log('[LogAnalysisPage] initiateExportProcess - START. exportIds:', exportIds);\n        const blocksToExport = dataChunks.filter((block)=>exportIds.includes(block.block_id));\n        if (blocksToExport.length === 0) {\n            toast({\n                title: \"导出错误\",\n                description: \"没有找到要导出的数据块。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setBlocksToRenderForExport(blocksToExport);\n        setIsExportingMode(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (isExportingMode && blocksToRenderForExport.length > 0 && exportTargetContainerRef.current) {\n                const allSelectedIds = blocksToRenderForExport.map({\n                    \"LogAnalysisPage.useEffect.allSelectedIds\": (block)=>block.block_id\n                }[\"LogAnalysisPage.useEffect.allSelectedIds\"]);\n                console.log('[LogAnalysisPage] Starting ZIP export for block IDs:', allSelectedIds);\n                // 等待图表渲染完成后再开始导出\n                const startExport = {\n                    \"LogAnalysisPage.useEffect.startExport\": async ()=>{\n                        // 等待更长时间确保所有图表都渲染完成\n                        await new Promise({\n                            \"LogAnalysisPage.useEffect.startExport\": (resolve)=>setTimeout(resolve, 2000)\n                        }[\"LogAnalysisPage.useEffect.startExport\"]);\n                        // 检查所有要导出的元素是否都存在\n                        const container = exportTargetContainerRef.current;\n                        if (!container) {\n                            throw new Error('导出容器未找到');\n                        }\n                        const missingBlocks = [];\n                        for (const blockId of allSelectedIds){\n                            const element = container.querySelector('[data-block-id=\"'.concat(blockId, '\"]'));\n                            if (!element) {\n                                missingBlocks.push(blockId);\n                            }\n                        }\n                        if (missingBlocks.length > 0) {\n                            console.warn('[LogAnalysisPage] Missing blocks for export:', missingBlocks);\n                        }\n                        return (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_7__.exportElementAsImage)(container, \"exported_log_charts\", allSelectedIds, {\n                            \"LogAnalysisPage.useEffect.startExport\": (progress)=>{\n                                console.log(\"[LogAnalysisPage] Export progress: \".concat(progress.toFixed(2), \"%\"));\n                            }\n                        }[\"LogAnalysisPage.useEffect.startExport\"]);\n                    }\n                }[\"LogAnalysisPage.useEffect.startExport\"];\n                startExport().then({\n                    \"LogAnalysisPage.useEffect\": ()=>{\n                        toast({\n                            title: \"导出成功\",\n                            description: \"所有选中的图表已成功导出。\"\n                        });\n                    }\n                }[\"LogAnalysisPage.useEffect\"]).catch({\n                    \"LogAnalysisPage.useEffect\": (error)=>{\n                        console.error('[LogAnalysisPage] Export failed:', error);\n                        toast({\n                            title: \"导出失败\",\n                            description: error instanceof Error ? error.message : \"导出过程中发生错误。\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }[\"LogAnalysisPage.useEffect\"]).finally({\n                    \"LogAnalysisPage.useEffect\": ()=>{\n                        setIsExportingMode(false);\n                        setBlocksToRenderForExport([]);\n                    }\n                }[\"LogAnalysisPage.useEffect\"]);\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        isExportingMode,\n        blocksToRenderForExport,\n        dataChunks\n    ]);\n    console.log('[LogAnalysisPage] Rendering. isLoading:', isLoading, 'error:', error, 'dataChunks count:', dataChunks.length, 'selectedBlocksForChart count:', selectedBlocksForChart.length, 'chartDataForView count:', chartDataForView.length);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"日志分析\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onProcessingStart: handleProcessingStart,\n                            onDataProcessed: handleDataProcessed,\n                            onError: handleError,\n                            disabled: isLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            dataChunks: dataChunks,\n                            onSelectionChange: handleBlockSelectionChanged,\n                            onStartExport: initiateExportProcess\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow mt-4\",\n                children: [\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"正在处理文件...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"h-full flex items-center justify-center bg-destructive/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-destructive font-semibold\",\n                                    children: \"发生错误\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    !isLoading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: exportTargetContainerRef,\n                        className: \"h-full\",\n                        children: isExportingMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            dataChunks: blocksToRenderForExport,\n                            selectedBlockIds: blocksToRenderForExport.map((b)=>b.block_id),\n                            onBlockSelect: handleBlockSelect\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 15\n                        }, this) : chartDataForView.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            dataChunks: chartDataForView,\n                            selectedBlockIds: chartDataForView.map((b)=>b.block_id),\n                            onBlockSelect: handleBlockSelect\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: dataChunks.length > 0 ? \"请从左侧选择数据块以显示图表\" : \"请先上传日志文件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(LogAnalysisPage, \"MTEH1qUTRvyQTASQZTtlZZfcoEs=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = LogAnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"LogAnalysisPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx\n"));

/***/ })

});