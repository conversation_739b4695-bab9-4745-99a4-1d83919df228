(()=>{var e={};e.id=989,e.ids=[989],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39011:(e,t,r)=>{"use strict";r.d(t,{WI:()=>d,_K:()=>u});var i=r(6226),n=r(27910);let s=process.env.FTP_HOST||"",o=process.env.FTP_USER||"",a=process.env.FTP_PASSWORD||"",l=process.env.FTP_PORT?parseInt(process.env.FTP_PORT,10):21,c=process.env.FTP_PATH_PREFIX||"/";async function f(){let e=new i.Client;try{return await e.access({host:s,port:l,user:o,password:a,secure:!1}),console.log("FTP connected successfully for operation."),e}catch(t){throw console.error("FTP connection error for operation:",t),e.close(),Error("Failed to connect to FTP server for operation.")}}function h(e){let t=e.replace(/\\/g,"/");return c&&"/"!==c&&(t=`${c.replace(/\/$/,"")}/${t.replace(/^\//,"")}`),t.replace(/\/\//g,"/")}async function u(e=".",t,r=20){let i=await f(),n=h(e),s=[],o=RegExp(t,"i");async function a(e){let t;if(!(s.length>=r)){try{t=await i.list(e)}catch(t){console.error(`Error listing directory ${e} during search:`,t);return}for(let i of t){if(s.length>=r)break;let t=`${e}/${i.name}`.replace(/\/\//g,"/");i.isFile&&o.test(i.name)?s.push({type:"f",name:i.name,size:i.size,date:i.rawModifiedAt,path:t}):i.isDirectory&&"."!==i.name&&".."!==i.name&&await a(t)}}}try{await a(n)}catch(e){throw console.error("Error during recursive search:",e),Error("File search operation failed.")}finally{i.closed||i.close()}return s.slice(0,r)}async function d(e){let t=await f(),r=h(e),i=new n.PassThrough,s=[];try{let e=t.downloadTo(i,r),n=new Promise((e,t)=>{i.on("data",e=>{s.push(Buffer.isBuffer(e)?e:Buffer.from(e))}),i.on("end",()=>{e(Buffer.concat(s))}),i.on("error",e=>{console.error(`Error reading from PassThrough stream for file ${r}:`,e),t(Error(`Failed to read stream for file ${r}.`))})});return await e,await n}catch(e){throw console.error(`Error initiating or during download for file ${r}:`,e),i.destroy(e),Error(`Failed to download file ${r}. Details: ${e.message}`)}finally{t.closed||(t.close(),console.log(`FTP connection closed for ${r}`))}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65232:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>U,routeModule:()=>O,serverHooks:()=>z,workAsyncStorage:()=>F,workUnitAsyncStorage:()=>T});var i={};r.r(i),r.d(i,{POST:()=>b});var n=r(96559),s=r(48088),o=r(37719),a=r(32190),l=r(39011),c=r(82076),f=r.n(c),h=r(74075),u=r.n(h);function d(e){return(e<0&&(e=0),e>1&&(e=1),e<.25)?{r:0,g:Math.round(4*e*255),b:255}:e<.5?{r:0,g:255,b:Math.round(255*(1-(e-.25)*4))}:e<.75?{r:Math.round((e-.5)*1020),g:255,b:0}:{r:255,g:Math.round(255*(1-(e-.75)*4)),b:0}}var E=r(33873),p=r.n(E);let m=require("fs/promises");var g=r.n(m),w=r(21820),y=r.n(w),I=r(55511);let L={randomUUID:I.randomUUID},D=new Uint8Array(256),N=D.length,P=[];for(let e=0;e<256;++e)P.push((e+256).toString(16).slice(1));let C=function(e,t,r){if(L.randomUUID&&!t&&!e)return L.randomUUID();let i=(e=e||{}).random??e.rng?.()??(N>D.length-16&&((0,I.randomFillSync)(D),N=0),D.slice(N,N+=16));if(i.length<16)throw Error("Random bytes length must be >= 16");if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=i[e];return t}return function(e,t=0){return(P[e[t+0]]+P[e[t+1]]+P[e[t+2]]+P[e[t+3]]+"-"+P[e[t+4]]+P[e[t+5]]+"-"+P[e[t+6]]+P[e[t+7]]+"-"+P[e[t+8]]+P[e[t+9]]+"-"+P[e[t+10]]+P[e[t+11]]+P[e[t+12]]+P[e[t+13]]+P[e[t+14]]+P[e[t+15]]).toLowerCase()}(i)};class x extends Error{constructor(e,t=500){super(e),this.status=t,this.name="PreviewError"}}async function v(e){let t;let r=y().tmpdir(),i=p().join(r,`preview-${C()}.zip`);try{console.log(`[Preview API] Writing buffer to temporary zip file: ${i}`),await g().writeFile(i,e),console.log("[Preview API] Detected .zip file, decompressing from temporary file"),t=new(f()).async({file:i});let r=await t.entries(),n=Object.values(r).find(e=>!e.isDirectory&&e.name.toLowerCase().endsWith(".xyz"));if(!n)throw new x("No .xyz file found in the zip archive",400);return console.log(`[Preview API] Found .xyz file in zip: ${n.name}`),(await t.entryData(n.name)).toString("utf-8")}finally{t&&(await t.close(),console.log("[Preview API] Zip instance closed."));try{await g().unlink(i),console.log(`[Preview API] Deleted temporary zip file: ${i}`)}catch(e){console.error(`[Preview API] Failed to delete temporary zip file ${i}:`,e)}}}async function S(e){console.log(`[Preview API] Downloading: ${e}`);let t=await (0,l.WI)(e).catch(e=>{throw new x(`FTP Download Error: ${e.message}`,500)});if(console.log(`[Preview API] File downloaded, size: ${t.length} bytes`),t.length>0x3200000)throw new x("File size exceeds limit of 50MB",413);let r=p().extname(e).toLowerCase();switch(r){case".zip":return v(t);case".gz":return console.log("[Preview API] Detected .gz file, decompressing"),u().gunzipSync(t).toString("utf-8");case".xyz":return console.log("[Preview API] Detected .xyz file"),t.toString("utf-8");default:throw new x(`Unsupported file type: ${r}`,400)}}async function R(e){return new Promise(async(t,i)=>{try{let n;try{n=(await Promise.resolve().then(r.t.bind(r,88044,23))).createCanvas}catch(e){throw console.error("Failed to load 'canvas' module. It may not be installed or the environment is missing dependencies.",e),new x("Preview generation is unavailable on the server due to missing dependencies.",501)}let s=n(800,600),o=s.getContext("2d");o.fillStyle="rgb(30,30,30)",o.fillRect(0,0,800,600);let a=function(e){if(!e||0===e.length)return null;let t=e[0].x,r=e[0].x,i=e[0].y,n=e[0].y,s=e[0].z,o=e[0].z;for(let a of e)a.x<t&&(t=a.x),a.x>r&&(r=a.x),a.y<i&&(i=a.y),a.y>n&&(n=a.y),a.z<s&&(s=a.z),a.z>o&&(o=a.z);return{minX:t,maxX:r,minY:i,maxY:n,minZ:s,maxZ:o}}(e);if(!a)return i(new x("Could not calculate bounds for the point cloud.",400));let{minX:l,maxX:c,minY:f,maxY:h,minZ:u,maxZ:E}=a,p=c-l,m=h-f;if(p<=0||m<=0)return o.font="16px Arial",o.fillStyle="red",o.textAlign="center",o.fillText("Invalid point cloud dimensions.",360,300),t(s.toDataURL("image/png"));let g=.9*Math.min(720/p,600/m),w=(720-p*g)/2,y=(600-m*g)/2,I=E-u;for(let t of e){let e=w+(t.x-l)*g,r=y+(t.y-f)*g,i=I>1e-9?(t.z-u)/I:.5;i=Math.max(0,Math.min(1,i));let n=d(i);o.fillStyle=`rgb(${n.r},${n.g},${n.b})`,o.fillRect(e-1,r-1,3,3)}(function(e,{minZ:t,maxZ:r,chartWidth:i,canvasHeight:n}){let s=i+10,o=n-100,a=o/20;for(let t=0;t<20;t++){let r=d(1-t/19);e.fillStyle=`rgb(${r.r},${r.g},${r.b})`,e.fillRect(s,50+t*a,30,a)}e.strokeStyle="white",e.lineWidth=1,e.strokeRect(s,50,30,o),e.font="12px Arial",e.fillStyle="white",e.textAlign="left",e.fillText(r.toFixed(2),s+30+5,56),e.fillText(t.toFixed(2),s+30+5,50+o),e.fillText("Z",s+15-3,40)})(o,{minZ:u,maxZ:E,chartWidth:720,canvasHeight:600}),o.font="10px Arial",o.fillStyle="grey",o.fillText(`Points: ${e.length.toLocaleString()}`,10,590),console.log("[Preview API] Canvas rendering completed"),t(s.toDataURL("image/png"))}catch(e){console.error("[Preview API] Rendering error:",e),i(new x("Failed to render preview image.",500))}})}async function b(e){try{let{filePath:t}=await e.json();if(!t)throw new x("File path is required",400);console.log(`[Preview API] Request for filePath: ${t}`);let r=await S(t);console.log("[Preview API] Parsing XYZ data");let i=function(e,t){let r=[],i=e.trim().split("\n"),n=i.length,s=t?.maxPoints,o=s&&n>s?Math.floor(n/s):1;for(let e=0;e<n;e+=o){let t=i[e];if(!t)continue;let n=t.trim().split(/\s+/);if(3===n.length){let e=parseFloat(n[0]),t=parseFloat(n[1]),i=parseFloat(n[2]);isNaN(e)||isNaN(t)||isNaN(i)||r.push({x:e,y:t,z:i})}}return r}(r,{maxPoints:15e4});if(0===i.length)throw new x("No valid points found in the file.",400);console.log(`[Preview API] Parsed ${i.length} points (sampled)`),console.log("[Preview API] Starting canvas rendering");let n=R(i),s=await Promise.race([n,new Promise((e,t)=>setTimeout(()=>t(new x("Rendering timed out",504)),3e4))]);return console.log("[Preview API] Image generated successfully"),a.NextResponse.json({success:!0,imageData:s})}catch(r){console.error("[Preview API] Error:",r);let e=r instanceof x?r.status:500,t=r.message||"An unexpected error occurred.";return a.NextResponse.json({success:!1,error:t,message:t},{status:e})}}let O=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/surface-data/preview/route",pathname:"/api/surface-data/preview",filename:"route",bundlePath:"app/api/surface-data/preview/route"},resolvedPagePath:"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\api\\surface-data\\preview\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:F,workUnitAsyncStorage:T,serverHooks:z}=O;function U(){return(0,o.patchFetch)({workAsyncStorage:F,workUnitAsyncStorage:T})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},82076:(e,t,r)=>{let i=r(29021),n=r(28354),s=r(33873),o=r(94735),a=r(74075),l=r(27910),c={LOCHDR:30,LOCSIG:0x4034b50,LOCVER:4,LOCFLG:6,LOCHOW:8,LOCTIM:10,LOCCRC:14,LOCSIZ:18,LOCLEN:22,LOCNAM:26,LOCEXT:28,CENHDR:46,CENSIG:0x2014b50,CENVEM:4,CENVER:6,CENFLG:8,CENHOW:10,CENTIM:12,CENCRC:16,CENSIZ:20,CENLEN:24,CENNAM:28,CENEXT:30,CENCOM:32,CENDSK:34,CENATT:36,CENATX:38,CENOFF:42,ENDHDR:22,ENDSIG:0x6054b50,ENDSIGFIRST:80,ENDSUB:8,ENDTOT:10,ENDSIZ:12,ENDOFF:16,ENDCOM:20,MAXFILECOMMENT:65535,ENDL64HDR:20,ENDL64SIG:0x7064b50,ENDL64SIGFIRST:80,END64HDR:56,END64SIG:0x6064b50,END64SIGFIRST:80,END64SUB:24,END64TOT:32,END64SIZ:40,END64OFF:48,STORED:0,DEFLATED:8,ENHANCED_DEFLATED:9,FLG_ENTRY_ENC:1,ID_ZIP64:1,EF_ZIP64_OR_32:0xffffffff,EF_ZIP64_OR_16:65535},f=function(e){let t,r,n,l,f,h;let L=this,D=!1!==e.storeEntries?{}:null,N=e.file,P=e.nameEncoding?new TextDecoder(e.nameEncoding):null;function C(){i.fstat(t,(i,s)=>{if(i)return L.emit("error",i);r=s.size,n=Math.max(Math.min(n=e.chunkSize||Math.round(r/1e3),Math.min(131072,r)),Math.min(1024,r)),function(){let e=Math.min(c.ENDHDR+c.MAXFILECOMMENT,r);(l={win:new g(t),totalReadLength:e,minPos:r-e,lastPos:r,chunkSize:Math.min(1024,n),firstByte:c.ENDSIGFIRST,sig:c.ENDSIG,complete:v}).win.read(r-l.chunkSize,l.chunkSize,x)}()})}function x(e,t){if(e||!t)return L.emit("error",e||Error("Archive read error"));let r=l.lastPos,i=r-l.win.position,n=l.win.buffer,s=l.minPos;for(;--r>=s&&--i>=0;)if(n.length-i>=4&&n[i]===l.firstByte&&n.readUInt32LE(i)===l.sig){l.lastBufferPosition=i,l.lastBytesRead=t,l.complete();return}if(r===s||(l.lastPos=r+1,l.chunkSize*=2,r<=s))return L.emit("error",Error("Bad archive"));let o=Math.min(l.chunkSize,r-s);l.win.expandLeft(o,x)}function v(){let e=l.win.buffer,t=l.lastBufferPosition;try{(f=new u).read(e.slice(t,t+c.ENDHDR)),f.headerOffset=l.win.position+t,f.commentLength?L.comment=e.slice(t+c.ENDHDR,t+c.ENDHDR+f.commentLength).toString():L.comment=null,L.entriesCount=f.volumeEntries,L.centralDirectory=f,f.volumeEntries===c.EF_ZIP64_OR_16&&f.totalEntries===c.EF_ZIP64_OR_16||f.size===c.EF_ZIP64_OR_32||f.offset===c.EF_ZIP64_OR_32?function(){let e=c.ENDL64HDR;l.lastBufferPosition>e?(l.lastBufferPosition-=e,S()):(l={win:l.win,totalReadLength:e,minPos:l.win.position-e,lastPos:l.win.position,chunkSize:l.chunkSize,firstByte:c.ENDL64SIGFIRST,sig:c.ENDL64SIG,complete:S}).win.read(l.lastPos-l.chunkSize,l.chunkSize,x)}():(l={},b())}catch(e){L.emit("error",e)}}function S(){let e=l.win.buffer,t=new d;t.read(e.slice(l.lastBufferPosition,l.lastBufferPosition+c.ENDL64HDR));let i=r-t.headerOffset;(l={win:l.win,totalReadLength:i,minPos:t.headerOffset,lastPos:l.lastPos,chunkSize:l.chunkSize,firstByte:c.END64SIGFIRST,sig:c.END64SIG,complete:R}).win.read(r-l.chunkSize,l.chunkSize,x)}function R(){let e=l.win.buffer,t=new E;t.read(e.slice(l.lastBufferPosition,l.lastBufferPosition+c.END64HDR)),L.centralDirectory.volumeEntries=t.volumeEntries,L.centralDirectory.totalEntries=t.totalEntries,L.centralDirectory.size=t.size,L.centralDirectory.offset=t.offset,L.entriesCount=t.volumeEntries,l={},b()}function b(){(l={win:new g(t),pos:f.offset,chunkSize:n,entriesLeft:f.volumeEntries}).win.read(l.pos,Math.min(n,r-l.pos),O)}function O(t,r){if(t||!r)return L.emit("error",t||Error("Entries read error"));let i=l.pos-l.win.position,s=l.entry,o=l.win.buffer,a=o.length;try{for(;l.entriesLeft>0;){s||((s=new p).readHeader(o,i),s.headerOffset=l.win.position+i,l.entry=s,l.pos+=c.CENHDR,i+=c.CENHDR);let t=s.fnameLen+s.extraLen+s.comLen,r=t+(l.entriesLeft>1?c.CENHDR:0);if(a-i<r){l.win.moveRight(n,O,i),l.move=!0;return}s.read(o,i,P),e.skipEntryNameValidation||s.validateName(),D&&(D[s.name]=s),L.emit("entry",s),l.entry=s=null,l.entriesLeft--,l.pos+=t,i+=t}L.emit("ready")}catch(e){L.emit("error",e)}}function F(){if(!D)throw Error("storeEntries disabled")}function T(e){return e.offset+c.LOCHDR+e.fnameLen+e.extraLen}function z(e){return(8&e.flags)!=8}function U(e,r,n){L.stream(e,(s,o)=>{if(s)n(s);else{let s,a;o.on("error",e=>{a=e,s&&(o.unpipe(s),s.close(()=>{n(e)}))}),i.open(r,"w",(l,c)=>{if(l)return n(l);if(a){i.close(t,()=>{n(a)});return}(s=i.createWriteStream(r,{fd:c})).on("finish",()=>{L.emit("extract",e,r),a||n()}),o.pipe(s)})}})}function _(e,t,r,i,n){if(!r.length)return i(null,n);let o=r.shift(),a=s.join(e,o.name.replace(t,""));U(o,a,s=>{if(s)return i(s,n);_(e,t,r,i,n+1)})}e.fd?(t=e.fd,C()):i.open(N,"r",(e,r)=>{if(e)return L.emit("error",e);t=r,C()}),Object.defineProperty(this,"ready",{get:()=>!1}),this.entry=function(e){return F(),D[e]},this.entries=function(){return F(),D},this.stream=function(e,r){return this.openEntry(e,(e,i)=>{if(e)return r(e);let n=T(i),s=new w(t,n,i.compressedSize);if(i.method===c.STORED);else{if(i.method!==c.DEFLATED)return r(Error("Unknown compression method: "+i.method));s=s.pipe(a.createInflateRaw())}z(i)&&(s=s.pipe(new y(s,i.crc,i.size))),r(null,s)},!1)},this.entryDataSync=function(e){let r=null;if(this.openEntry(e,(t,i)=>{r=t,e=i},!0),r)throw r;let i=Buffer.alloc(e.compressedSize);if(new m(t,i,0,e.compressedSize,T(e),e=>{r=e}).read(!0),r)throw r;if(e.method===c.STORED);else if(e.method===c.DEFLATED||e.method===c.ENHANCED_DEFLATED)i=a.inflateRawSync(i);else throw Error("Unknown compression method: "+e.method);if(i.length!==e.size)throw Error("Invalid size");return z(e)&&new I(e.crc,e.size).data(i),i},this.openEntry=function(e,r,i){if("string"==typeof e&&(F(),!(e=D[e])))return r(Error("Entry not found"));if(!e.isFile)return r(Error("Entry is not file"));if(!t)return r(Error("Archive closed"));let n=Buffer.alloc(c.LOCHDR);new m(t,n,0,n.length,e.offset,t=>{let i;if(t)return r(t);try{e.readDataHeader(n),e.encrypted&&(i=Error("Entry encrypted"))}catch(e){i=e}r(i,e)}).read(i)},this.extract=function(e,t,r){let n=e||"";if("string"==typeof e&&((e=this.entry(e))?n=e.name:n.length&&"/"!==n[n.length-1]&&(n+="/")),!e||e.isDirectory){let e=[],o=[],a={};for(let t in D)if(Object.prototype.hasOwnProperty.call(D,t)&&0===t.lastIndexOf(n,0)){let r=t.replace(n,""),i=D[t];if(i.isFile&&(e.push(i),r=s.dirname(r)),r&&!a[r]&&"."!==r){a[r]=!0;let e=r.split("/").filter(e=>e);for(e.length&&o.push(e);e.length>1;){let t=(e=e.slice(0,e.length-1)).join("/");if(a[t]||"."===t)break;a[t]=!0,o.push(e)}}}o.sort((e,t)=>e.length-t.length),o.length?function e(t,r,n){if(!r.length)return n();let o=r.shift();o=s.join(t,s.join(...o)),i.mkdir(o,{recursive:!0},i=>{if(i&&"EEXIST"!==i.code)return n(i);e(t,r,n)})}(t,o,i=>{i?r(i):_(t,n,e,r,0)}):_(t,n,e,r,0)}else i.stat(t,(i,n)=>{n&&n.isDirectory()?U(e,s.join(t,s.basename(e.name)),r):U(e,t,r)})},this.close=function(e){h||!t?(h=!0,e&&e()):(h=!0,i.close(t,r=>{t=null,e&&e(r)}))};let A=o.EventEmitter.prototype.emit;this.emit=function(...e){if(!h)return A.call(this,...e)}};f.setFs=function(e){i=e},f.debugLog=(...e)=>{f.debug&&console.log(...e)},n.inherits(f,o.EventEmitter);let h=Symbol("zip");f.async=class extends o.EventEmitter{constructor(e){super();let t=new f(e);t.on("entry",e=>this.emit("entry",e)),t.on("extract",(e,t)=>this.emit("extract",e,t)),this[h]=new Promise((e,r)=>{t.on("ready",()=>{t.removeListener("error",r),e(t)}),t.on("error",r)})}get entriesCount(){return this[h].then(e=>e.entriesCount)}get comment(){return this[h].then(e=>e.comment)}async entry(e){return(await this[h]).entry(e)}async entries(){return(await this[h]).entries()}async stream(e){let t=await this[h];return new Promise((r,i)=>{t.stream(e,(e,t)=>{e?i(e):r(t)})})}async entryData(e){let t=await this.stream(e);return new Promise((e,r)=>{let i=[];t.on("data",e=>i.push(e)),t.on("end",()=>{e(Buffer.concat(i))}),t.on("error",e=>{t.removeAllListeners("end"),r(e)})})}async extract(e,t){let r=await this[h];return new Promise((i,n)=>{r.extract(e,t,(e,t)=>{e?n(e):i(t)})})}async close(){let e=await this[h];return new Promise((t,r)=>{e.close(e=>{e?r(e):t()})})}};class u{read(e){if(e.length!==c.ENDHDR||e.readUInt32LE(0)!==c.ENDSIG)throw Error("Invalid central directory");this.volumeEntries=e.readUInt16LE(c.ENDSUB),this.totalEntries=e.readUInt16LE(c.ENDTOT),this.size=e.readUInt32LE(c.ENDSIZ),this.offset=e.readUInt32LE(c.ENDOFF),this.commentLength=e.readUInt16LE(c.ENDCOM)}}class d{read(e){if(e.length!==c.ENDL64HDR||e.readUInt32LE(0)!==c.ENDL64SIG)throw Error("Invalid zip64 central directory locator");this.headerOffset=N(e,c.ENDSUB)}}class E{read(e){if(e.length!==c.END64HDR||e.readUInt32LE(0)!==c.END64SIG)throw Error("Invalid central directory");this.volumeEntries=N(e,c.END64SUB),this.totalEntries=N(e,c.END64TOT),this.size=N(e,c.END64SIZ),this.offset=N(e,c.END64OFF)}}class p{readHeader(e,t){if(e.length<t+c.CENHDR||e.readUInt32LE(t)!==c.CENSIG)throw Error("Invalid entry header");this.verMade=e.readUInt16LE(t+c.CENVEM),this.version=e.readUInt16LE(t+c.CENVER),this.flags=e.readUInt16LE(t+c.CENFLG),this.method=e.readUInt16LE(t+c.CENHOW);let r=e.readUInt16LE(t+c.CENTIM),i=e.readUInt16LE(t+c.CENTIM+2);this.time=L(r,i),this.crc=e.readUInt32LE(t+c.CENCRC),this.compressedSize=e.readUInt32LE(t+c.CENSIZ),this.size=e.readUInt32LE(t+c.CENLEN),this.fnameLen=e.readUInt16LE(t+c.CENNAM),this.extraLen=e.readUInt16LE(t+c.CENEXT),this.comLen=e.readUInt16LE(t+c.CENCOM),this.diskStart=e.readUInt16LE(t+c.CENDSK),this.inattr=e.readUInt16LE(t+c.CENATT),this.attr=e.readUInt32LE(t+c.CENATX),this.offset=e.readUInt32LE(t+c.CENOFF)}readDataHeader(e){if(e.readUInt32LE(0)!==c.LOCSIG)throw Error("Invalid local header");this.version=e.readUInt16LE(c.LOCVER),this.flags=e.readUInt16LE(c.LOCFLG),this.method=e.readUInt16LE(c.LOCHOW);let t=e.readUInt16LE(c.LOCTIM),r=e.readUInt16LE(c.LOCTIM+2);this.time=L(t,r),this.crc=e.readUInt32LE(c.LOCCRC)||this.crc;let i=e.readUInt32LE(c.LOCSIZ);i&&i!==c.EF_ZIP64_OR_32&&(this.compressedSize=i);let n=e.readUInt32LE(c.LOCLEN);n&&n!==c.EF_ZIP64_OR_32&&(this.size=n),this.fnameLen=e.readUInt16LE(c.LOCNAM),this.extraLen=e.readUInt16LE(c.LOCEXT)}read(e,t,r){let i=e.slice(t,t+=this.fnameLen);this.name=r?r.decode(new Uint8Array(i)):i.toString("utf8");let n=e[t-1];this.isDirectory=47===n||92===n,this.extraLen&&(this.readExtra(e,t),t+=this.extraLen),this.comment=this.comLen?e.slice(t,t+this.comLen).toString():null}validateName(){if(/\\|^\w+:|^\/|(^|\/)\.\.(\/|$)/.test(this.name))throw Error("Malicious entry: "+this.name)}readExtra(e,t){let r,i;let n=t+this.extraLen;for(;t<n;)r=e.readUInt16LE(t),t+=2,i=e.readUInt16LE(t),t+=2,c.ID_ZIP64===r&&this.parseZip64Extra(e,t,i),t+=i}parseZip64Extra(e,t,r){r>=8&&this.size===c.EF_ZIP64_OR_32&&(this.size=N(e,t),t+=8,r-=8),r>=8&&this.compressedSize===c.EF_ZIP64_OR_32&&(this.compressedSize=N(e,t),t+=8,r-=8),r>=8&&this.offset===c.EF_ZIP64_OR_32&&(this.offset=N(e,t),t+=8,r-=8),r>=4&&this.diskStart===c.EF_ZIP64_OR_16&&(this.diskStart=e.readUInt32LE(t))}get encrypted(){return(this.flags&c.FLG_ENTRY_ENC)===c.FLG_ENTRY_ENC}get isFile(){return!this.isDirectory}}class m{constructor(e,t,r,i,n,s){this.fd=e,this.buffer=t,this.offset=r,this.length=i,this.position=n,this.callback=s,this.bytesRead=0,this.waiting=!1}read(e){let t;if(f.debugLog("read",this.position,this.bytesRead,this.length,this.offset),this.waiting=!0,e){let r=0;try{r=i.readSync(this.fd,this.buffer,this.offset+this.bytesRead,this.length-this.bytesRead,this.position+this.bytesRead)}catch(e){t=e}this.readCallback(e,t,t?r:null)}else i.read(this.fd,this.buffer,this.offset+this.bytesRead,this.length-this.bytesRead,this.position+this.bytesRead,this.readCallback.bind(this,e))}readCallback(e,t,r){if("number"==typeof r&&(this.bytesRead+=r),t||!r||this.bytesRead===this.length)return this.waiting=!1,this.callback(t,this.bytesRead);this.read(e)}}class g{constructor(e){this.position=0,this.buffer=Buffer.alloc(0),this.fd=e,this.fsOp=null}checkOp(){if(this.fsOp&&this.fsOp.waiting)throw Error("Operation in progress")}read(e,t,r){this.checkOp(),this.buffer.length<t&&(this.buffer=Buffer.alloc(t)),this.position=e,this.fsOp=new m(this.fd,this.buffer,0,t,this.position,r).read()}expandLeft(e,t){this.checkOp(),this.buffer=Buffer.concat([Buffer.alloc(e),this.buffer]),this.position-=e,this.position<0&&(this.position=0),this.fsOp=new m(this.fd,this.buffer,0,e,this.position,t).read()}expandRight(e,t){this.checkOp();let r=this.buffer.length;this.buffer=Buffer.concat([this.buffer,Buffer.alloc(e)]),this.fsOp=new m(this.fd,this.buffer,r,e,this.position+r,t).read()}moveRight(e,t,r){this.checkOp(),r?this.buffer.copy(this.buffer,0,r):r=0,this.position+=r,this.fsOp=new m(this.fd,this.buffer,this.buffer.length-r,r,this.position+this.buffer.length-r,t).read()}}class w extends l.Readable{constructor(e,t,r){super(),this.fd=e,this.offset=t,this.length=r,this.pos=0,this.readCallback=this.readCallback.bind(this)}_read(e){let t=Buffer.alloc(Math.min(e,this.length-this.pos));t.length?i.read(this.fd,t,0,t.length,this.offset+this.pos,this.readCallback):this.push(null)}readCallback(e,t,r){this.pos+=t,e?(this.emit("error",e),this.push(null)):t?(t!==r.length&&(r=r.slice(0,t)),this.push(r)):this.push(null)}}class y extends l.Transform{constructor(e,t,r){super(),this.verify=new I(t,r),e.on("error",e=>{this.emit("error",e)})}_transform(e,t,r){let i;try{this.verify.data(e)}catch(e){i=e}r(i,e)}}class I{constructor(e,t){this.crc=e,this.size=t,this.state={crc:-1,size:0}}data(e){let t=I.getCrcTable(),r=this.state.crc,i=0,n=e.length;for(;--n>=0;)r=t[(r^e[i++])&255]^r>>>8;if(this.state.crc=r,this.state.size+=e.length,this.state.size>=this.size){let e=Buffer.alloc(4);if(e.writeInt32LE(0xffffffff&~this.state.crc,0),(r=e.readUInt32LE(0))!==this.crc)throw Error("Invalid CRC");if(this.state.size!==this.size)throw Error("Invalid size")}}static getCrcTable(){let e=I.crcTable;if(!e){I.crcTable=e=[];let t=Buffer.alloc(4);for(let r=0;r<256;r++){let i=r;for(let e=8;--e>=0;)(1&i)!=0?i=0xedb88320^i>>>1:i>>>=1;i<0&&(t.writeInt32LE(i,0),i=t.readUInt32LE(0)),e[r]=i}}return e}}function L(e,t){let r=D(e,16),i=D(t,16),n={h:parseInt(r.slice(0,5).join(""),2),m:parseInt(r.slice(5,11).join(""),2),s:2*parseInt(r.slice(11,16).join(""),2),Y:parseInt(i.slice(0,7).join(""),2)+1980,M:parseInt(i.slice(7,11).join(""),2),D:parseInt(i.slice(11,16).join(""),2)};return new Date([n.Y,n.M,n.D].join("-")+" "+[n.h,n.m,n.s].join(":")+" GMT+0").getTime()}function D(e,t){let r=(e>>>0).toString(2);for(;r.length<t;)r="0"+r;return r.split("")}function N(e,t){return 0x100000000*e.readUInt32LE(t+4)+e.readUInt32LE(t)}e.exports=f},88044:e=>{"use strict";e.exports=require("canvas")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[719,580,226],()=>r(65232));module.exports=i})();