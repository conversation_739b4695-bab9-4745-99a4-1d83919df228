"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx":
/*!***********************************************!*\
  !*** ./app/(dashboard)/log-analysis/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogAnalysisPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jschardet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jschardet */ \"(app-pages-browser)/./node_modules/jschardet/index.js\");\n/* harmony import */ var _components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/log-analysis/LogDisplayArea */ \"(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\");\n/* harmony import */ var _components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/log-analysis/LogChartView */ \"(app-pages-browser)/./components/log-analysis/LogChartView.tsx\");\n/* harmony import */ var _components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/log-analysis/LogFileUpload */ \"(app-pages-browser)/./components/log-analysis/LogFileUpload.tsx\");\n/* harmony import */ var _components_log_analysis_SnSearchBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/log-analysis/SnSearchBar */ \"(app-pages-browser)/./components/log-analysis/SnSearchBar.tsx\");\n/* harmony import */ var _components_log_analysis_ImageNameSearch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/log-analysis/ImageNameSearch */ \"(app-pages-browser)/./components/log-analysis/ImageNameSearch.tsx\");\n/* harmony import */ var _components_log_analysis_BatchExportCSV__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/log-analysis/BatchExportCSV */ \"(app-pages-browser)/./components/log-analysis/BatchExportCSV.tsx\");\n/* harmony import */ var _utils_snHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/snHelper */ \"(app-pages-browser)/./utils/snHelper.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_exportUtils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/exportUtils */ \"(app-pages-browser)/./lib/exportUtils.ts\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LogAnalysisPage() {\n    _s();\n    const [dataChunks, setDataChunks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Unified selection state\n    const [selectedBlockIds, setSelectedBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // SN Search states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSnSearching, setIsSnSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Image Name Search states\n    const [isImageNameSearching, setIsImageNameSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // --- Queued export states ---\n    const [exportQueue, setExportQueue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentlyExportingBlockId, setCurrentlyExportingBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedImages, setGeneratedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exportProgress, setExportProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const exportTargetContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const logParserWorker = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // --- Worker Initialization and Message Handling ---\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            logParserWorker.current = new Worker(__webpack_require__.tu(new URL(/* worker import */ __webpack_require__.p + __webpack_require__.u(\"_app-pages-browser_workers_logParser_worker_ts\"), __webpack_require__.b)));\n            logParserWorker.current.onmessage = ({\n                \"LogAnalysisPage.useEffect\": (event)=>{\n                    const { type, payload, error, allBlocks } = event.data; // Correctly destructure allBlocks\n                    switch(type){\n                        case 'PARSE_LOG_RESULT':\n                            setIsLoading(false);\n                            if (error) {\n                                handleError(error);\n                            } else {\n                                // Use allBlocks directly, which is the correct property name from the worker\n                                handleDataProcessed(allBlocks || []);\n                            }\n                            break;\n                        case 'MATCH_BY_TIMESTAMP_RESULT':\n                            setIsImageNameSearching(false);\n                            if (error) {\n                                toast({\n                                    title: \"图片名称搜索失败\",\n                                    description: error,\n                                    variant: \"destructive\"\n                                });\n                            } else {\n                                const { matchedBlockIds } = payload;\n                                setSelectedBlockIds({\n                                    \"LogAnalysisPage.useEffect\": (prevIds)=>new Set([\n                                            ...Array.from(prevIds),\n                                            ...matchedBlockIds\n                                        ])\n                                }[\"LogAnalysisPage.useEffect\"]);\n                                toast({\n                                    title: \"图片名称搜索完成\",\n                                    description: \"匹配到 \".concat(matchedBlockIds.length, \" 个新的数据块。\")\n                                });\n                            }\n                            break;\n                        default:\n                            break;\n                    }\n                }\n            })[\"LogAnalysisPage.useEffect\"];\n            return ({\n                \"LogAnalysisPage.useEffect\": ()=>{\n                    var _logParserWorker_current;\n                    (_logParserWorker_current = logParserWorker.current) === null || _logParserWorker_current === void 0 ? void 0 : _logParserWorker_current.terminate();\n                }\n            })[\"LogAnalysisPage.useEffect\"];\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        toast\n    ]);\n    const handleFilesSelected = async (files)=>{\n        if (!logParserWorker.current) {\n            handleError(\"日志解析器未初始化。\");\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSelectedBlockIds(new Set());\n        setDataChunks([]);\n        toast({\n            title: \"开始处理\",\n            description: \"正在处理 \".concat(files.length, \" 个文件...\")\n        });\n        try {\n            const readAndDecodeFile = (file)=>{\n                return new Promise(async (resolve, reject)=>{\n                    try {\n                        const arrayBuffer = await file.arrayBuffer();\n                        if (!arrayBuffer || arrayBuffer.byteLength === 0) {\n                            resolve(\"\");\n                            return;\n                        }\n                        const buffer = Buffer.from(new Uint8Array(arrayBuffer));\n                        const detectionResult = jschardet__WEBPACK_IMPORTED_MODULE_2__.detect(buffer);\n                        let encoding = 'utf-8';\n                        if (detectionResult && detectionResult.encoding) {\n                            const detectedEncoding = detectionResult.encoding.toUpperCase();\n                            if ([\n                                'GB2312',\n                                'GBK',\n                                'BIG5',\n                                'EUC-TW',\n                                'HZ-GB-2312'\n                            ].includes(detectedEncoding)) {\n                                encoding = 'gbk';\n                            } else if ([\n                                'UTF-8',\n                                'UTF-16LE',\n                                'UTF-16BE'\n                            ].includes(detectedEncoding)) {\n                                encoding = detectedEncoding.toLowerCase();\n                            }\n                        }\n                        try {\n                            const decoder = new TextDecoder(encoding, {\n                                fatal: true\n                            });\n                            resolve(decoder.decode(arrayBuffer));\n                        } catch (e) {\n                            const decoder = new TextDecoder('gbk', {\n                                fatal: false\n                            });\n                            resolve(decoder.decode(arrayBuffer));\n                        }\n                    } catch (e) {\n                        reject(e);\n                    }\n                });\n            };\n            const decodedContents = await Promise.all(files.map((file)=>readAndDecodeFile(file)));\n            const combinedContent = decodedContents.join('\\n');\n            if (combinedContent.trim().length === 0) {\n                handleError('所有选定文件均为空或读取失败。');\n                return;\n            }\n            const message = {\n                type: 'PARSE_LOG',\n                payload: combinedContent\n            };\n            logParserWorker.current.postMessage(message);\n        } catch (error) {\n            handleError(\"文件处理失败: \".concat(error.message, \".\"));\n        }\n    };\n    const handleDataProcessed = (workerData)=>{\n        const processedData = workerData.map((block)=>({\n                ...block,\n                data: []\n            }));\n        setDataChunks(processedData);\n        toast({\n            title: \"处理完成\",\n            description: \"日志文件已成功解析。\"\n        });\n    };\n    const handleError = (errorMessage)=>{\n        setError(errorMessage);\n        setIsLoading(false);\n        toast({\n            title: \"处理错误\",\n            description: errorMessage,\n            variant: \"destructive\"\n        });\n    };\n    const handleBlockSelectionChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogAnalysisPage.useCallback[handleBlockSelectionChanged]\": (selectedIds)=>{\n            setSelectedBlockIds(selectedIds);\n        }\n    }[\"LogAnalysisPage.useCallback[handleBlockSelectionChanged]\"], []);\n    const selectedBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[selectedBlocks]\": ()=>{\n            return dataChunks.filter({\n                \"LogAnalysisPage.useMemo[selectedBlocks]\": (block)=>selectedBlockIds.has(block.block_id)\n            }[\"LogAnalysisPage.useMemo[selectedBlocks]\"]);\n        }\n    }[\"LogAnalysisPage.useMemo[selectedBlocks]\"], [\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const chartDataForView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[chartDataForView]\": ()=>{\n            return selectedBlocks;\n        }\n    }[\"LogAnalysisPage.useMemo[chartDataForView]\"], [\n        selectedBlocks\n    ]);\n    const handleSnSearch = (query)=>{\n        setSearchQuery(query);\n        setIsSnSearching(true);\n        const snsToSearch = (0,_utils_snHelper__WEBPACK_IMPORTED_MODULE_9__.parseSnInput)(query).map((sn)=>sn.toUpperCase());\n        if (snsToSearch.length === 0) {\n            setSelectedBlockIds(new Set());\n            setIsSnSearching(false);\n            return;\n        }\n        const results = new Set();\n        dataChunks.forEach((block)=>{\n            const snFromId = block.block_id.split('_')[0].toUpperCase();\n            for (const sn of snsToSearch){\n                const isSnInSnsArray = Array.isArray(block.sns) && block.sns.some((blockSn)=>blockSn.toUpperCase() === sn);\n                if (snFromId === sn || isSnInSnsArray) {\n                    results.add(block.block_id);\n                    break;\n                }\n            }\n        });\n        setSelectedBlockIds(results);\n        toast({\n            title: \"SN搜索完成\",\n            description: \"找到 \".concat(results.size, \" 个相关数据块。\")\n        });\n    };\n    const handleClearSearch = ()=>{\n        setSearchQuery('');\n        setSelectedBlockIds(new Set());\n        setIsSnSearching(false);\n    };\n    const generateExportFilename = (block)=>{\n        if (!block) return \"unknown_block.png\";\n        // 1. Format Timestamp\n        let timestampPart = 'NODATE';\n        if (block.start_time) {\n            try {\n                const date = new Date(block.start_time.replace(',', '.'));\n                const y = date.getFullYear();\n                const m = (date.getMonth() + 1).toString().padStart(2, '0');\n                const d = date.getDate().toString().padStart(2, '0');\n                const h = date.getHours().toString().padStart(2, '0');\n                const min = date.getMinutes().toString().padStart(2, '0');\n                const s = date.getSeconds().toString().padStart(2, '0');\n                timestampPart = \"\".concat(y).concat(m).concat(d, \"_\").concat(h).concat(min).concat(s); // Corrected: Removed underscores between time components\n            } catch (e) {\n            // Keep 'NODATE' on parsing error\n            }\n        }\n        // 2. Find SN\n        let snPart = 'NOSN';\n        if (Array.isArray(block.sns) && block.sns.length > 0 && block.sns[0]) {\n            snPart = block.sns[0];\n        } else {\n            const snFromId = block.block_id.split('_')[0];\n            if (snFromId && snFromId.toLowerCase() !== 'block') {\n                snPart = snFromId;\n            }\n        }\n        return \"\".concat(timestampPart, \"GBSN\").concat(snPart, \".png\");\n    };\n    const handleImageNameSearch = (timestamps)=>{\n        if (!logParserWorker.current) {\n            toast({\n                title: \"错误\",\n                description: \"日志解析器未初始化。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (timestamps.length === 0) {\n            toast({\n                title: \"提示\",\n                description: \"没有从文件名中解析出有效的时间戳。\",\n                variant: \"default\"\n            });\n            return;\n        }\n        setIsImageNameSearching(true);\n        toast({\n            title: \"正在搜索...\",\n            description: \"根据 \".concat(timestamps.length, \" 个时间戳进行匹配。\")\n        });\n        const message = {\n            type: 'MATCH_BY_TIMESTAMP',\n            payload: {\n                timestamps\n            }\n        };\n        logParserWorker.current.postMessage(message);\n    };\n    const displayedDataChunks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[displayedDataChunks]\": ()=>{\n            if (!isSnSearching) {\n                return dataChunks;\n            }\n            return dataChunks.filter({\n                \"LogAnalysisPage.useMemo[displayedDataChunks]\": (chunk)=>selectedBlockIds.has(chunk.block_id)\n            }[\"LogAnalysisPage.useMemo[displayedDataChunks]\"]);\n        }\n    }[\"LogAnalysisPage.useMemo[displayedDataChunks]\"], [\n        isSnSearching,\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const initiateExportProcess = (exportIds)=>{\n        if (exportIds.length === 0) {\n            toast({\n                title: \"没有内容可导出\",\n                description: \"请选择至少一个数据块进行导出。\",\n                variant: \"default\"\n            });\n            return;\n        }\n        setExportProgress({\n            completed: 0,\n            total: exportIds.length\n        });\n        setGeneratedImages([]);\n        setExportQueue([\n            ...exportIds\n        ]);\n        setCurrentlyExportingBlockId(exportIds[0]);\n        toast({\n            title: \"导出已开始\",\n            description: \"准备导出 \".concat(exportIds.length, \" 个图表...\")\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (!currentlyExportingBlockId) return;\n            const processBlock = {\n                \"LogAnalysisPage.useEffect.processBlock\": async ()=>{\n                    await new Promise({\n                        \"LogAnalysisPage.useEffect.processBlock\": (resolve)=>setTimeout(resolve, 100)\n                    }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                    const container = exportTargetContainerRef.current;\n                    if (!container) {\n                        console.error(\"Export container not found.\");\n                        return;\n                    }\n                    const blockToExport = dataChunks.find({\n                        \"LogAnalysisPage.useEffect.processBlock.blockToExport\": (b)=>b.block_id === currentlyExportingBlockId\n                    }[\"LogAnalysisPage.useEffect.processBlock.blockToExport\"]);\n                    if (!blockToExport) {\n                        console.error(\"Block with ID \".concat(currentlyExportingBlockId, \" not found in dataChunks.\"));\n                        // Move to the next item in the queue even if the block is not found\n                        setExportQueue({\n                            \"LogAnalysisPage.useEffect.processBlock\": (prevQueue)=>{\n                                const newQueue = prevQueue.slice(1);\n                                setCurrentlyExportingBlockId(newQueue[0] || null);\n                                return newQueue;\n                            }\n                        }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                        return;\n                    }\n                    const chartElement = container.querySelector('[data-block-id=\"'.concat(currentlyExportingBlockId, '\"]'));\n                    if (chartElement) {\n                        try {\n                            await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_12__.waitForChartReady)(chartElement);\n                            const blob = await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_12__.generateSingleImageBlob)(chartElement);\n                            const filename = generateExportFilename(blockToExport);\n                            setGeneratedImages({\n                                \"LogAnalysisPage.useEffect.processBlock\": (prev)=>[\n                                        ...prev,\n                                        {\n                                            filename,\n                                            blob\n                                        }\n                                    ]\n                            }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                        } catch (error) {\n                            toast({\n                                title: \"图表生成失败\",\n                                description: \"无法为数据块 \".concat(currentlyExportingBlockId, \" 生成图片。\"),\n                                variant: \"destructive\"\n                            });\n                        }\n                    } else {\n                        console.warn(\"Chart element for block ID \".concat(currentlyExportingBlockId, \" not found in DOM.\"));\n                    }\n                    // Advance the queue\n                    setExportQueue({\n                        \"LogAnalysisPage.useEffect.processBlock\": (prevQueue)=>{\n                            const newQueue = prevQueue.slice(1);\n                            setCurrentlyExportingBlockId(newQueue[0] || null);\n                            return newQueue;\n                        }\n                    }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                }\n            }[\"LogAnalysisPage.useEffect.processBlock\"];\n            processBlock();\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        currentlyExportingBlockId,\n        dataChunks,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (exportProgress) {\n                setExportProgress({\n                    \"LogAnalysisPage.useEffect\": (prev)=>({\n                            ...prev,\n                            completed: generatedImages.length\n                        })\n                }[\"LogAnalysisPage.useEffect\"]);\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        generatedImages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (exportProgress && !currentlyExportingBlockId && exportQueue.length === 0) {\n                if (generatedImages.length > 0 && generatedImages.length === exportProgress.total) {\n                    const zipAndDownload = {\n                        \"LogAnalysisPage.useEffect.zipAndDownload\": async ()=>{\n                            try {\n                                await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_12__.zipAndDownloadImages)(generatedImages, 'exported_log_charts');\n                                toast({\n                                    title: \"导出成功\",\n                                    description: \"已将 \".concat(generatedImages.length, \" 个图表导出为压缩包。\")\n                                });\n                            } catch (error) {\n                                toast({\n                                    title: \"导出失败\",\n                                    description: \"无法创建或下载ZIP文件。\",\n                                    variant: \"destructive\"\n                                });\n                            } finally{\n                                setExportQueue([]);\n                                setCurrentlyExportingBlockId(null);\n                                setGeneratedImages([]);\n                                setExportProgress(null);\n                            }\n                        }\n                    }[\"LogAnalysisPage.useEffect.zipAndDownload\"];\n                    zipAndDownload();\n                } else if (exportProgress.total > 0) {\n                    toast({\n                        title: \"导出完成\",\n                        description: \"成功导出 \".concat(generatedImages.length, \" 个图表，\").concat(exportProgress.total - generatedImages.length, \" 个失败。\"),\n                        variant: \"default\"\n                    });\n                    setExportQueue([]);\n                    setCurrentlyExportingBlockId(null);\n                    setGeneratedImages([]);\n                    setExportProgress(null);\n                }\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        currentlyExportingBlockId,\n        exportProgress,\n        generatedImages,\n        exportQueue.length,\n        toast\n    ]);\n    const blockToRenderOffscreen = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[blockToRenderOffscreen]\": ()=>{\n            if (!currentlyExportingBlockId) return null;\n            return dataChunks.find({\n                \"LogAnalysisPage.useMemo[blockToRenderOffscreen]\": (b)=>b.block_id === currentlyExportingBlockId\n            }[\"LogAnalysisPage.useMemo[blockToRenderOffscreen]\"]) || null;\n        }\n    }[\"LogAnalysisPage.useMemo[blockToRenderOffscreen]\"], [\n        currentlyExportingBlockId,\n        dataChunks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"日志分析与查询\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-1 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onFilesSelected: handleFilesSelected,\n                                isProcessing: isLoading,\n                                disabled: isLoading || !!exportProgress || isImageNameSearching\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_SnSearchBar__WEBPACK_IMPORTED_MODULE_6__.SnSearchBar, {\n                                onSearch: handleSnSearch,\n                                onClear: handleClearSearch,\n                                isLoading: isLoading || isImageNameSearching\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 12\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_ImageNameSearch__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                onSearch: handleImageNameSearch,\n                                isLoading: isImageNameSearching,\n                                disabled: dataChunks.length === 0 || isLoading || !!exportProgress\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            dataChunks: displayedDataChunks,\n                            onSelectionChange: handleBlockSelectionChanged,\n                            onStartExport: initiateExportProcess,\n                            selectedBlockIds: selectedBlockIds,\n                            isSearching: isSnSearching\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            exportProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_13__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_13__.AlertTitle, {\n                        children: \"正在导出图表...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_13__.AlertDescription, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_14__.Progress, {\n                                    value: exportProgress.completed / exportProgress.total * 100,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\".concat(exportProgress.completed, \" / \").concat(exportProgress.total)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 429,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_BatchExportCSV__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    selectedBlocks: selectedBlocks\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow mt-4\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"正在处理文件...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"h-full flex items-center justify-center bg-destructive/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-destructive font-semibold\",\n                                children: \"发生错误\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 11\n                }, this) : isSnSearching && displayedDataChunks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                '未找到与 \"',\n                                searchQuery,\n                                '\" 相关的日志块。'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 16\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 14\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 12\n                }, this) : chartDataForView.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    dataChunks: chartDataForView,\n                    selectedBlockIds: Array.from(selectedBlockIds),\n                    onBlockSelect: ()=>{},\n                    isHighlighted: isSnSearching\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: dataChunks.length > 0 ? \"请从左侧选择数据块以显示图表\" : \"请先上传日志文件\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: exportTargetContainerRef,\n                style: {\n                    position: 'absolute',\n                    left: '-9999px',\n                    top: '-9999px',\n                    width: '1200px',\n                    height: '800px'\n                },\n                children: blockToRenderOffscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    dataChunks: [\n                        blockToRenderOffscreen\n                    ],\n                    selectedBlockIds: [\n                        blockToRenderOffscreen.block_id\n                    ],\n                    onBlockSelect: ()=>{}\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n        lineNumber: 396,\n        columnNumber: 5\n    }, this);\n}\n_s(LogAnalysisPage, \"R1fGctfGnV+52OYaOD91BrHcg+Y=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = LogAnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"LogAnalysisPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx\n"));

/***/ })

});