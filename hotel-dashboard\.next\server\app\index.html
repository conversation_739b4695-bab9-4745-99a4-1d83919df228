<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/1aced213878a5f57.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-a44ad871deb0cf4b.js"/><script src="/_next/static/chunks/4bd1b696-aa84a7bbb4f5d404.js" async=""></script><script src="/_next/static/chunks/684-cfcaba307fa72e65.js" async=""></script><script src="/_next/static/chunks/main-app-5d3d02ea74ef5e29.js" async=""></script><script src="/_next/static/chunks/650-7aea19605fb7597e.js" async=""></script><script src="/_next/static/chunks/739-812712b8ba97d08a.js" async=""></script><script src="/_next/static/chunks/855-afd203703717a347.js" async=""></script><script src="/_next/static/chunks/433-1bac7719f2f81af6.js" async=""></script><script src="/_next/static/chunks/127-1ab88834524c94cc.js" async=""></script><script src="/_next/static/chunks/116-f70d76e370f3e3dd.js" async=""></script><script src="/_next/static/chunks/548-8fe07d2fa4278991.js" async=""></script><script src="/_next/static/chunks/394-9a282e58e56d2015.js" async=""></script><script src="/_next/static/chunks/587-8fcdf730491ed1b3.js" async=""></script><script src="/_next/static/chunks/508-857f46bb9b55702d.js" async=""></script><script src="/_next/static/chunks/991-4f4365c2e88d530d.js" async=""></script><script src="/_next/static/chunks/app/page-cd70133e90b86f4f.js" async=""></script><title>v0 App</title><meta name="description" content="Created with v0"/><meta name="generator" content="v0.dev"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><div class="flex h-screen bg-gray-100"><div class="w-64 translate-x-0 bg-white border-r border-gray-200 flex flex-col"><div class="p-6 border-b border-gray-200"><h1 class="text-2xl font-semibold text-purple-600">XREAL TEST</h1></div><div class="flex-1 py-4 overflow-y-auto"><nav class="space-y-1 px-2"><button class="flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md text-blue-600 bg-blue-50 border-l-4 border-blue-600"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-no-axes-column-increasing mr-3 h-5 w-5"><line x1="12" x2="12" y1="20" y2="10"></line><line x1="18" x2="18" y1="20" y2="4"></line><line x1="6" x2="6" y1="20" y2="16"></line></svg>Dashboard</button><button class="flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text mr-3 h-5 w-5"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>胶合日志分析</button><button class="flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database mr-3 h-5 w-5"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg>面形数据查询</button><button class="flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-brush mr-3 h-5 w-5"><path d="m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08"></path><path d="M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08 1.1 2.49 2.02 4 2.02 2.2 0 4-1.8 4-4.04a3.01 3.01 0 0 0-3-3.02z"></path></svg>画图板</button><button class="flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database mr-3 h-5 w-5"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg>数据库查询</button><button class="flex items-center w-full px-4 py-3 text-sm font-medium rounded-r-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award mr-3 h-5 w-5"><path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"></path><circle cx="12" cy="8" r="6"></circle></svg>Try Premium Version</button></nav></div></div><div class="flex-1 flex flex-col overflow-hidden"><header class="bg-white border-b border-gray-200 flex items-center justify-between px-4 py-4 md:px-6"><div class="flex items-center"><h1 class="text-xl font-semibold text-gray-800">Dashboard</h1></div><div class="flex items-center space-x-4"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-10 w-10 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-5 w-5"><path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path></svg><span class="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground px-4 py-2 relative h-8 w-8 rounded-full" type="button" id="radix-«Raefb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">U</span></span></button></div></header><main class="flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50"><div class="flex justify-end mb-4"><p class="text-sm text-gray-600">Wed // July 26th, 2023</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-4 flex items-center"><div class="bg-blue-50 p-3 rounded-full mr-4"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"></path><path d="M12 5l7 7-7 7"></path></svg></div><div><p class="text-sm text-gray-500">Arrival <span class="text-xs">(This week)</span></p><div class="flex items-center"><h3 class="text-2xl font-bold mr-2">73</h3><span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded">+24%</span></div><p class="text-xs text-gray-500">Previous week: 35</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-4 flex items-center"><div class="bg-amber-50 p-3 rounded-full mr-4"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5"></path><path d="M12 19l-7-7 7-7"></path></svg></div><div><p class="text-sm text-gray-500">Departure <span class="text-xs">(This week)</span></p><div class="flex items-center"><h3 class="text-2xl font-bold mr-2">35</h3><span class="text-xs px-1.5 py-0.5 bg-red-100 text-red-600 rounded">-12%</span></div><p class="text-xs text-gray-500">Previous week: 97</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-4 flex items-center"><div class="bg-cyan-50 p-3 rounded-full mr-4"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-cyan-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg></div><div><p class="text-sm text-gray-500">Booking <span class="text-xs">(This week)</span></p><div class="flex items-center"><h3 class="text-2xl font-bold mr-2">237</h3><span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded">+31%</span></div><p class="text-xs text-gray-500">Previous week: 187</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="p-4"><p class="text-sm text-gray-500 mb-2">Today Activities</p><div class="flex justify-between mb-2"><div class="text-center"><div class="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1"><span>5</span></div><p class="text-xs">Room<br/>Available</p></div><div class="text-center"><div class="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1"><span>10</span></div><p class="text-xs">Room<br/>Blocked</p></div><div class="text-center"><div class="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-1"><span>15</span></div><p class="text-xs">Guest</p></div></div><div class="mt-4"><p class="text-xs text-gray-500">Total Revenue</p><p class="text-lg font-bold">Rs.35k</p></div></div></div></div><div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="space-y-1.5 flex flex-row items-center justify-between p-4 pb-2"><div class="tracking-tight text-base font-medium">Revenue</div><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md px-3 h-8 text-xs" type="button" id="radix-«R4lhmfb»" aria-haspopup="menu" aria-expanded="false" data-state="closed">this week <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-1 h-3 w-3"><path d="m6 9 6 6 6-6"></path></svg></button></div><div class="p-4 pt-0"><div class="h-[200px] w-full"><div class="recharts-responsive-container" style="width:100%;height:100%;min-width:0"></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="space-y-1.5 flex flex-row items-center justify-between p-4 pb-2"><div class="tracking-tight text-base font-medium">Guests</div><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md px-3 h-8 text-xs" type="button" id="radix-«R4phmfb»" aria-haspopup="menu" aria-expanded="false" data-state="closed">this week <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-1 h-3 w-3"><path d="m6 9 6 6 6-6"></path></svg></button></div><div class="p-4 pt-0"><div class="h-[200px] w-full"><div class="recharts-responsive-container" style="width:100%;height:100%;min-width:0"></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="space-y-1.5 flex flex-row items-center justify-between p-4 pb-2"><div class="tracking-tight text-base font-medium">Rooms</div><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md px-3 h-8 text-xs" type="button" id="radix-«R4thmfb»" aria-haspopup="menu" aria-expanded="false" data-state="closed">this week <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-1 h-3 w-3"><path d="m6 9 6 6 6-6"></path></svg></button></div><div class="p-4 pt-0"><div class="text-xs mb-2"><div class="flex items-center justify-between"><p>Total 50 Rooms</p><div class="flex items-center gap-4"><div class="flex items-center gap-1"><span class="h-2 w-2 rounded-full bg-blue-500"></span><span>Occupied</span></div><div class="flex items-center gap-1"><span class="h-2 w-2 rounded-full bg-green-500"></span><span>Booked</span></div><div class="flex items-center gap-1"><span class="h-2 w-2 rounded-full bg-amber-500"></span><span>Available</span></div></div></div></div><div class="h-[180px] w-full"><div class="recharts-responsive-container" style="width:100%;height:100%;min-width:0"></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm mb-6"><div class="flex flex-col space-y-1.5 p-4 pb-0"><div class="tracking-tight text-base font-medium">Todays Booking <span class="text-xs font-normal text-gray-500">(8 Guest today)</span></div></div><div class="p-4"><div dir="ltr" data-orientation="horizontal" class="w-full"><div role="tablist" aria-orientation="horizontal" class="inline-flex h-10 items-center text-muted-foreground mb-4 border-b w-full justify-start rounded-none bg-transparent p-0" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«Ra1mfb»-content-stays" data-state="active" id="radix-«Ra1mfb»-trigger-stays" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Stays</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«Ra1mfb»-content-packages" data-state="inactive" id="radix-«Ra1mfb»-trigger-packages" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Packages</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«Ra1mfb»-content-arrivals" data-state="inactive" id="radix-«Ra1mfb»-trigger-arrivals" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Arrivals</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«Ra1mfb»-content-departure" data-state="inactive" id="radix-«Ra1mfb»-trigger-departure" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-none border-b-2 border-transparent px-4 py-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Departure</button></div><div class="flex flex-col md:flex-row justify-between mb-4 gap-4"><div class="relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><input type="text" placeholder="Search guest by name or phone number or booking ID" class="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full md:w-[400px] text-sm"/></div><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-4 w-4 mr-2"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg>Add Booking</button></div><div class="overflow-x-auto"><div class="relative w-full overflow-auto"><table class="w-full caption-bottom text-sm"><thead class="[&amp;_tr]:border-b"><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 whitespace-nowrap"><div class="flex items-center">NAME <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 ml-1"><path d="m6 9 6 6 6-6"></path></svg></div></th><th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 whitespace-nowrap">BOOKING ID</th><th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 whitespace-nowrap">NIGHTS</th><th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 whitespace-nowrap">ROOM TYPE</th><th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 whitespace-nowrap">GUESTS</th><th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 whitespace-nowrap">PAID</th><th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 whitespace-nowrap">COST</th><th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0 whitespace-nowrap">ACTION</th></tr></thead><tbody class="[&amp;_tr:last-child]:border-0"><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div class="flex items-center"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 mr-3"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">RK</span></span><div><p class="font-medium">Ram Kailash</p><p class="text-xs text-gray-500">9905598912</p></div></div></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">SDK89635</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">2</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">1 King Room</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">2<!-- --> Guests</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">rsp.150</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">rsp.1500</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div class="flex space-x-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen h-4 w-4"><path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg></button></div></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div class="flex items-center"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 mr-3"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">SK</span></span><div><p class="font-medium">Samira Karki</p><p class="text-xs text-gray-500">9815394203</p></div></div></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">SDK89635</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">4</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div><p>1 Queen</p><p>1 King Room</p></div></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">5<!-- --> Guests</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><span class="px-2 py-1 bg-green-100 text-green-600 rounded text-xs">paid</span></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">rsp.5500</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div class="flex space-x-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen h-4 w-4"><path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg></button></div></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div class="flex items-center"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 mr-3"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">JR</span></span><div><p class="font-medium">Jeevan Rai</p><p class="text-xs text-gray-500">9865328452</p></div></div></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">SDK89635</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">1</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div><p>1 Deluxe</p><p>1 King Room</p></div></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">3<!-- --> Guests</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">rsp.150</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">rsp.2500</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div class="flex space-x-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen h-4 w-4"><path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg></button></div></td></tr><tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div class="flex items-center"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 mr-3"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">BS</span></span><div><p class="font-medium">Bindu Sharma</p><p class="text-xs text-gray-500">9845653124</p></div></div></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">SDK89635</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">3</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div><p>1 Deluxe</p><p>1 King Room</p></div></td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">2<!-- --> Guests</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">rsp.150</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">rsp.3000</td><td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><div class="flex space-x-2"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen h-4 w-4"><path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg></button></div></td></tr></tbody></table></div></div><div class="flex justify-end mt-4"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 underline-offset-4 hover:underline h-10 px-4 py-2 text-blue-500 hover:text-blue-600">See other Bookings</button></div></div></div></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-4 pb-0"><div class="tracking-tight text-base font-medium">Calender</div></div><div class="p-4"><div class="flex items-center justify-between mb-4"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left h-4 w-4"><path d="m15 18-6-6 6-6"></path></svg></button><h3 class="text-sm font-medium">August 2023</h3><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4"><path d="m9 18 6-6-6-6"></path></svg></button></div><div class="grid grid-cols-7 gap-1 text-center text-xs"><div class="py-1 font-medium">SU</div><div class="py-1 font-medium">MO</div><div class="py-1 font-medium">TU</div><div class="py-1 font-medium">WE</div><div class="py-1 font-medium">TH</div><div class="py-1 font-medium">FR</div><div class="py-1 font-medium">SA</div><div class="py-1 text-gray-400">31</div><div class="py-1">1</div><div class="py-1 relative">2<span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></span></div><div class="py-1">3</div><div class="py-1">4</div><div class="py-1">5</div><div class="py-1">6</div><div class="py-1">7</div><div class="py-1">8</div><div class="py-1 relative">9<span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></span></div><div class="py-1">10</div><div class="py-1">11</div><div class="py-1">12</div><div class="py-1">13</div><div class="py-1">14</div><div class="py-1">15</div><div class="py-1">16</div><div class="py-1">17</div><div class="py-1">18</div><div class="py-1">19</div><div class="py-1">20</div><div class="py-1">21</div><div class="py-1">22</div><div class="py-1">23</div><div class="py-1 relative">24<span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></span></div><div class="py-1">25</div><div class="py-1">26</div><div class="py-1">27</div><div class="py-1">28</div><div class="py-1">29</div><div class="py-1">30</div><div class="py-1">31</div><div class="py-1 text-gray-400">1</div><div class="py-1 text-gray-400">2</div><div class="py-1 text-gray-400">3</div></div><div class="mt-6 border rounded-md p-3"><h4 class="text-sm font-medium mb-2">August 02, 2023 Booking Lists</h4><p class="text-xs text-gray-500 mb-3">(3 Bookings)</p><div class="space-y-3"><div class="flex items-center"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 mr-3"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">CLI</span></span><div><p class="text-sm font-medium">Carl Larson II</p><p class="text-xs text-gray-500">2<!-- --> Nights | <!-- -->2<!-- --> Guests</p></div></div><div class="flex items-center"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 mr-3"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">MEM</span></span><div><p class="text-sm font-medium">Mrs. Emmett Morar</p><p class="text-xs text-gray-500">2<!-- --> Nights | <!-- -->2<!-- --> Guests</p></div></div><div class="flex items-center"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8 mr-3"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">MK</span></span><div><p class="text-sm font-medium">Marjorie Klocko</p><p class="text-xs text-gray-500">2<!-- --> Nights | <!-- -->2<!-- --> Guests</p></div></div></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="space-y-1.5 flex flex-row items-center justify-between p-4 pb-0"><div class="tracking-tight text-base font-medium">Overall Rating</div><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md px-3 h-8 text-xs" type="button" id="radix-«R4qhmfb»" aria-haspopup="menu" aria-expanded="false" data-state="closed">This Week <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down ml-1 h-3 w-3"><path d="m6 9 6 6 6-6"></path></svg></button></div><div class="p-4"><div class="flex justify-center mb-6"><div class="relative w-48 h-24"><svg viewBox="0 0 100 50" class="w-full h-full"><path d="M 0 50 A 50 50 0 0 1 100 50" fill="none" stroke="#e5e7eb" stroke-width="10"></path><path d="M 0 50 A 50 50 0 0 1 90 50" fill="none" stroke="#3b82f6" stroke-width="10"></path></svg><div class="absolute inset-0 flex flex-col items-center justify-center"><div class="text-center"><p class="text-sm font-medium">Rating</p><p class="text-2xl font-bold">4.5/5</p><span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-600 rounded">+31%</span></div></div></div></div><div class="space-y-3"><div class="flex items-center justify-between"><span class="text-sm">Cleanliness</span><div class="flex items-center gap-2"><div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" class="relative overflow-hidden rounded-full bg-gray-200 h-2 w-32"><div data-state="indeterminate" data-max="100" class="h-full w-full flex-1 bg-blue-500 transition-all" style="transform:translateX(-10%)"></div></div><span class="text-sm">4.5</span></div></div><div class="flex items-center justify-between"><span class="text-sm">Facilities</span><div class="flex items-center gap-2"><div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" class="relative overflow-hidden rounded-full bg-gray-200 h-2 w-32"><div data-state="indeterminate" data-max="100" class="h-full w-full flex-1 bg-blue-500 transition-all" style="transform:translateX(-10%)"></div></div><span class="text-sm">4.5</span></div></div><div class="flex items-center justify-between"><span class="text-sm">Location</span><div class="flex items-center gap-2"><div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" class="relative overflow-hidden rounded-full bg-gray-200 h-2 w-32"><div data-state="indeterminate" data-max="100" class="h-full w-full flex-1 bg-blue-500 transition-all" style="transform:translateX(-50%)"></div></div><span class="text-sm">2.5</span></div></div><div class="flex items-center justify-between"><span class="text-sm">Room Comfort</span><div class="flex items-center gap-2"><div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" class="relative overflow-hidden rounded-full bg-gray-200 h-2 w-32"><div data-state="indeterminate" data-max="100" class="h-full w-full flex-1 bg-blue-500 transition-all" style="transform:translateX(-50%)"></div></div><span class="text-sm">2.5</span></div></div><div class="flex items-center justify-between"><span class="text-sm">Service</span><div class="flex items-center gap-2"><div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" class="relative overflow-hidden rounded-full bg-gray-200 h-2 w-32"><div data-state="indeterminate" data-max="100" class="h-full w-full flex-1 bg-blue-500 transition-all" style="transform:translateX(-24%)"></div></div><span class="text-sm">3.8</span></div></div><div class="flex items-center justify-between"><span class="text-sm">Value for money</span><div class="flex items-center gap-2"><div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100" class="relative overflow-hidden rounded-full bg-gray-200 h-2 w-32"><div data-state="indeterminate" data-max="100" class="h-full w-full flex-1 bg-blue-500 transition-all" style="transform:translateX(-24%)"></div></div><span class="text-sm">3.8</span></div></div></div></div></div></div> <!-- --> <!-- --> <!-- --> </main></div></div><script src="/_next/static/chunks/webpack-a44ad871deb0cf4b.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[87555,[],\"\"]\n3:I[31295,[],\"\"]\n4:I[57022,[\"650\",\"static/chunks/650-7aea19605fb7597e.js\",\"739\",\"static/chunks/739-812712b8ba97d08a.js\",\"855\",\"static/chunks/855-afd203703717a347.js\",\"433\",\"static/chunks/433-1bac7719f2f81af6.js\",\"127\",\"static/chunks/127-1ab88834524c94cc.js\",\"116\",\"static/chunks/116-f70d76e370f3e3dd.js\",\"548\",\"static/chunks/548-8fe07d2fa4278991.js\",\"394\",\"static/chunks/394-9a282e58e56d2015.js\",\"587\",\"static/chunks/587-8fcdf730491ed1b3.js\",\"508\",\"static/chunks/508-857f46bb9b55702d.js\",\"991\",\"static/chunks/991-4f4365c2e88d530d.js\",\"974\",\"static/chunks/app/page-cd70133e90b86f4f.js\"],\"default\"]\n5:I[59665,[],\"OutletBoundary\"]\n8:I[59665,[],\"ViewportBoundary\"]\na:I[59665,[],\"MetadataBoundary\"]\nc:I[26614,[],\"\"]\n:HL[\"/_next/static/css/1aced213878a5f57.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"XM5Ur-4WDRzDAz6myWE0H\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1aced213878a5f57.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{}],\"$undefined\",null,[\"$\",\"$L5\",null,{\"children\":[\"$L6\",\"$L7\",null]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"KXGctmP9316fPOmuLriLk\",{\"children\":[[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null]}],[\"$\",\"$La\",null,{\"children\":\"$Lb\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$c\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"9:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n6:null\n"])</script><script>self.__next_f.push([1,"7:null\nb:[[\"$\",\"title\",\"0\",{\"children\":\"v0 App\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Created with v0\"}],[\"$\",\"meta\",\"2\",{\"name\":\"generator\",\"content\":\"v0.dev\"}]]\n"])</script></body></html>