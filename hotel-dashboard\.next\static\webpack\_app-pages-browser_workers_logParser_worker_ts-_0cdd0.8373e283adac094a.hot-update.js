"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts-_0cdd0",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {\n    console.warn('[Worker] logParser.worker.ts loaded in non-worker context (or main thread). Exports are available, but worker-specific code will not run.');\n} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    const BLOCK_REGEX_V1 = /打开真空泵(?:(?!打开真空泵)[\\s\\S])*?insert into g_support/g;\n    // --- V2 正则表达式 ---\n    const BLOCK_REGEX_V2 = /轴停止运动(?:(?!轴停止运动)[\\s\\S])*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function detectLogVersion(logContent) {\n        if (logContent.includes(\"轴停止运动\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V2');\n            return \"V2\";\n        }\n        if (logContent.includes(\"打开真空泵\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V1');\n            return \"V1\";\n        }\n        if (DEBUG) console.warn('[Worker] Could not determine log version.');\n        return \"UNKNOWN\";\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const version = detectLogVersion(logContent);\n        if (version === \"UNKNOWN\") {\n            throw new Error('Unknown log file version. Could not parse.');\n        }\n        const blockRegex = version === \"V1\" ? BLOCK_REGEX_V1 : BLOCK_REGEX_V2;\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        let match;\n        let lastIndex = 0;\n        let iterationCount = 0;\n        const MAX_ITERATIONS = 100000; // Safety break to prevent infinite loops\n        // Manually iterate with exec() to have more control and prevent crashes\n        while((match = blockRegex.exec(logContent)) !== null){\n            iterationCount++;\n            if (iterationCount > MAX_ITERATIONS) {\n                console.error('[Worker] Infinite loop detected in block matching. Aborting.');\n                self.postMessage({\n                    type: 'PARSE_LOG_RESULT',\n                    success: false,\n                    error: 'Infinite loop detected during log parsing. Check log format and block delimiters.'\n                });\n                return []; // Stop processing\n            }\n            // This check prevents infinite loops on zero-length matches\n            if (match.index === blockRegex.lastIndex) {\n                blockRegex.lastIndex++;\n            }\n            const blockContent = match[0];\n            if (!blockContent || typeof match.index === 'undefined') continue;\n            let sn = null;\n            // 1. Try to find SN inside the block first\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n                if (DEBUG) console.log(\"[Worker] Found SN inside block \".concat(iterationCount, \": \").concat(sn));\n            } else {\n                // 2. If not found, search for the next SN *after* this block\n                const endOfCurrentBlock = match.index + blockContent.length;\n                const searchArea = logContent.substring(endOfCurrentBlock); // Search from end of block to end of file\n                const snMatchOutside = snRegex.exec(searchArea);\n                if (snMatchOutside) {\n                    // To avoid altering the main loop's regex state, create a new regex for peeking.\n                    const peekRegex = new RegExp(blockRegex.source, blockRegex.flags);\n                    peekRegex.lastIndex = endOfCurrentBlock; // Start peeking from the end of the current block\n                    const nextBlockMatch = peekRegex.exec(logContent);\n                    // Check if the found SN is located *before* the start of the next block.\n                    if (!nextBlockMatch || endOfCurrentBlock + snMatchOutside.index < nextBlockMatch.index) {\n                        sn = snMatchOutside[1];\n                        if (DEBUG) console.log(\"[Worker] Found SN outside block \".concat(iterationCount, \": \").concat(sn));\n                    } else {\n                        // The SN found belongs to a later block, so ignore it for this one.\n                        if (DEBUG) console.log(\"[Worker] SN found outside block \".concat(iterationCount, \", but it belongs to a subsequent block. Ignoring.\"));\n                    }\n                }\n            }\n            // Ensure lastIndex is correctly updated after every iteration to prevent infinite loops.\n            blockRegex.lastIndex = match.index + blockContent.length;\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(iterationCount) : \"block_\".concat(iterationCount);\n            if (DEBUG) console.log(\"[Worker] Final Block ID for index \".concat(iterationCount - 1, \": \").concat(blockId));\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0) {\n                    localProcessedBlocks.push(processedBlock);\n                }\n            }\n            lastIndex = blockRegex.lastIndex;\n        }\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the parsed blocks globally in the worker\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"打开真空泵\";\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        for (const block of processedBlocks){\n                            // Avoid re-checking a block that already has a match\n                            if (matchedBlockIds.has(block.block_id)) {\n                                continue;\n                            }\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            // Timestamps are in 'YYYY-MM-DD HH:mm:ss,SSS' format\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {\n                                        // Ignore lines with invalid date formats\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {\n        console.error('[Worker] Uncaught error in worker script:', errorEvent);\n    };\n    console.log('[Worker] logParser.worker.ts script loaded and event listener for \"message\" set up.');\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("4886dd1fc7549d35")
/******/ })();
/******/ 
/******/ }
);