"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx":
/*!****************************************************!*\
  !*** ./components/log-analysis/LogDisplayArea.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// LogChartView is removed as it will be handled by the parent page\n\n\n\n\n\n\n\nconst LogDisplayArea = (param)=>{\n    let { dataChunks, onSelectionChange, onStartExport } = param;\n    _s();\n    console.log('[LogDisplayArea] Rendering. ProcessedDataChunks count:', dataChunks.length);\n    const [selectedBlockId, setSelectedBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''); // 单选显示\n    const [exportBlockIds, setExportBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 多选导出\n    const displayAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const onSelectionChangeRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(onSelectionChange);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            onSelectionChangeRef.current = onSelectionChange;\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        onSelectionChange\n    ]);\n    // 当有新的数据块时，自动选择第一个\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            console.log('[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:', dataChunks.length, 'current selectedBlockId:', selectedBlockId);\n            if (dataChunks.length === 0) {\n                if (selectedBlockId !== '') {\n                    console.log('[LogDisplayArea] No data chunks, clearing selectedBlockId.');\n                    setSelectedBlockId('');\n                }\n            } else {\n                const currentSelectionIsValid = selectedBlockId && selectedBlockId.trim() !== '' && dataChunks.some({\n                    \"LogDisplayArea.useEffect\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect\"]);\n                if (!currentSelectionIsValid) {\n                    console.log('[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.');\n                    const firstValidBlock = dataChunks.find({\n                        \"LogDisplayArea.useEffect.firstValidBlock\": (chunk)=>chunk.block_id && typeof chunk.block_id === 'string' && chunk.block_id.trim() !== ''\n                    }[\"LogDisplayArea.useEffect.firstValidBlock\"]);\n                    if (firstValidBlock) {\n                        console.log('[LogDisplayArea] Found first valid block. Setting selectedBlockId to:', firstValidBlock.block_id);\n                        setSelectedBlockId(firstValidBlock.block_id);\n                    } else {\n                        if (selectedBlockId !== '') {\n                            console.warn('[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId.');\n                            setSelectedBlockId('');\n                        }\n                    }\n                } else {\n                    console.log('[LogDisplayArea] Current selection is still valid:', selectedBlockId);\n                }\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        dataChunks,\n        selectedBlockId\n    ]); // Keep selectedBlockId in dependencies to re-evaluate if it changes externally or becomes invalid\n    // 当显示选择改变时，通知父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            if (selectedBlockId && dataChunks.length > 0) {\n                const selected = dataChunks.filter({\n                    \"LogDisplayArea.useEffect.selected\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect.selected\"]);\n                onSelectionChangeRef.current(selected); // 传递筛选出的块\n            } else {\n                onSelectionChangeRef.current([]); // 如果没有选中的ID或没有数据块，传递空数组\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        selectedBlockId,\n        dataChunks\n    ]);\n    const handleBlockSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleBlockSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleBlockSelectionChange - START. blockId:', blockId);\n            setSelectedBlockId(blockId);\n            console.log('[LogDisplayArea] handleBlockSelectionChange - END.');\n        }\n    }[\"LogDisplayArea.useCallback[handleBlockSelectionChange]\"], []);\n    const handleExportSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleExportSelectionChange - START. blockId:', blockId);\n            setExportBlockIds({\n                \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (prevSelected)=>{\n                    const newSelection = prevSelected.includes(blockId) ? prevSelected.filter({\n                        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (id)=>id !== blockId\n                    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]) : [\n                        ...prevSelected,\n                        blockId\n                    ];\n                    console.log('[LogDisplayArea] handleExportSelectionChange - New selection:', newSelection);\n                    return newSelection;\n                }\n            }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]);\n        }\n    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"], []);\n    const selectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[selectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] selectAllForExport - START');\n            const allIds = dataChunks.map({\n                \"LogDisplayArea.useCallback[selectAllForExport].allIds\": (chunk)=>chunk.block_id\n            }[\"LogDisplayArea.useCallback[selectAllForExport].allIds\"]);\n            setExportBlockIds(allIds);\n            console.log('[LogDisplayArea] selectAllForExport - END. Selected all IDs:', allIds);\n        }\n    }[\"LogDisplayArea.useCallback[selectAllForExport]\"], [\n        dataChunks\n    ]);\n    const deselectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[deselectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] deselectAllForExport - START');\n            setExportBlockIds([]);\n            console.log('[LogDisplayArea] deselectAllForExport - END');\n        }\n    }[\"LogDisplayArea.useCallback[deselectAllForExport]\"], []);\n    const handleExportAllImages = async ()=>{\n        console.log('handleExportAllImages called');\n        console.log('exportBlockIds:', exportBlockIds);\n        if (!dataChunks || dataChunks.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '没有可导出的内容。'\n            });\n            return;\n        }\n        if (exportBlockIds.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '请至少选择一个数据块进行导出。'\n            });\n            return;\n        }\n        // 使用主页面的导出模式，这样可以正确渲染所有选中的图表\n        console.log('[LogDisplayArea] Triggering export via onStartExport callback');\n        onStartExport(exportBlockIds);\n    };\n    const hasContentToExport = dataChunks && dataChunks.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"h-[450px] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"选择数据块进行分析\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"从解析的日志文件中选择一个数据块以在图表中显示。\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleExportAllImages,\n                        disabled: isExportingAll || !hasContentToExport || exportBlockIds.length === 0,\n                        size: \"sm\",\n                        children: isExportingAll ? '导出中...' : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"导出选中图片\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: displayAreaRef,\n                    className: \"h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: selectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: deselectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"取消全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground ml-2\",\n                                    children: [\n                                        \"已选择导出: \",\n                                        exportBlockIds.length,\n                                        \" 项\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                isExportingAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 ml-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                        value: exportProgress,\n                                        className: \"h-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-2 border rounded-md\",\n                                children: dataChunks.map((chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__.RadioGroup, {\n                                                value: selectedBlockId,\n                                                onValueChange: handleBlockSelectionChange,\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__.RadioGroupItem, {\n                                                            value: chunk.block_id,\n                                                            id: \"display-\".concat(chunk.block_id)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                            htmlFor: \"display-\".concat(chunk.block_id),\n                                                            className: \"cursor-pointer\",\n                                                            children: \"显示\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"export-\".concat(chunk.block_id),\n                                                        checked: exportBlockIds.includes(chunk.block_id),\n                                                        onChange: ()=>handleExportSelectionChange(chunk.block_id),\n                                                        className: \"h-4 w-4 rounded border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"export-\".concat(chunk.block_id),\n                                                        className: \"cursor-pointer\",\n                                                        children: \"数据块 \".concat(chunk.block_id, \" (胶厚: \").concat(chunk.glue_thickness_values.length, \", 准直: \").concat(chunk.collimation_diff_values.length, \")\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, chunk.block_id, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined),\n                        (!dataChunks || dataChunks.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground p-2\",\n                            children: \"暂无数据块可供分析或导出。\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LogDisplayArea, \"ghX4kALUHkvRRCRMmi6hPcionB0=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = LogDisplayArea;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogDisplayArea);\nvar _c;\n$RefreshReg$(_c, \"LogDisplayArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\n"));

/***/ })

});