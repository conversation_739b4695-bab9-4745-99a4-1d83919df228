"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/log-analysis/LogChartView.tsx":
/*!**************************************************!*\
  !*** ./components/log-analysis/LogChartView.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogChartView: () => (/* binding */ LogChartView),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/ComposedChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/ReferenceLine.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ LogChartView,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Helper functions (parseTimestamp, processBlockData) defined at the top-level\n// so they are accessible by both LogChartView and ChartRenderer.\nconst parseTimestamp = (timestamp)=>{\n    if (!timestamp) return NaN;\n    try {\n        let standardizedTimestamp = timestamp.replace(\",\", \".\");\n        if (!standardizedTimestamp.includes(\"T\") && standardizedTimestamp.includes(\" \")) {\n            const parts = standardizedTimestamp.split(\" \");\n            if (parts.length > 1 && parts[0].includes(\"-\") && parts[1].includes(\":\")) {\n                standardizedTimestamp = parts[0] + \"T\" + parts.slice(1).join(\" \");\n            }\n        }\n        let date = new Date(standardizedTimestamp);\n        let time = date.getTime();\n        if (isNaN(time)) {\n            const slightlyLessStandardized = timestamp.replace(\",\", \".\");\n            date = new Date(slightlyLessStandardized);\n            time = date.getTime();\n        }\n        if (isNaN(time)) {\n            date = new Date(timestamp);\n            time = date.getTime();\n        }\n        if (isNaN(time)) {\n            console.warn('[LogChartViewHelper] Failed to parse timestamp: \"'.concat(timestamp, '\"'));\n            return NaN;\n        }\n        return time;\n    } catch (error) {\n        console.error('[LogChartViewHelper] Error parsing timestamp: \"'.concat(timestamp, '\"'), error);\n        return NaN;\n    }\n};\nconst processBlockData = (block)=>{\n    const dataPoints = [];\n    (block.glue_thickness_values || []).forEach((value)=>{\n        const time = parseTimestamp(value.timestamp);\n        if (!isNaN(time)) dataPoints.push({\n            time,\n            glueThickness: value.value,\n            collimationDiff: null\n        });\n    });\n    (block.collimation_diff_values || []).forEach((value)=>{\n        const time = parseTimestamp(value.timestamp);\n        if (!isNaN(time)) dataPoints.push({\n            time,\n            glueThickness: null,\n            collimationDiff: value.value\n        });\n    });\n    dataPoints.sort((a, b)=>a.time - b.time);\n    const mergedPoints = [];\n    dataPoints.forEach((point)=>{\n        const existingPoint = mergedPoints.find((p)=>p.time === point.time);\n        if (existingPoint) {\n            if (point.glueThickness !== null) existingPoint.glueThickness = point.glueThickness;\n            if (point.collimationDiff !== null) existingPoint.collimationDiff = point.collimationDiff;\n        } else {\n            mergedPoints.push({\n                ...point\n            });\n        }\n    });\n    return mergedPoints;\n};\nconst ChartRenderer = (param)=>{\n    let { chunk, isChartReady } = param;\n    _s();\n    const { chartDataForThisBlock, eventPointsForThisBlock, timeDomainForThisBlock, glueDomainForThisBlock, collimationDomainForThisBlock, hasDataForThisBlock } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChartRenderer.useMemo\": ()=>{\n            if (!chunk) {\n                return {\n                    chartDataForThisBlock: [],\n                    eventPointsForThisBlock: [],\n                    timeDomainForThisBlock: [\n                        Date.now() - 3600000,\n                        Date.now()\n                    ],\n                    glueDomainForThisBlock: [\n                        0,\n                        1000\n                    ],\n                    collimationDomainForThisBlock: [\n                        0,\n                        0.1\n                    ],\n                    hasDataForThisBlock: false\n                };\n            }\n            const processedDataPoints = processBlockData(chunk);\n            const currentEventPoints = (chunk.valve_open_events || []).map({\n                \"ChartRenderer.useMemo.currentEventPoints\": (event)=>{\n                    const eventTime = parseTimestamp(event.timestamp);\n                    return {\n                        time: eventTime,\n                        value: 0,\n                        label: \"打开放气阀 (\".concat(event.timestamp.split(' ')[1] || event.timestamp, \")\")\n                    };\n                }\n            }[\"ChartRenderer.useMemo.currentEventPoints\"]).filter({\n                \"ChartRenderer.useMemo.currentEventPoints\": (ep)=>!isNaN(ep.time)\n            }[\"ChartRenderer.useMemo.currentEventPoints\"]);\n            currentEventPoints.sort({\n                \"ChartRenderer.useMemo\": (a, b)=>a.time - b.time\n            }[\"ChartRenderer.useMemo\"]);\n            let timeDom = [\n                Date.now() - 3600000,\n                Date.now()\n            ];\n            if (processedDataPoints.length > 0) {\n                const times = processedDataPoints.map({\n                    \"ChartRenderer.useMemo.times\": (p)=>p.time\n                }[\"ChartRenderer.useMemo.times\"]).filter({\n                    \"ChartRenderer.useMemo.times\": (t)=>!isNaN(t)\n                }[\"ChartRenderer.useMemo.times\"]);\n                if (times.length > 0) {\n                    timeDom = [\n                        Math.min(...times),\n                        Math.max(...times)\n                    ];\n                }\n            }\n            if (timeDom[0] === timeDom[1]) timeDom[1] = timeDom[0] + 3600000;\n            const glueValues = processedDataPoints.map({\n                \"ChartRenderer.useMemo.glueValues\": (p)=>p.glueThickness\n            }[\"ChartRenderer.useMemo.glueValues\"]).filter({\n                \"ChartRenderer.useMemo.glueValues\": (v)=>v !== null && !isNaN(v)\n            }[\"ChartRenderer.useMemo.glueValues\"]);\n            let glueDom = [\n                0,\n                1000\n            ];\n            if (glueValues.length > 0) glueDom = [\n                Math.min(...glueValues),\n                Math.max(...glueValues)\n            ];\n            if (glueDom[0] === glueDom[1]) glueDom[1] = glueDom[0] + 10;\n            const collimValues = processedDataPoints.map({\n                \"ChartRenderer.useMemo.collimValues\": (p)=>p.collimationDiff\n            }[\"ChartRenderer.useMemo.collimValues\"]).filter({\n                \"ChartRenderer.useMemo.collimValues\": (v)=>v !== null && !isNaN(v)\n            }[\"ChartRenderer.useMemo.collimValues\"]);\n            let collimDom = [\n                0,\n                0.1\n            ];\n            if (collimValues.length > 0) collimDom = [\n                Math.min(...collimValues),\n                Math.max(...collimValues)\n            ];\n            if (collimDom[0] === collimDom[1]) collimDom[1] = collimDom[0] + 0.01;\n            return {\n                chartDataForThisBlock: processedDataPoints,\n                eventPointsForThisBlock: currentEventPoints,\n                timeDomainForThisBlock: timeDom,\n                glueDomainForThisBlock: glueDom,\n                collimationDomainForThisBlock: collimDom,\n                hasDataForThisBlock: processedDataPoints.length > 0\n            };\n        }\n    }[\"ChartRenderer.useMemo\"], [\n        chunk\n    ]);\n    const shouldRenderChartContent = hasDataForThisBlock && isChartReady;\n    if (!shouldRenderChartContent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full text-muted-foreground\",\n            children: !isChartReady ? \"图表加载中...\" : \"此数据块无有效图表数据。\"\n        }, void 0, false, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.ResponsiveContainer, {\n        width: \"100%\",\n        height: \"100%\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.ComposedChart, {\n            data: chartDataForThisBlock,\n            margin: {\n                top: 20,\n                right: 40,\n                left: 30,\n                bottom: 20\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.XAxis, {\n                    dataKey: \"time\",\n                    domain: timeDomainForThisBlock,\n                    type: \"number\",\n                    tickFormatter: (value)=>new Date(value).toLocaleTimeString(),\n                    allowDuplicatedCategory: false\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.YAxis, {\n                    yAxisId: \"glue\",\n                    orientation: \"left\",\n                    domain: glueDomainForThisBlock,\n                    type: \"number\",\n                    stroke: \"#8884d8\",\n                    label: {\n                        value: '胶厚 (μm)',\n                        angle: -90,\n                        position: 'insideLeft',\n                        offset: -5,\n                        style: {\n                            fill: '#8884d8',\n                            textAnchor: 'middle'\n                        }\n                    },\n                    tickFormatter: (value)=>value.toFixed(2),\n                    width: 70\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.YAxis, {\n                    yAxisId: \"collimation\",\n                    orientation: \"right\",\n                    domain: collimationDomainForThisBlock,\n                    type: \"number\",\n                    stroke: \"#82ca9d\",\n                    label: {\n                        value: '准直差',\n                        angle: 90,\n                        position: 'insideRight',\n                        offset: -15,\n                        style: {\n                            fill: '#82ca9d',\n                            textAnchor: 'middle'\n                        }\n                    },\n                    tickFormatter: (value)=>value.toFixed(3),\n                    width: 80\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                    labelFormatter: (value)=>new Date(value).toLocaleString(),\n                    formatter: (value, name)=>{\n                        if (typeof value !== 'number') return [\n                            value,\n                            name\n                        ];\n                        if (name === 'glueThickness') return [\n                            value.toFixed(2) + ' μm',\n                            '胶厚'\n                        ];\n                        if (name === 'collimationDiff') return [\n                            value.toFixed(3),\n                            '准直差'\n                        ];\n                        return [\n                            value,\n                            name\n                        ];\n                    },\n                    contentStyle: {\n                        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n                        border: '1px solid #ccc',\n                        borderRadius: '4px',\n                        padding: '8px'\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Legend, {\n                    verticalAlign: \"top\",\n                    height: 36,\n                    wrapperStyle: {\n                        paddingBottom: '10px'\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Line, {\n                    yAxisId: \"glue\",\n                    type: \"monotone\",\n                    dataKey: \"glueThickness\",\n                    name: \"胶厚\",\n                    stroke: \"#8884d8\",\n                    strokeWidth: 2,\n                    dot: {\n                        r: 2\n                    },\n                    activeDot: {\n                        r: 5\n                    },\n                    isAnimationActive: false,\n                    connectNulls: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Line, {\n                    yAxisId: \"collimation\",\n                    type: \"monotone\",\n                    dataKey: \"collimationDiff\",\n                    name: \"准直差\",\n                    stroke: \"#82ca9d\",\n                    strokeWidth: 2,\n                    dot: {\n                        r: 2\n                    },\n                    activeDot: {\n                        r: 5\n                    },\n                    isAnimationActive: false,\n                    connectNulls: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, undefined),\n                eventPointsForThisBlock.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.ReferenceLine, {\n                        x: event.time,\n                        stroke: \"rgba(255,0,0,0.7)\",\n                        yAxisId: \"glue\",\n                        strokeDasharray: \"4 4\",\n                        label: {\n                            value: event.label,\n                            position: 'insideTopRight',\n                            fill: 'rgba(255,0,0,0.7)',\n                            fontSize: 10\n                        }\n                    }, \"event-\".concat(chunk.block_id, \"-\").concat(index), false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChartRenderer, \"8mDZ0hNgDzpsS3GxcLMYCTN7JZg=\");\n_c = ChartRenderer;\nfunction LogChartView(param) {\n    let { dataChunks, selectedBlockIds, onBlockSelect, isHighlighted = false } = param;\n    _s1();\n    const chartContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isChartReady, setIsChartReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogChartView.useEffect\": ()=>{\n            setIsChartReady(false); // Reset on selection change\n            const timer = setTimeout({\n                \"LogChartView.useEffect.timer\": ()=>setIsChartReady(true)\n            }[\"LogChartView.useEffect.timer\"], 100); // Delay for container sizing\n            return ({\n                \"LogChartView.useEffect\": ()=>clearTimeout(timer)\n            })[\"LogChartView.useEffect\"];\n        }\n    }[\"LogChartView.useEffect\"], [\n        selectedBlockIds,\n        dataChunks\n    ]); // Also depend on dataChunks if it can change independently for selectedBlockIds\n    const hasChartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogChartView.useMemo[hasChartData]\": ()=>{\n            console.log('LogChartView - hasChartData check:', {\n                dataChunks\n            });\n            if (!dataChunks || !Array.isArray(dataChunks)) {\n                console.log('LogChartView - dataChunks is invalid:', dataChunks);\n                return false;\n            }\n            return dataChunks.length > 0;\n        }\n    }[\"LogChartView.useMemo[hasChartData]\"], [\n        dataChunks\n    ]);\n    const chartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogChartView.useMemo[chartData]\": ()=>{\n            console.log('LogChartView - chartData calculation:', {\n                dataChunks,\n                selectedBlockIds\n            });\n            if (!dataChunks || !Array.isArray(dataChunks)) {\n                console.log('LogChartView - dataChunks is invalid in chartData:', dataChunks);\n                return [];\n            }\n            return dataChunks.filter({\n                \"LogChartView.useMemo[chartData]\": (chunk)=>{\n                    console.log('Filtering chunk:', {\n                        chunk,\n                        selectedBlockIds\n                    });\n                    return selectedBlockIds.includes(chunk.block_id);\n                }\n            }[\"LogChartView.useMemo[chartData]\"]).flatMap({\n                \"LogChartView.useMemo[chartData]\": (chunk)=>{\n                    console.log('Processing chunk for chart data:', chunk);\n                    if (!chunk.data || !Array.isArray(chunk.data)) {\n                        console.log('Invalid chunk data:', chunk.data);\n                        return [];\n                    }\n                    return chunk.data.map({\n                        \"LogChartView.useMemo[chartData]\": (item)=>({\n                                name: item.name,\n                                value: item.value,\n                                type: item.type,\n                                block_id: chunk.block_id\n                            })\n                    }[\"LogChartView.useMemo[chartData]\"]);\n                }\n            }[\"LogChartView.useMemo[chartData]\"]);\n        }\n    }[\"LogChartView.useMemo[chartData]\"], [\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const handleBlockSelect = (blockId)=>{\n        console.log('LogChartView - handleBlockSelect called with:', blockId);\n        onBlockSelect(blockId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: chartContainerRef,\n        className: \"space-y-6 log-chart-container\",\n        children: dataChunks.map((chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                \"data-block-id\": chunk.block_id,\n                className: \"\".concat(selectedBlockIds.includes(chunk.block_id) ? 'block' : 'hidden', \" \").concat(isHighlighted ? 'ring-2 ring-offset-2 ring-blue-500' : ''),\n                style: {\n                    display: selectedBlockIds.includes(chunk.block_id) ? 'block' : 'none',\n                    minHeight: selectedBlockIds.includes(chunk.block_id) ? '450px' : '0'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row justify-between items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: [\n                                \"数据块 \",\n                                chunk.block_id\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-[400px] min-h-[400px]\",\n                            style: {\n                                width: '100%',\n                                height: '400px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartRenderer, {\n                                chunk: chunk,\n                                isChartReady: isChartReady\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, chunk.block_id, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s1(LogChartView, \"HjaQbVhm0E28jgmHJb3zubxsEFg=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c1 = LogChartView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogChartView);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartRenderer\");\n$RefreshReg$(_c1, \"LogChartView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/LogChartView.tsx\n"));

/***/ })

});