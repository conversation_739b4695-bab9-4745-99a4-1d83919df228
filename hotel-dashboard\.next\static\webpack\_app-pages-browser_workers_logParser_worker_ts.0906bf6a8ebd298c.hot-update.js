"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // V1 blocks are now strictly defined from a start phrase to an end phrase.\n    const BLOCK_REGEX_V1 = /(?:开始|打开)抽真空[\\s\\S]*?insert into g_support/g;\n    // V2 blocks have their own distinct start and end markers.\n    const BLOCK_REGEX_V2 = /轴停止运动[\\s\\S]*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent, blockVersion) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        // Get timestamps for specific start/end sentences for debugging, as requested by the user.\n        const startSentenceTs = _extractTimestampFromLine(blockLines[0]);\n        let endSentenceTs = null;\n        // Find the last line containing a known end-of-block marker to get its timestamp.\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const line = blockLines[i];\n            if (line.includes('insert into g_support') || line.includes('SetSpeed:2, 85, result:')) {\n                endSentenceTs = _extractTimestampFromLine(line);\n                break; // Found the last marker line\n            }\n        }\n        if (DEBUG) {\n            // Enhanced logging as requested by the user.\n            console.log(\"[Worker] Block \".concat(blockId, \": \") + \"StartLineTS=\".concat(startSentenceTs, \", EndLineTS=\").concat(endSentenceTs, \", \") + \"OverallStartTS=\".concat(startTimeStr, \", OverallEndTS=\").concat(endTimeStr));\n        }\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            if (blockVersion === \"V1\") {\n                // Check for V1 Glue\n                const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n                if (glueMatchV1) {\n                    try {\n                        const valueFloat = parseFloat(glueMatchV1[1]);\n                        if (timestampForValue) {\n                            glueThicknessValuesList.push({\n                                timestamp: timestampForValue,\n                                value: valueFloat\n                            });\n                        }\n                    } catch (e) {}\n                }\n                // Check for V1 Diff\n                const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n                if (diffMatchV1) {\n                    try {\n                        const valueFloat = parseFloat(diffMatchV1[1]);\n                        if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                            collimationDiffValuesList.push({\n                                timestamp: timestampForValue,\n                                value: valueFloat\n                            });\n                        }\n                    } catch (e) {}\n                }\n            }\n            if (blockVersion === \"V2\") {\n                // Check for V2 Glue\n                const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n                if (glueMatchV2) {\n                    const valueFloat = parseFloat(glueMatchV2[1]);\n                    if (timestampForValue && !isNaN(valueFloat)) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                    }\n                }\n                // Check for V2 Diff\n                const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n                if (diffMatchV2) {\n                    if (timestampForValue) {\n                        const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                        const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                        if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                            collimationDiffValuesList.push({\n                                timestamp: timestampForValue,\n                                value: diffValue\n                            });\n                        }\n                    }\n                }\n            }\n            // Generic events can be checked for all block types\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        // 1. Find all possible V1 and V2 blocks independently.\n        // Add version information to each match before combining.\n        const v1Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V1)).map((m)=>({\n                ...m,\n                version: \"V1\"\n            }));\n        const v2Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V2)).map((m)=>({\n                ...m,\n                version: \"V2\"\n            }));\n        // 2. Combine and sort all found blocks by their starting position in the log file.\n        const allMatches = [\n            ...v1Matches,\n            ...v2Matches\n        ].sort((a, b)=>(a.index || 0) - (b.index || 0));\n        if (allMatches.length === 0) {\n            if (DEBUG) console.log(\"[Worker] No V1 or V2 blocks found in log content.\");\n            return [];\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // 3. Process each block in the correct order.\n        allMatches.forEach((match, index)=>{\n            const blockContent = match[0];\n            if (!blockContent) return;\n            let sn = null;\n            // SN is always expected inside the block now, due to the new regex definitions.\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            }\n            // Process the block first with a temporary ID to get its properties, like start_time.\n            const tempBlockId = \"temp_\".concat(index + 1);\n            const processedBlock = processRawBlock(tempBlockId, blockContent, match.version);\n            if (processedBlock) {\n                // Now, create the final block_id based on the user's request.\n                // Use the start_time if available, otherwise fall back to the old naming scheme.\n                // Use the start_time to create the block ID in YYYYMMDD_HH_mm_ss format, as requested.\n                let formattedId = null;\n                if (processedBlock.start_time) {\n                    // This is a more robust way to format the date, avoiding potential string replacement issues.\n                    try {\n                        const date = new Date(processedBlock.start_time.replace(',', '.'));\n                        if (isNaN(date.getTime())) {\n                            throw new Error(\"Invalid date\");\n                        }\n                        const y = date.getFullYear().toString();\n                        const m = (date.getMonth() + 1).toString().padStart(2, '0');\n                        const d = date.getDate().toString().padStart(2, '0');\n                        const h = date.getHours().toString().padStart(2, '0');\n                        const min = date.getMinutes().toString().padStart(2, '0');\n                        const s = date.getSeconds().toString().padStart(2, '0');\n                        formattedId = \"\".concat(y).concat(m).concat(d, \"_\").concat(h, \"_\").concat(min, \"_\").concat(s);\n                    } catch (e) {\n                        // If date parsing fails, formattedId remains null, and the fallback ID will be used.\n                        if (DEBUG) console.error(\"Could not parse date for block ID: \".concat(processedBlock.start_time));\n                    }\n                }\n                const finalBlockId = formattedId || (sn ? \"\".concat(sn, \"_\").concat(index + 1) : \"block_\".concat(index + 1));\n                processedBlock.block_id = finalBlockId;\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Every matched block is valid and must be kept.\n                localProcessedBlocks.push(processedBlock);\n            }\n        });\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the full log content for searching, and parse it into blocks.\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp range match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        for (const block of processedBlocks){\n                            // Check if the block has valid start and end times.\n                            if (block.start_time && block.end_time) {\n                                try {\n                                    const blockStartTime = new Date(block.start_time.replace(',', '.')).getTime();\n                                    const blockEndTime = new Date(block.end_time.replace(',', '.')).getTime();\n                                    // The correct logic, as per user instruction:\n                                    // Check if the image's timestamp falls within the block's time range.\n                                    if (targetTime >= blockStartTime && targetTime <= blockEndTime) {\n                                        if (DEBUG) console.log(\"[Worker] Timestamp \".concat(targetTime, \" falls within block \").concat(block.block_id, \" range [\").concat(blockStartTime, \" - \").concat(blockEndTime, \"]. Match found.\"));\n                                        matchedBlockIds.add(block.block_id);\n                                    }\n                                } catch (e) {\n                                    if (DEBUG) console.error(\"[Worker] Could not parse timestamp for block \".concat(block.block_id, \".\"), e);\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("acc9935d70bf9776")
/******/ })();
/******/ 
/******/ }
);