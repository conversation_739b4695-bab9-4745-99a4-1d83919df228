# Active Context

  This file tracks the project's current status, including recent changes, current goals, and open questions.
  2025-07-07 18:29:12 - Log of updates made.

*

## Current Focus
*   [2025-07-07 18:29:48] - 初始任务：更新 `hotel-dashboard/workers/logParser.worker.ts` 文件中 `calculateV2CollimationDiff` 函数的计算公式。

*   [2025-07-08 17:06:00] - 当前任务：为日志分析页面设计并实现一个新的SN搜索功能。

*   [2025-07-07 18:30:37] - 已将 `calculateV2CollimationDiff` 函数更新为使用两点之间的距离公式。
## Recent Changes
*   [2025-07-07 18:42:16] - 修复了 `LogFileUpload.tsx` 中的文件上传功能，通过读取 `ArrayBuffer` 并使用 `jschardet` 恢复了对多文件批量上传的字符编码检测，以解决因编码错误导致的 "Unknown log file version" 问题。

*   

## Open Questions/Issues

*   
* [2025-07-07 19:47:46] - 重构了日志分析图表的批量导出功能，通过实现异步处理队列解决了大量组件同时渲染导致的性能问题。
* [2025-07-07 19:57:09] - Fixed an issue where exported chart images were captured before rendering was complete by implementing a `waitForChartReady` polling function.
* [2025-07-08 09:21:07] - Refactored `logParser.worker.ts` to fix a regression causing silent failures in V1 log parsing. The `parseLogContent` function no longer has side effects, and the `onmessage` handler now uses a `try...catch` block to report all errors.
* [2025-07-08 09:26:44] - 修复了 `logParser.worker.ts` 中 V1 日志解析的正则表达式状态问题，确保所有日志条目都能被正确匹配。
* [2025-07-08 09:32:41] - 重新应用了对 `logParser.worker.ts` 的修复，以解决 V1 日志正则表达式的状态问题，确保所有条目都能被正确解析。
* [2025-07-08 09:40:19] - 重构了 `logParser.worker.ts`，通过创建一个统一的 `processRawBlock` 函数来支持在同一数据块中解析 V1 和 V2 的混合日志格式。
* [2025-07-08 09:44:27] - [FIX] Re-added outlier filtering (`Math.abs(value) <= 100`) for V2 collimation difference values in `logParser.worker.ts` to fix a regression.
* [2025-07-08 09:56:36] - [FEAT] Enhanced block naming in `logParser.worker.ts`. Blocks are now named `{SN}_{index}` if an SN is found in the `insert into g_support` statement, falling back to `block_{index}`.
* [2025-07-08 09:59:21] - [FEAT] Improved SN detection in `logParser.worker.ts`. The parser now searches for the SN both inside the data block and, if not found, in the area immediately following the block, ensuring correct association for both V1 and V2 log structures.
* [2025-07-08 17:17:16] - [DEBUG] Investigating and fixing case-sensitive SN search bug in log analysis page.
* [2025-07-08 17:17:16] - [FIX] Patched `handleSearch` in `log-analysis/page.tsx` to perform case-insensitive search and also check against `block.id` as a fallback.
* [2025-07-08 17:19:58] - [FIX] Corrected a runtime crash in the SN search feature on the log analysis page by adding a check to ensure `block.sns` is an array before attempting to iterate over it.
* [2025-07-11 15:38:55] - [FEAT] Created new React components `ImageNameSearch.tsx` and `BatchExportCSV.tsx` for the log analysis page as per architectural design.
* [2025-07-11 15:45:50] - [FEAT] Refactored `logParser.worker.ts` to support stateful, message-based processing and implemented the `MATCH_BY_TIMESTAMP` feature for interactive log searching.
* [2025-07-11 16:02:30] - [FEAT] Integrated `ImageNameSearch` and `BatchExportCSV` components into the log analysis page, enabling two-way communication with the Web Worker for timestamp-based log matching and unifying the UI selection state.
* [2025-07-11 16:08:00] - [FEAT] Implemented the core logic for CSV export of `g_support` SQL statements by adding `extractAndParseSqlInserts` and `exportDataToCsv` to `lib/exportUtils.ts` and integrating them into the `BatchExportCSV.tsx` component.
* [2025-07-11 16:19:00] - [DEBUG] Investigated and fixed a bug in `logParser.worker.ts` where the Web Worker would hang, caused by catastrophic backtracking in the block-parsing regex.
* [2025-07-11 16:42:00] - [DEBUG] Investigated and fixed a regression where the 'Upload and Analyze' button was unresponsive. The root cause was a faulty refactoring that created two conflicting Web Worker instances.
* [2025-07-11 16:47:00] - [DEBUG] Fixed a follow-up `TypeError` in `page.tsx` caused by incorrect destructuring of the Web Worker's message payload.
* [2025-07-11 16:53:00] - [FEAT] Implemented correct filename formatting (`YYYYMMDD_HH_mm_ssGBSNxxxxxx.png`) for exported chart images based on user feedback.
* [2025-07-11 16:56:00] - [FIX] Corrected the timestamp format in exported filenames to `YYYYMMDD_HHmmss` by removing underscores between time components.
* [2025-07-11 16:59:00] - [FIX] Fixed a bug in `ImageNameSearch.tsx` that caused a crash when parsing filenames with underscores in the time component (e.g., `YYYYMMDD_HH_mm_ss...`).
* [2025-07-11 17:01:00] - [FIX] Corrected the final destructuring error in `page.tsx` for the `MATCH_BY_TIMESTAMP_RESULT` message, unifying the worker communication logic.
* [2025-07-11 17:05:00] - [FEAT] Improved user feedback for the image name search feature by adding explicit "no results found" notifications and console logging for all search outcomes.
* [2025-07-11 17:07:00] - [FIX] Resolved the final bug in timestamp matching by changing the hardcoded search keyword in the worker from "打开真空泵" to a more generic "抽真空".
* [2025-07-11 17:41:00] - [DEBUG] Investigating issue where chart X-axis does not respect the full `start_time` and `end_time` of the data block.