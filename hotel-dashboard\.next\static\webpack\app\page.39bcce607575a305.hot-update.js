"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx":
/*!****************************************************!*\
  !*** ./components/log-analysis/LogDisplayArea.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// LogChartView is removed as it will be handled by the parent page\n\n\n\n\n\n\nconst LogDisplayArea = (param)=>{\n    let { dataChunks, onSelectionChange, onStartExport } = param;\n    _s();\n    console.log('[LogDisplayArea] Rendering. ProcessedDataChunks count:', dataChunks.length);\n    const [selectedBlockId, setSelectedBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''); // 单选显示\n    const [exportBlockIds, setExportBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 多选导出\n    const displayAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const onSelectionChangeRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(onSelectionChange);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            onSelectionChangeRef.current = onSelectionChange;\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        onSelectionChange\n    ]);\n    // 当有新的数据块时，自动选择第一个\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            console.log('[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:', dataChunks.length, 'current selectedBlockId:', selectedBlockId);\n            if (dataChunks.length === 0) {\n                if (selectedBlockId !== '') {\n                    console.log('[LogDisplayArea] No data chunks, clearing selectedBlockId.');\n                    setSelectedBlockId('');\n                }\n            } else {\n                const currentSelectionIsValid = selectedBlockId && selectedBlockId.trim() !== '' && dataChunks.some({\n                    \"LogDisplayArea.useEffect\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect\"]);\n                if (!currentSelectionIsValid) {\n                    console.log('[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.');\n                    const firstValidBlock = dataChunks.find({\n                        \"LogDisplayArea.useEffect.firstValidBlock\": (chunk)=>chunk.block_id && typeof chunk.block_id === 'string' && chunk.block_id.trim() !== ''\n                    }[\"LogDisplayArea.useEffect.firstValidBlock\"]);\n                    if (firstValidBlock) {\n                        console.log('[LogDisplayArea] Found first valid block. Setting selectedBlockId to:', firstValidBlock.block_id);\n                        setSelectedBlockId(firstValidBlock.block_id);\n                    } else {\n                        if (selectedBlockId !== '') {\n                            console.warn('[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId.');\n                            setSelectedBlockId('');\n                        }\n                    }\n                } else {\n                    console.log('[LogDisplayArea] Current selection is still valid:', selectedBlockId);\n                }\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        dataChunks,\n        selectedBlockId\n    ]); // Keep selectedBlockId in dependencies to re-evaluate if it changes externally or becomes invalid\n    // 当显示选择改变时，通知父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            if (selectedBlockId && dataChunks.length > 0) {\n                const selected = dataChunks.filter({\n                    \"LogDisplayArea.useEffect.selected\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect.selected\"]);\n                onSelectionChangeRef.current(selected); // 传递筛选出的块\n            } else {\n                onSelectionChangeRef.current([]); // 如果没有选中的ID或没有数据块，传递空数组\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        selectedBlockId,\n        dataChunks\n    ]);\n    const handleBlockSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleBlockSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleBlockSelectionChange - START. blockId:', blockId);\n            setSelectedBlockId(blockId);\n            console.log('[LogDisplayArea] handleBlockSelectionChange - END.');\n        }\n    }[\"LogDisplayArea.useCallback[handleBlockSelectionChange]\"], []);\n    const handleExportSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleExportSelectionChange - START. blockId:', blockId);\n            setExportBlockIds({\n                \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (prevSelected)=>{\n                    const newSelection = prevSelected.includes(blockId) ? prevSelected.filter({\n                        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (id)=>id !== blockId\n                    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]) : [\n                        ...prevSelected,\n                        blockId\n                    ];\n                    console.log('[LogDisplayArea] handleExportSelectionChange - New selection:', newSelection);\n                    return newSelection;\n                }\n            }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]);\n        }\n    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"], []);\n    const selectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[selectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] selectAllForExport - START');\n            const allIds = dataChunks.map({\n                \"LogDisplayArea.useCallback[selectAllForExport].allIds\": (chunk)=>chunk.block_id\n            }[\"LogDisplayArea.useCallback[selectAllForExport].allIds\"]);\n            setExportBlockIds(allIds);\n            console.log('[LogDisplayArea] selectAllForExport - END. Selected all IDs:', allIds);\n        }\n    }[\"LogDisplayArea.useCallback[selectAllForExport]\"], [\n        dataChunks\n    ]);\n    const deselectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[deselectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] deselectAllForExport - START');\n            setExportBlockIds([]);\n            console.log('[LogDisplayArea] deselectAllForExport - END');\n        }\n    }[\"LogDisplayArea.useCallback[deselectAllForExport]\"], []);\n    const handleExportAllImages = async ()=>{\n        console.log('handleExportAllImages called');\n        console.log('exportBlockIds:', exportBlockIds);\n        if (!dataChunks || dataChunks.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '没有可导出的内容。'\n            });\n            return;\n        }\n        if (exportBlockIds.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '请至少选择一个数据块进行导出。'\n            });\n            return;\n        }\n        // 使用主页面的导出模式，这样可以正确渲染所有选中的图表\n        console.log('[LogDisplayArea] Triggering export via onStartExport callback');\n        onStartExport(exportBlockIds);\n    };\n    const hasContentToExport = dataChunks && dataChunks.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"h-[450px] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"选择数据块进行分析\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"从解析的日志文件中选择一个数据块以在图表中显示。\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleExportAllImages,\n                        disabled: !hasContentToExport || exportBlockIds.length === 0,\n                        size: \"sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined),\n                            \"导出选中图片\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: displayAreaRef,\n                    className: \"h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: selectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: deselectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"取消全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground ml-2\",\n                                    children: [\n                                        \"已选择导出: \",\n                                        exportBlockIds.length,\n                                        \" 项\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-2 border rounded-md\",\n                                children: dataChunks.map((chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroup, {\n                                                value: selectedBlockId,\n                                                onValueChange: handleBlockSelectionChange,\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                            value: chunk.block_id,\n                                                            id: \"display-\".concat(chunk.block_id)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                            htmlFor: \"display-\".concat(chunk.block_id),\n                                                            className: \"cursor-pointer\",\n                                                            children: \"显示\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"export-\".concat(chunk.block_id),\n                                                        checked: exportBlockIds.includes(chunk.block_id),\n                                                        onChange: ()=>handleExportSelectionChange(chunk.block_id),\n                                                        className: \"h-4 w-4 rounded border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"export-\".concat(chunk.block_id),\n                                                        className: \"cursor-pointer\",\n                                                        children: \"数据块 \".concat(chunk.block_id, \" (胶厚: \").concat(chunk.glue_thickness_values.length, \", 准直: \").concat(chunk.collimation_diff_values.length, \")\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, chunk.block_id, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, undefined),\n                        (!dataChunks || dataChunks.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground p-2\",\n                            children: \"暂无数据块可供分析或导出。\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LogDisplayArea, \"ghX4kALUHkvRRCRMmi6hPcionB0=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = LogDisplayArea;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogDisplayArea);\nvar _c;\n$RefreshReg$(_c, \"LogDisplayArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\n"));

/***/ })

});