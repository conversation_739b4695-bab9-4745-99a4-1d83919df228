/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	// runtime can't be in strict mode because a global variable is assign and maybe created.
/******/ 	var __webpack_modules__ = ({

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    const BLOCK_REGEX_V1 = /打开真空泵(?:(?!打开真空泵)[\\s\\S])*?insert into g_support/g;\n    // --- V2 正则表达式 ---\n    const BLOCK_REGEX_V2 = /轴停止运动(?:(?!轴停止运动)[\\s\\S])*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function detectLogVersion(logContent) {\n        if (logContent.includes(\"轴停止运动\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V2');\n            return \"V2\";\n        }\n        if (logContent.includes(\"打开真空泵\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V1');\n            return \"V1\";\n        }\n        if (DEBUG) console.warn('[Worker] Could not determine log version.');\n        return \"UNKNOWN\";\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const version = detectLogVersion(logContent);\n        if (version === \"UNKNOWN\") {\n            throw new Error('Unknown log file version. Could not parse.');\n        }\n        const blockRegex = version === \"V1\" ? BLOCK_REGEX_V1 : BLOCK_REGEX_V2;\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        let match;\n        let lastIndex = 0;\n        let iterationCount = 0;\n        const MAX_ITERATIONS = 100000; // Safety break to prevent infinite loops\n        // Manually iterate with exec() to have more control and prevent crashes\n        while((match = blockRegex.exec(logContent)) !== null){\n            iterationCount++;\n            if (iterationCount > MAX_ITERATIONS) {\n                self.postMessage({\n                    type: 'PARSE_LOG_RESULT',\n                    success: false,\n                    error: 'Infinite loop detected during log parsing. Check log format and block delimiters.'\n                });\n                return []; // Stop processing\n            }\n            // This check prevents infinite loops on zero-length matches\n            if (match.index === blockRegex.lastIndex) {\n                blockRegex.lastIndex++;\n            }\n            const blockContent = match[0];\n            if (!blockContent || typeof match.index === 'undefined') continue;\n            let sn = null;\n            // 1. Try to find SN inside the block first\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n                if (DEBUG) console.log(\"[Worker] Found SN inside block \".concat(iterationCount, \": \").concat(sn));\n            } else {\n                // 2. If not found, search for the next SN *after* this block\n                const endOfCurrentBlock = match.index + blockContent.length;\n                const searchArea = logContent.substring(endOfCurrentBlock); // Search from end of block to end of file\n                const snMatchOutside = snRegex.exec(searchArea);\n                if (snMatchOutside) {\n                    // To avoid altering the main loop's regex state, create a new regex for peeking.\n                    const peekRegex = new RegExp(blockRegex.source, blockRegex.flags);\n                    peekRegex.lastIndex = endOfCurrentBlock; // Start peeking from the end of the current block\n                    const nextBlockMatch = peekRegex.exec(logContent);\n                    // Check if the found SN is located *before* the start of the next block.\n                    if (!nextBlockMatch || endOfCurrentBlock + snMatchOutside.index < nextBlockMatch.index) {\n                        sn = snMatchOutside[1];\n                        if (DEBUG) console.log(\"[Worker] Found SN outside block \".concat(iterationCount, \": \").concat(sn));\n                    } else {\n                        // The SN found belongs to a later block, so ignore it for this one.\n                        if (DEBUG) console.log(\"[Worker] SN found outside block \".concat(iterationCount, \", but it belongs to a subsequent block. Ignoring.\"));\n                    }\n                }\n            }\n            // Ensure lastIndex is correctly updated after every iteration to prevent infinite loops.\n            blockRegex.lastIndex = match.index + blockContent.length;\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(iterationCount) : \"block_\".concat(iterationCount);\n            if (DEBUG) console.log(\"[Worker] Final Block ID for index \".concat(iterationCount - 1, \": \").concat(blockId));\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0) {\n                    localProcessedBlocks.push(processedBlock);\n                }\n            }\n            lastIndex = blockRegex.lastIndex;\n        }\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the parsed blocks globally in the worker\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"打开真空泵\";\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        for (const block of processedBlocks){\n                            // Avoid re-checking a block that already has a match\n                            if (matchedBlockIds.has(block.block_id)) {\n                                continue;\n                            }\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            // Timestamps are in 'YYYY-MM-DD HH:mm:ss,SSS' format\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {\n                                        // Ignore lines with invalid date formats\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			if (cachedModule.error !== undefined) throw cachedModule.error;
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		var threw = true;
/******/ 		try {
/******/ 			var execOptions = { id: moduleId, module: module, factory: __webpack_modules__[moduleId], require: __webpack_require__ };
/******/ 			__webpack_require__.i.forEach(function(handler) { handler(execOptions); });
/******/ 			module = execOptions.module;
/******/ 			execOptions.factory.call(module.exports, module, module.exports, execOptions.require);
/******/ 			threw = false;
/******/ 		} finally {
/******/ 			if(threw) delete __webpack_module_cache__[moduleId];
/******/ 		}
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = __webpack_module_cache__;
/******/ 	
/******/ 	// expose the module execution interceptor
/******/ 	__webpack_require__.i = [];
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/get javascript update chunk filename */
/******/ 	(() => {
/******/ 		// This function allow to reference all chunks
/******/ 		__webpack_require__.hu = (chunkId) => {
/******/ 			// return url for filenames based on template
/******/ 			return "static/webpack/" + chunkId + "." + __webpack_require__.h() + ".hot-update.js";
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/get mini-css chunk filename */
/******/ 	(() => {
/******/ 		// This function allow to reference async chunks
/******/ 		__webpack_require__.miniCssF = (chunkId) => {
/******/ 			// return url for filenames based on template
/******/ 			return undefined;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/get update manifest filename */
/******/ 	(() => {
/******/ 		__webpack_require__.hmrF = () => ("static/webpack/" + __webpack_require__.h() + ".0bab1b7ad71e0b63.hot-update.json");
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/getFullHash */
/******/ 	(() => {
/******/ 		__webpack_require__.h = () => ("8fb95d1bdd995d60")
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/trusted types policy */
/******/ 	(() => {
/******/ 		var policy;
/******/ 		__webpack_require__.tt = () => {
/******/ 			// Create Trusted Type policy if Trusted Types are available and the policy doesn't exist yet.
/******/ 			if (policy === undefined) {
/******/ 				policy = {
/******/ 					createScript: (script) => (script),
/******/ 					createScriptURL: (url) => (url)
/******/ 				};
/******/ 				if (typeof trustedTypes !== "undefined" && trustedTypes.createPolicy) {
/******/ 					policy = trustedTypes.createPolicy("nextjs#bundler", policy);
/******/ 				}
/******/ 			}
/******/ 			return policy;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/trusted types script */
/******/ 	(() => {
/******/ 		__webpack_require__.ts = (script) => (__webpack_require__.tt().createScript(script));
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/trusted types script url */
/******/ 	(() => {
/******/ 		__webpack_require__.tu = (url) => (__webpack_require__.tt().createScriptURL(url));
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hot module replacement */
/******/ 	(() => {
/******/ 		var currentModuleData = {};
/******/ 		var installedModules = __webpack_require__.c;
/******/ 		
/******/ 		// module and require creation
/******/ 		var currentChildModule;
/******/ 		var currentParents = [];
/******/ 		
/******/ 		// status
/******/ 		var registeredStatusHandlers = [];
/******/ 		var currentStatus = "idle";
/******/ 		
/******/ 		// while downloading
/******/ 		var blockingPromises = 0;
/******/ 		var blockingPromisesWaiting = [];
/******/ 		
/******/ 		// The update info
/******/ 		var currentUpdateApplyHandlers;
/******/ 		var queuedInvalidatedModules;
/******/ 		
/******/ 		__webpack_require__.hmrD = currentModuleData;
/******/ 		
/******/ 		__webpack_require__.i.push(function (options) {
/******/ 			var module = options.module;
/******/ 			var require = createRequire(options.require, options.id);
/******/ 			module.hot = createModuleHotObject(options.id, module);
/******/ 			module.parents = currentParents;
/******/ 			module.children = [];
/******/ 			currentParents = [];
/******/ 			options.require = require;
/******/ 		});
/******/ 		
/******/ 		__webpack_require__.hmrC = {};
/******/ 		__webpack_require__.hmrI = {};
/******/ 		
/******/ 		function createRequire(require, moduleId) {
/******/ 			var me = installedModules[moduleId];
/******/ 			if (!me) return require;
/******/ 			var fn = function (request) {
/******/ 				if (me.hot.active) {
/******/ 					if (installedModules[request]) {
/******/ 						var parents = installedModules[request].parents;
/******/ 						if (parents.indexOf(moduleId) === -1) {
/******/ 							parents.push(moduleId);
/******/ 						}
/******/ 					} else {
/******/ 						currentParents = [moduleId];
/******/ 						currentChildModule = request;
/******/ 					}
/******/ 					if (me.children.indexOf(request) === -1) {
/******/ 						me.children.push(request);
/******/ 					}
/******/ 				} else {
/******/ 					console.warn(
/******/ 						"[HMR] unexpected require(" +
/******/ 							request +
/******/ 							") from disposed module " +
/******/ 							moduleId
/******/ 					);
/******/ 					currentParents = [];
/******/ 				}
/******/ 				return require(request);
/******/ 			};
/******/ 			var createPropertyDescriptor = function (name) {
/******/ 				return {
/******/ 					configurable: true,
/******/ 					enumerable: true,
/******/ 					get: function () {
/******/ 						return require[name];
/******/ 					},
/******/ 					set: function (value) {
/******/ 						require[name] = value;
/******/ 					}
/******/ 				};
/******/ 			};
/******/ 			for (var name in require) {
/******/ 				if (Object.prototype.hasOwnProperty.call(require, name) && name !== "e") {
/******/ 					Object.defineProperty(fn, name, createPropertyDescriptor(name));
/******/ 				}
/******/ 			}
/******/ 			fn.e = function (chunkId, fetchPriority) {
/******/ 				return trackBlockingPromise(require.e(chunkId, fetchPriority));
/******/ 			};
/******/ 			return fn;
/******/ 		}
/******/ 		
/******/ 		function createModuleHotObject(moduleId, me) {
/******/ 			var _main = currentChildModule !== moduleId;
/******/ 			var hot = {
/******/ 				// private stuff
/******/ 				_acceptedDependencies: {},
/******/ 				_acceptedErrorHandlers: {},
/******/ 				_declinedDependencies: {},
/******/ 				_selfAccepted: false,
/******/ 				_selfDeclined: false,
/******/ 				_selfInvalidated: false,
/******/ 				_disposeHandlers: [],
/******/ 				_main: _main,
/******/ 				_requireSelf: function () {
/******/ 					currentParents = me.parents.slice();
/******/ 					currentChildModule = _main ? undefined : moduleId;
/******/ 					__webpack_require__(moduleId);
/******/ 				},
/******/ 		
/******/ 				// Module API
/******/ 				active: true,
/******/ 				accept: function (dep, callback, errorHandler) {
/******/ 					if (dep === undefined) hot._selfAccepted = true;
/******/ 					else if (typeof dep === "function") hot._selfAccepted = dep;
/******/ 					else if (typeof dep === "object" && dep !== null) {
/******/ 						for (var i = 0; i < dep.length; i++) {
/******/ 							hot._acceptedDependencies[dep[i]] = callback || function () {};
/******/ 							hot._acceptedErrorHandlers[dep[i]] = errorHandler;
/******/ 						}
/******/ 					} else {
/******/ 						hot._acceptedDependencies[dep] = callback || function () {};
/******/ 						hot._acceptedErrorHandlers[dep] = errorHandler;
/******/ 					}
/******/ 				},
/******/ 				decline: function (dep) {
/******/ 					if (dep === undefined) hot._selfDeclined = true;
/******/ 					else if (typeof dep === "object" && dep !== null)
/******/ 						for (var i = 0; i < dep.length; i++)
/******/ 							hot._declinedDependencies[dep[i]] = true;
/******/ 					else hot._declinedDependencies[dep] = true;
/******/ 				},
/******/ 				dispose: function (callback) {
/******/ 					hot._disposeHandlers.push(callback);
/******/ 				},
/******/ 				addDisposeHandler: function (callback) {
/******/ 					hot._disposeHandlers.push(callback);
/******/ 				},
/******/ 				removeDisposeHandler: function (callback) {
/******/ 					var idx = hot._disposeHandlers.indexOf(callback);
/******/ 					if (idx >= 0) hot._disposeHandlers.splice(idx, 1);
/******/ 				},
/******/ 				invalidate: function () {
/******/ 					this._selfInvalidated = true;
/******/ 					switch (currentStatus) {
/******/ 						case "idle":
/******/ 							currentUpdateApplyHandlers = [];
/******/ 							Object.keys(__webpack_require__.hmrI).forEach(function (key) {
/******/ 								__webpack_require__.hmrI[key](
/******/ 									moduleId,
/******/ 									currentUpdateApplyHandlers
/******/ 								);
/******/ 							});
/******/ 							setStatus("ready");
/******/ 							break;
/******/ 						case "ready":
/******/ 							Object.keys(__webpack_require__.hmrI).forEach(function (key) {
/******/ 								__webpack_require__.hmrI[key](
/******/ 									moduleId,
/******/ 									currentUpdateApplyHandlers
/******/ 								);
/******/ 							});
/******/ 							break;
/******/ 						case "prepare":
/******/ 						case "check":
/******/ 						case "dispose":
/******/ 						case "apply":
/******/ 							(queuedInvalidatedModules = queuedInvalidatedModules || []).push(
/******/ 								moduleId
/******/ 							);
/******/ 							break;
/******/ 						default:
/******/ 							// ignore requests in error states
/******/ 							break;
/******/ 					}
/******/ 				},
/******/ 		
/******/ 				// Management API
/******/ 				check: hotCheck,
/******/ 				apply: hotApply,
/******/ 				status: function (l) {
/******/ 					if (!l) return currentStatus;
/******/ 					registeredStatusHandlers.push(l);
/******/ 				},
/******/ 				addStatusHandler: function (l) {
/******/ 					registeredStatusHandlers.push(l);
/******/ 				},
/******/ 				removeStatusHandler: function (l) {
/******/ 					var idx = registeredStatusHandlers.indexOf(l);
/******/ 					if (idx >= 0) registeredStatusHandlers.splice(idx, 1);
/******/ 				},
/******/ 		
/******/ 				// inherit from previous dispose call
/******/ 				data: currentModuleData[moduleId]
/******/ 			};
/******/ 			currentChildModule = undefined;
/******/ 			return hot;
/******/ 		}
/******/ 		
/******/ 		function setStatus(newStatus) {
/******/ 			currentStatus = newStatus;
/******/ 			var results = [];
/******/ 		
/******/ 			for (var i = 0; i < registeredStatusHandlers.length; i++)
/******/ 				results[i] = registeredStatusHandlers[i].call(null, newStatus);
/******/ 		
/******/ 			return Promise.all(results).then(function () {});
/******/ 		}
/******/ 		
/******/ 		function unblock() {
/******/ 			if (--blockingPromises === 0) {
/******/ 				setStatus("ready").then(function () {
/******/ 					if (blockingPromises === 0) {
/******/ 						var list = blockingPromisesWaiting;
/******/ 						blockingPromisesWaiting = [];
/******/ 						for (var i = 0; i < list.length; i++) {
/******/ 							list[i]();
/******/ 						}
/******/ 					}
/******/ 				});
/******/ 			}
/******/ 		}
/******/ 		
/******/ 		function trackBlockingPromise(promise) {
/******/ 			switch (currentStatus) {
/******/ 				case "ready":
/******/ 					setStatus("prepare");
/******/ 				/* fallthrough */
/******/ 				case "prepare":
/******/ 					blockingPromises++;
/******/ 					promise.then(unblock, unblock);
/******/ 					return promise;
/******/ 				default:
/******/ 					return promise;
/******/ 			}
/******/ 		}
/******/ 		
/******/ 		function waitForBlockingPromises(fn) {
/******/ 			if (blockingPromises === 0) return fn();
/******/ 			return new Promise(function (resolve) {
/******/ 				blockingPromisesWaiting.push(function () {
/******/ 					resolve(fn());
/******/ 				});
/******/ 			});
/******/ 		}
/******/ 		
/******/ 		function hotCheck(applyOnUpdate) {
/******/ 			if (currentStatus !== "idle") {
/******/ 				throw new Error("check() is only allowed in idle status");
/******/ 			}
/******/ 			return setStatus("check")
/******/ 				.then(__webpack_require__.hmrM)
/******/ 				.then(function (update) {
/******/ 					if (!update) {
/******/ 						return setStatus(applyInvalidatedModules() ? "ready" : "idle").then(
/******/ 							function () {
/******/ 								return null;
/******/ 							}
/******/ 						);
/******/ 					}
/******/ 		
/******/ 					return setStatus("prepare").then(function () {
/******/ 						var updatedModules = [];
/******/ 						currentUpdateApplyHandlers = [];
/******/ 		
/******/ 						return Promise.all(
/******/ 							Object.keys(__webpack_require__.hmrC).reduce(function (
/******/ 								promises,
/******/ 								key
/******/ 							) {
/******/ 								__webpack_require__.hmrC[key](
/******/ 									update.c,
/******/ 									update.r,
/******/ 									update.m,
/******/ 									promises,
/******/ 									currentUpdateApplyHandlers,
/******/ 									updatedModules
/******/ 								);
/******/ 								return promises;
/******/ 							}, [])
/******/ 						).then(function () {
/******/ 							return waitForBlockingPromises(function () {
/******/ 								if (applyOnUpdate) {
/******/ 									return internalApply(applyOnUpdate);
/******/ 								}
/******/ 								return setStatus("ready").then(function () {
/******/ 									return updatedModules;
/******/ 								});
/******/ 							});
/******/ 						});
/******/ 					});
/******/ 				});
/******/ 		}
/******/ 		
/******/ 		function hotApply(options) {
/******/ 			if (currentStatus !== "ready") {
/******/ 				return Promise.resolve().then(function () {
/******/ 					throw new Error(
/******/ 						"apply() is only allowed in ready status (state: " +
/******/ 							currentStatus +
/******/ 							")"
/******/ 					);
/******/ 				});
/******/ 			}
/******/ 			return internalApply(options);
/******/ 		}
/******/ 		
/******/ 		function internalApply(options) {
/******/ 			options = options || {};
/******/ 		
/******/ 			applyInvalidatedModules();
/******/ 		
/******/ 			var results = currentUpdateApplyHandlers.map(function (handler) {
/******/ 				return handler(options);
/******/ 			});
/******/ 			currentUpdateApplyHandlers = undefined;
/******/ 		
/******/ 			var errors = results
/******/ 				.map(function (r) {
/******/ 					return r.error;
/******/ 				})
/******/ 				.filter(Boolean);
/******/ 		
/******/ 			if (errors.length > 0) {
/******/ 				return setStatus("abort").then(function () {
/******/ 					throw errors[0];
/******/ 				});
/******/ 			}
/******/ 		
/******/ 			// Now in "dispose" phase
/******/ 			var disposePromise = setStatus("dispose");
/******/ 		
/******/ 			results.forEach(function (result) {
/******/ 				if (result.dispose) result.dispose();
/******/ 			});
/******/ 		
/******/ 			// Now in "apply" phase
/******/ 			var applyPromise = setStatus("apply");
/******/ 		
/******/ 			var error;
/******/ 			var reportError = function (err) {
/******/ 				if (!error) error = err;
/******/ 			};
/******/ 		
/******/ 			var outdatedModules = [];
/******/ 			results.forEach(function (result) {
/******/ 				if (result.apply) {
/******/ 					var modules = result.apply(reportError);
/******/ 					if (modules) {
/******/ 						for (var i = 0; i < modules.length; i++) {
/******/ 							outdatedModules.push(modules[i]);
/******/ 						}
/******/ 					}
/******/ 				}
/******/ 			});
/******/ 		
/******/ 			return Promise.all([disposePromise, applyPromise]).then(function () {
/******/ 				// handle errors in accept handlers and self accepted module load
/******/ 				if (error) {
/******/ 					return setStatus("fail").then(function () {
/******/ 						throw error;
/******/ 					});
/******/ 				}
/******/ 		
/******/ 				if (queuedInvalidatedModules) {
/******/ 					return internalApply(options).then(function (list) {
/******/ 						outdatedModules.forEach(function (moduleId) {
/******/ 							if (list.indexOf(moduleId) < 0) list.push(moduleId);
/******/ 						});
/******/ 						return list;
/******/ 					});
/******/ 				}
/******/ 		
/******/ 				return setStatus("idle").then(function () {
/******/ 					return outdatedModules;
/******/ 				});
/******/ 			});
/******/ 		}
/******/ 		
/******/ 		function applyInvalidatedModules() {
/******/ 			if (queuedInvalidatedModules) {
/******/ 				if (!currentUpdateApplyHandlers) currentUpdateApplyHandlers = [];
/******/ 				Object.keys(__webpack_require__.hmrI).forEach(function (key) {
/******/ 					queuedInvalidatedModules.forEach(function (moduleId) {
/******/ 						__webpack_require__.hmrI[key](
/******/ 							moduleId,
/******/ 							currentUpdateApplyHandlers
/******/ 						);
/******/ 					});
/******/ 				});
/******/ 				queuedInvalidatedModules = undefined;
/******/ 				return true;
/******/ 			}
/******/ 		}
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/publicPath */
/******/ 	(() => {
/******/ 		__webpack_require__.p = "/_next/";
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/react refresh */
/******/ 	(() => {
/******/ 		if (__webpack_require__.i) {
/******/ 		__webpack_require__.i.push((options) => {
/******/ 			const originalFactory = options.factory;
/******/ 			options.factory = (moduleObject, moduleExports, webpackRequire) => {
/******/ 				const hasRefresh = typeof self !== "undefined" && !!self.$RefreshInterceptModuleExecution$;
/******/ 				const cleanup = hasRefresh ? self.$RefreshInterceptModuleExecution$(moduleObject.id) : () => {};
/******/ 				try {
/******/ 					originalFactory.call(this, moduleObject, moduleExports, webpackRequire);
/******/ 				} finally {
/******/ 					cleanup();
/******/ 				}
/******/ 			}
/******/ 		})
/******/ 		}
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/compat */
/******/ 	
/******/ 	
/******/ 	// noop fns to prevent runtime errors during initialization
/******/ 	if (typeof self !== "undefined") {
/******/ 		self.$RefreshReg$ = function () {};
/******/ 		self.$RefreshSig$ = function () {
/******/ 			return function (type) {
/******/ 				return type;
/******/ 			};
/******/ 		};
/******/ 	}
/******/ 	
/******/ 	/* webpack/runtime/css loading */
/******/ 	(() => {
/******/ 		var createStylesheet = (chunkId, fullhref, resolve, reject) => {
/******/ 			var linkTag = document.createElement("link");
/******/ 		
/******/ 			linkTag.rel = "stylesheet";
/******/ 			linkTag.type = "text/css";
/******/ 			var onLinkComplete = (event) => {
/******/ 				// avoid mem leaks.
/******/ 				linkTag.onerror = linkTag.onload = null;
/******/ 				if (event.type === 'load') {
/******/ 					resolve();
/******/ 				} else {
/******/ 					var errorType = event && (event.type === 'load' ? 'missing' : event.type);
/******/ 					var realHref = event && event.target && event.target.href || fullhref;
/******/ 					var err = new Error("Loading CSS chunk " + chunkId + " failed.\n(" + realHref + ")");
/******/ 					err.code = "CSS_CHUNK_LOAD_FAILED";
/******/ 					err.type = errorType;
/******/ 					err.request = realHref;
/******/ 					linkTag.parentNode.removeChild(linkTag)
/******/ 					reject(err);
/******/ 				}
/******/ 			}
/******/ 			linkTag.onerror = linkTag.onload = onLinkComplete;
/******/ 			linkTag.href = fullhref;
/******/ 		
/******/ 			(function(linkTag) {
/******/ 			                if (typeof _N_E_STYLE_LOAD === 'function') {
/******/ 			                    const { href, onload, onerror } = linkTag;
/******/ 			                    _N_E_STYLE_LOAD(href.indexOf(window.location.origin) === 0 ? new URL(href).pathname : href).then(()=>onload == null ? void 0 : onload.call(linkTag, {
/******/ 			                            type: 'load'
/******/ 			                        }), ()=>onerror == null ? void 0 : onerror.call(linkTag, {}));
/******/ 			                } else {
/******/ 			                    document.head.appendChild(linkTag);
/******/ 			                }
/******/ 			            })(linkTag)
/******/ 			return linkTag;
/******/ 		};
/******/ 		var findStylesheet = (href, fullhref) => {
/******/ 			var existingLinkTags = document.getElementsByTagName("link");
/******/ 			for(var i = 0; i < existingLinkTags.length; i++) {
/******/ 				var tag = existingLinkTags[i];
/******/ 				var dataHref = tag.getAttribute("data-href") || tag.getAttribute("href");
/******/ 				if(tag.rel === "stylesheet" && (dataHref === href || dataHref === fullhref)) return tag;
/******/ 			}
/******/ 			var existingStyleTags = document.getElementsByTagName("style");
/******/ 			for(var i = 0; i < existingStyleTags.length; i++) {
/******/ 				var tag = existingStyleTags[i];
/******/ 				var dataHref = tag.getAttribute("data-href");
/******/ 				if(dataHref === href || dataHref === fullhref) return tag;
/******/ 			}
/******/ 		};
/******/ 		var loadStylesheet = (chunkId) => {
/******/ 			return new Promise((resolve, reject) => {
/******/ 				var href = __webpack_require__.miniCssF(chunkId);
/******/ 				var fullhref = __webpack_require__.p + href;
/******/ 				if(findStylesheet(href, fullhref)) return resolve();
/******/ 				createStylesheet(chunkId, fullhref, resolve, reject);
/******/ 			});
/******/ 		}
/******/ 		// no chunk loading
/******/ 		
/******/ 		var oldTags = [];
/******/ 		var newTags = [];
/******/ 		var applyHandler = (options) => {
/******/ 			return { dispose: () => {
/******/ 				for(var i = 0; i < oldTags.length; i++) {
/******/ 					var oldTag = oldTags[i];
/******/ 					if(oldTag.parentNode) oldTag.parentNode.removeChild(oldTag);
/******/ 				}
/******/ 				oldTags.length = 0;
/******/ 			}, apply: () => {
/******/ 				for(var i = 0; i < newTags.length; i++) newTags[i].rel = "stylesheet";
/******/ 				newTags.length = 0;
/******/ 			} };
/******/ 		}
/******/ 		__webpack_require__.hmrC.miniCss = (chunkIds, removedChunks, removedModules, promises, applyHandlers, updatedModulesList) => {
/******/ 			applyHandlers.push(applyHandler);
/******/ 			chunkIds.forEach((chunkId) => {
/******/ 				var href = __webpack_require__.miniCssF(chunkId);
/******/ 				var fullhref = __webpack_require__.p + href;
/******/ 				var oldTag = findStylesheet(href, fullhref);
/******/ 				if(!oldTag) return;
/******/ 				promises.push(new Promise((resolve, reject) => {
/******/ 					var tag = createStylesheet(chunkId, fullhref, () => {
/******/ 						tag.as = "style";
/******/ 						tag.rel = "preload";
/******/ 						resolve();
/******/ 					}, reject);
/******/ 					oldTags.push(oldTag);
/******/ 					newTags.push(tag);
/******/ 				}));
/******/ 			});
/******/ 		}
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/importScripts chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded chunks
/******/ 		// "1" means "already loaded"
/******/ 		var installedChunks = __webpack_require__.hmrS_importScripts = __webpack_require__.hmrS_importScripts || {
/******/ 			"_app-pages-browser_workers_logParser_worker_ts-_0cdd0": 1
/******/ 		};
/******/ 		
/******/ 		// no chunk install function needed
/******/ 		// no chunk loading
/******/ 		
/******/ 		function loadUpdateChunk(chunkId, updatedModulesList) {
/******/ 			var success = false;
/******/ 			self["webpackHotUpdate_N_E"] = (_, moreModules, runtime) => {
/******/ 				for(var moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						currentUpdate[moduleId] = moreModules[moduleId];
/******/ 						if(updatedModulesList) updatedModulesList.push(moduleId);
/******/ 					}
/******/ 				}
/******/ 				if(runtime) currentUpdateRuntime.push(runtime);
/******/ 				success = true;
/******/ 			};
/******/ 			// start update chunk loading
/******/ 			importScripts(__webpack_require__.tu(__webpack_require__.p + __webpack_require__.hu(chunkId)));
/******/ 			if(!success) throw new Error("Loading update chunk failed for unknown reason");
/******/ 		}
/******/ 		
/******/ 		var currentUpdateChunks;
/******/ 		var currentUpdate;
/******/ 		var currentUpdateRemovedChunks;
/******/ 		var currentUpdateRuntime;
/******/ 		function applyHandler(options) {
/******/ 			if (__webpack_require__.f) delete __webpack_require__.f.importScriptsHmr;
/******/ 			currentUpdateChunks = undefined;
/******/ 			function getAffectedModuleEffects(updateModuleId) {
/******/ 				var outdatedModules = [updateModuleId];
/******/ 				var outdatedDependencies = {};
/******/ 		
/******/ 				var queue = outdatedModules.map(function (id) {
/******/ 					return {
/******/ 						chain: [id],
/******/ 						id: id
/******/ 					};
/******/ 				});
/******/ 				while (queue.length > 0) {
/******/ 					var queueItem = queue.pop();
/******/ 					var moduleId = queueItem.id;
/******/ 					var chain = queueItem.chain;
/******/ 					var module = __webpack_require__.c[moduleId];
/******/ 					if (
/******/ 						!module ||
/******/ 						(module.hot._selfAccepted && !module.hot._selfInvalidated)
/******/ 					)
/******/ 						continue;
/******/ 					if (module.hot._selfDeclined) {
/******/ 						return {
/******/ 							type: "self-declined",
/******/ 							chain: chain,
/******/ 							moduleId: moduleId
/******/ 						};
/******/ 					}
/******/ 					if (module.hot._main) {
/******/ 						return {
/******/ 							type: "unaccepted",
/******/ 							chain: chain,
/******/ 							moduleId: moduleId
/******/ 						};
/******/ 					}
/******/ 					for (var i = 0; i < module.parents.length; i++) {
/******/ 						var parentId = module.parents[i];
/******/ 						var parent = __webpack_require__.c[parentId];
/******/ 						if (!parent) continue;
/******/ 						if (parent.hot._declinedDependencies[moduleId]) {
/******/ 							return {
/******/ 								type: "declined",
/******/ 								chain: chain.concat([parentId]),
/******/ 								moduleId: moduleId,
/******/ 								parentId: parentId
/******/ 							};
/******/ 						}
/******/ 						if (outdatedModules.indexOf(parentId) !== -1) continue;
/******/ 						if (parent.hot._acceptedDependencies[moduleId]) {
/******/ 							if (!outdatedDependencies[parentId])
/******/ 								outdatedDependencies[parentId] = [];
/******/ 							addAllToSet(outdatedDependencies[parentId], [moduleId]);
/******/ 							continue;
/******/ 						}
/******/ 						delete outdatedDependencies[parentId];
/******/ 						outdatedModules.push(parentId);
/******/ 						queue.push({
/******/ 							chain: chain.concat([parentId]),
/******/ 							id: parentId
/******/ 						});
/******/ 					}
/******/ 				}
/******/ 		
/******/ 				return {
/******/ 					type: "accepted",
/******/ 					moduleId: updateModuleId,
/******/ 					outdatedModules: outdatedModules,
/******/ 					outdatedDependencies: outdatedDependencies
/******/ 				};
/******/ 			}
/******/ 		
/******/ 			function addAllToSet(a, b) {
/******/ 				for (var i = 0; i < b.length; i++) {
/******/ 					var item = b[i];
/******/ 					if (a.indexOf(item) === -1) a.push(item);
/******/ 				}
/******/ 			}
/******/ 		
/******/ 			// at begin all updates modules are outdated
/******/ 			// the "outdated" status can propagate to parents if they don't accept the children
/******/ 			var outdatedDependencies = {};
/******/ 			var outdatedModules = [];
/******/ 			var appliedUpdate = {};
/******/ 		
/******/ 			var warnUnexpectedRequire = function warnUnexpectedRequire(module) {
/******/ 				console.warn(
/******/ 					"[HMR] unexpected require(" + module.id + ") to disposed module"
/******/ 				);
/******/ 			};
/******/ 		
/******/ 			for (var moduleId in currentUpdate) {
/******/ 				if (__webpack_require__.o(currentUpdate, moduleId)) {
/******/ 					var newModuleFactory = currentUpdate[moduleId];
/******/ 					/** @type {TODO} */
/******/ 					var result = newModuleFactory
/******/ 						? getAffectedModuleEffects(moduleId)
/******/ 						: {
/******/ 								type: "disposed",
/******/ 								moduleId: moduleId
/******/ 							};
/******/ 					/** @type {Error|false} */
/******/ 					var abortError = false;
/******/ 					var doApply = false;
/******/ 					var doDispose = false;
/******/ 					var chainInfo = "";
/******/ 					if (result.chain) {
/******/ 						chainInfo = "\nUpdate propagation: " + result.chain.join(" -> ");
/******/ 					}
/******/ 					switch (result.type) {
/******/ 						case "self-declined":
/******/ 							if (options.onDeclined) options.onDeclined(result);
/******/ 							if (!options.ignoreDeclined)
/******/ 								abortError = new Error(
/******/ 									"Aborted because of self decline: " +
/******/ 										result.moduleId +
/******/ 										chainInfo
/******/ 								);
/******/ 							break;
/******/ 						case "declined":
/******/ 							if (options.onDeclined) options.onDeclined(result);
/******/ 							if (!options.ignoreDeclined)
/******/ 								abortError = new Error(
/******/ 									"Aborted because of declined dependency: " +
/******/ 										result.moduleId +
/******/ 										" in " +
/******/ 										result.parentId +
/******/ 										chainInfo
/******/ 								);
/******/ 							break;
/******/ 						case "unaccepted":
/******/ 							if (options.onUnaccepted) options.onUnaccepted(result);
/******/ 							if (!options.ignoreUnaccepted)
/******/ 								abortError = new Error(
/******/ 									"Aborted because " + moduleId + " is not accepted" + chainInfo
/******/ 								);
/******/ 							break;
/******/ 						case "accepted":
/******/ 							if (options.onAccepted) options.onAccepted(result);
/******/ 							doApply = true;
/******/ 							break;
/******/ 						case "disposed":
/******/ 							if (options.onDisposed) options.onDisposed(result);
/******/ 							doDispose = true;
/******/ 							break;
/******/ 						default:
/******/ 							throw new Error("Unexception type " + result.type);
/******/ 					}
/******/ 					if (abortError) {
/******/ 						return {
/******/ 							error: abortError
/******/ 						};
/******/ 					}
/******/ 					if (doApply) {
/******/ 						appliedUpdate[moduleId] = newModuleFactory;
/******/ 						addAllToSet(outdatedModules, result.outdatedModules);
/******/ 						for (moduleId in result.outdatedDependencies) {
/******/ 							if (__webpack_require__.o(result.outdatedDependencies, moduleId)) {
/******/ 								if (!outdatedDependencies[moduleId])
/******/ 									outdatedDependencies[moduleId] = [];
/******/ 								addAllToSet(
/******/ 									outdatedDependencies[moduleId],
/******/ 									result.outdatedDependencies[moduleId]
/******/ 								);
/******/ 							}
/******/ 						}
/******/ 					}
/******/ 					if (doDispose) {
/******/ 						addAllToSet(outdatedModules, [result.moduleId]);
/******/ 						appliedUpdate[moduleId] = warnUnexpectedRequire;
/******/ 					}
/******/ 				}
/******/ 			}
/******/ 			currentUpdate = undefined;
/******/ 		
/******/ 			// Store self accepted outdated modules to require them later by the module system
/******/ 			var outdatedSelfAcceptedModules = [];
/******/ 			for (var j = 0; j < outdatedModules.length; j++) {
/******/ 				var outdatedModuleId = outdatedModules[j];
/******/ 				var module = __webpack_require__.c[outdatedModuleId];
/******/ 				if (
/******/ 					module &&
/******/ 					(module.hot._selfAccepted || module.hot._main) &&
/******/ 					// removed self-accepted modules should not be required
/******/ 					appliedUpdate[outdatedModuleId] !== warnUnexpectedRequire &&
/******/ 					// when called invalidate self-accepting is not possible
/******/ 					!module.hot._selfInvalidated
/******/ 				) {
/******/ 					outdatedSelfAcceptedModules.push({
/******/ 						module: outdatedModuleId,
/******/ 						require: module.hot._requireSelf,
/******/ 						errorHandler: module.hot._selfAccepted
/******/ 					});
/******/ 				}
/******/ 			}
/******/ 		
/******/ 			var moduleOutdatedDependencies;
/******/ 		
/******/ 			return {
/******/ 				dispose: function () {
/******/ 					currentUpdateRemovedChunks.forEach(function (chunkId) {
/******/ 						delete installedChunks[chunkId];
/******/ 					});
/******/ 					currentUpdateRemovedChunks = undefined;
/******/ 		
/******/ 					var idx;
/******/ 					var queue = outdatedModules.slice();
/******/ 					while (queue.length > 0) {
/******/ 						var moduleId = queue.pop();
/******/ 						var module = __webpack_require__.c[moduleId];
/******/ 						if (!module) continue;
/******/ 		
/******/ 						var data = {};
/******/ 		
/******/ 						// Call dispose handlers
/******/ 						var disposeHandlers = module.hot._disposeHandlers;
/******/ 						for (j = 0; j < disposeHandlers.length; j++) {
/******/ 							disposeHandlers[j].call(null, data);
/******/ 						}
/******/ 						__webpack_require__.hmrD[moduleId] = data;
/******/ 		
/******/ 						// disable module (this disables requires from this module)
/******/ 						module.hot.active = false;
/******/ 		
/******/ 						// remove module from cache
/******/ 						delete __webpack_require__.c[moduleId];
/******/ 		
/******/ 						// when disposing there is no need to call dispose handler
/******/ 						delete outdatedDependencies[moduleId];
/******/ 		
/******/ 						// remove "parents" references from all children
/******/ 						for (j = 0; j < module.children.length; j++) {
/******/ 							var child = __webpack_require__.c[module.children[j]];
/******/ 							if (!child) continue;
/******/ 							idx = child.parents.indexOf(moduleId);
/******/ 							if (idx >= 0) {
/******/ 								child.parents.splice(idx, 1);
/******/ 							}
/******/ 						}
/******/ 					}
/******/ 		
/******/ 					// remove outdated dependency from module children
/******/ 					var dependency;
/******/ 					for (var outdatedModuleId in outdatedDependencies) {
/******/ 						if (__webpack_require__.o(outdatedDependencies, outdatedModuleId)) {
/******/ 							module = __webpack_require__.c[outdatedModuleId];
/******/ 							if (module) {
/******/ 								moduleOutdatedDependencies =
/******/ 									outdatedDependencies[outdatedModuleId];
/******/ 								for (j = 0; j < moduleOutdatedDependencies.length; j++) {
/******/ 									dependency = moduleOutdatedDependencies[j];
/******/ 									idx = module.children.indexOf(dependency);
/******/ 									if (idx >= 0) module.children.splice(idx, 1);
/******/ 								}
/******/ 							}
/******/ 						}
/******/ 					}
/******/ 				},
/******/ 				apply: function (reportError) {
/******/ 					// insert new code
/******/ 					for (var updateModuleId in appliedUpdate) {
/******/ 						if (__webpack_require__.o(appliedUpdate, updateModuleId)) {
/******/ 							__webpack_require__.m[updateModuleId] = appliedUpdate[updateModuleId];
/******/ 						}
/******/ 					}
/******/ 		
/******/ 					// run new runtime modules
/******/ 					for (var i = 0; i < currentUpdateRuntime.length; i++) {
/******/ 						currentUpdateRuntime[i](__webpack_require__);
/******/ 					}
/******/ 		
/******/ 					// call accept handlers
/******/ 					for (var outdatedModuleId in outdatedDependencies) {
/******/ 						if (__webpack_require__.o(outdatedDependencies, outdatedModuleId)) {
/******/ 							var module = __webpack_require__.c[outdatedModuleId];
/******/ 							if (module) {
/******/ 								moduleOutdatedDependencies =
/******/ 									outdatedDependencies[outdatedModuleId];
/******/ 								var callbacks = [];
/******/ 								var errorHandlers = [];
/******/ 								var dependenciesForCallbacks = [];
/******/ 								for (var j = 0; j < moduleOutdatedDependencies.length; j++) {
/******/ 									var dependency = moduleOutdatedDependencies[j];
/******/ 									var acceptCallback =
/******/ 										module.hot._acceptedDependencies[dependency];
/******/ 									var errorHandler =
/******/ 										module.hot._acceptedErrorHandlers[dependency];
/******/ 									if (acceptCallback) {
/******/ 										if (callbacks.indexOf(acceptCallback) !== -1) continue;
/******/ 										callbacks.push(acceptCallback);
/******/ 										errorHandlers.push(errorHandler);
/******/ 										dependenciesForCallbacks.push(dependency);
/******/ 									}
/******/ 								}
/******/ 								for (var k = 0; k < callbacks.length; k++) {
/******/ 									try {
/******/ 										callbacks[k].call(null, moduleOutdatedDependencies);
/******/ 									} catch (err) {
/******/ 										if (typeof errorHandlers[k] === "function") {
/******/ 											try {
/******/ 												errorHandlers[k](err, {
/******/ 													moduleId: outdatedModuleId,
/******/ 													dependencyId: dependenciesForCallbacks[k]
/******/ 												});
/******/ 											} catch (err2) {
/******/ 												if (options.onErrored) {
/******/ 													options.onErrored({
/******/ 														type: "accept-error-handler-errored",
/******/ 														moduleId: outdatedModuleId,
/******/ 														dependencyId: dependenciesForCallbacks[k],
/******/ 														error: err2,
/******/ 														originalError: err
/******/ 													});
/******/ 												}
/******/ 												if (!options.ignoreErrored) {
/******/ 													reportError(err2);
/******/ 													reportError(err);
/******/ 												}
/******/ 											}
/******/ 										} else {
/******/ 											if (options.onErrored) {
/******/ 												options.onErrored({
/******/ 													type: "accept-errored",
/******/ 													moduleId: outdatedModuleId,
/******/ 													dependencyId: dependenciesForCallbacks[k],
/******/ 													error: err
/******/ 												});
/******/ 											}
/******/ 											if (!options.ignoreErrored) {
/******/ 												reportError(err);
/******/ 											}
/******/ 										}
/******/ 									}
/******/ 								}
/******/ 							}
/******/ 						}
/******/ 					}
/******/ 		
/******/ 					// Load self accepted modules
/******/ 					for (var o = 0; o < outdatedSelfAcceptedModules.length; o++) {
/******/ 						var item = outdatedSelfAcceptedModules[o];
/******/ 						var moduleId = item.module;
/******/ 						try {
/******/ 							item.require(moduleId);
/******/ 						} catch (err) {
/******/ 							if (typeof item.errorHandler === "function") {
/******/ 								try {
/******/ 									item.errorHandler(err, {
/******/ 										moduleId: moduleId,
/******/ 										module: __webpack_require__.c[moduleId]
/******/ 									});
/******/ 								} catch (err1) {
/******/ 									if (options.onErrored) {
/******/ 										options.onErrored({
/******/ 											type: "self-accept-error-handler-errored",
/******/ 											moduleId: moduleId,
/******/ 											error: err1,
/******/ 											originalError: err
/******/ 										});
/******/ 									}
/******/ 									if (!options.ignoreErrored) {
/******/ 										reportError(err1);
/******/ 										reportError(err);
/******/ 									}
/******/ 								}
/******/ 							} else {
/******/ 								if (options.onErrored) {
/******/ 									options.onErrored({
/******/ 										type: "self-accept-errored",
/******/ 										moduleId: moduleId,
/******/ 										error: err
/******/ 									});
/******/ 								}
/******/ 								if (!options.ignoreErrored) {
/******/ 									reportError(err);
/******/ 								}
/******/ 							}
/******/ 						}
/******/ 					}
/******/ 		
/******/ 					return outdatedModules;
/******/ 				}
/******/ 			};
/******/ 		}
/******/ 		__webpack_require__.hmrI.importScripts = function (moduleId, applyHandlers) {
/******/ 			if (!currentUpdate) {
/******/ 				currentUpdate = {};
/******/ 				currentUpdateRuntime = [];
/******/ 				currentUpdateRemovedChunks = [];
/******/ 				applyHandlers.push(applyHandler);
/******/ 			}
/******/ 			if (!__webpack_require__.o(currentUpdate, moduleId)) {
/******/ 				currentUpdate[moduleId] = __webpack_require__.m[moduleId];
/******/ 			}
/******/ 		};
/******/ 		__webpack_require__.hmrC.importScripts = function (
/******/ 			chunkIds,
/******/ 			removedChunks,
/******/ 			removedModules,
/******/ 			promises,
/******/ 			applyHandlers,
/******/ 			updatedModulesList
/******/ 		) {
/******/ 			applyHandlers.push(applyHandler);
/******/ 			currentUpdateChunks = {};
/******/ 			currentUpdateRemovedChunks = removedChunks;
/******/ 			currentUpdate = removedModules.reduce(function (obj, key) {
/******/ 				obj[key] = false;
/******/ 				return obj;
/******/ 			}, {});
/******/ 			currentUpdateRuntime = [];
/******/ 			chunkIds.forEach(function (chunkId) {
/******/ 				if (
/******/ 					__webpack_require__.o(installedChunks, chunkId) &&
/******/ 					installedChunks[chunkId] !== undefined
/******/ 				) {
/******/ 					promises.push(loadUpdateChunk(chunkId, updatedModulesList));
/******/ 					currentUpdateChunks[chunkId] = true;
/******/ 				} else {
/******/ 					currentUpdateChunks[chunkId] = false;
/******/ 				}
/******/ 			});
/******/ 			if (__webpack_require__.f) {
/******/ 				__webpack_require__.f.importScriptsHmr = function (chunkId, promises) {
/******/ 					if (
/******/ 						currentUpdateChunks &&
/******/ 						__webpack_require__.o(currentUpdateChunks, chunkId) &&
/******/ 						!currentUpdateChunks[chunkId]
/******/ 					) {
/******/ 						promises.push(loadUpdateChunk(chunkId));
/******/ 						currentUpdateChunks[chunkId] = true;
/******/ 					}
/******/ 				};
/******/ 			}
/******/ 		};
/******/ 		
/******/ 		__webpack_require__.hmrM = () => {
/******/ 			if (typeof fetch === "undefined") throw new Error("No browser support: need fetch API");
/******/ 			return fetch(__webpack_require__.p + __webpack_require__.hmrF()).then((response) => {
/******/ 				if(response.status === 404) return; // no update available
/******/ 				if(!response.ok) throw new Error("Failed to fetch update manifest " + response.statusText);
/******/ 				return response.json();
/******/ 			});
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// module cache are used so entry inlining is disabled
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	var __webpack_exports__ = __webpack_require__("(app-pages-browser)/./workers/logParser.worker.ts");
/******/ 	_N_E = __webpack_exports__;
/******/ 	
/******/ })()
;