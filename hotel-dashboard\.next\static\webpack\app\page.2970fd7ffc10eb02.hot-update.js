"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx":
/*!***********************************************!*\
  !*** ./app/(dashboard)/log-analysis/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogAnalysisPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/log-analysis/LogDisplayArea */ \"(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\");\n/* harmony import */ var _components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/log-analysis/LogChartView */ \"(app-pages-browser)/./components/log-analysis/LogChartView.tsx\");\n/* harmony import */ var _components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/log-analysis/LogFileUpload */ \"(app-pages-browser)/./components/log-analysis/LogFileUpload.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_exportUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/exportUtils */ \"(app-pages-browser)/./lib/exportUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LogAnalysisPage() {\n    _s();\n    const [dataChunks, setDataChunks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedBlocksForChart, setSelectedBlocksForChart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExportingMode, setIsExportingMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [blocksToRenderForExport, setBlocksToRenderForExport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const exportTargetContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const handleProcessingStart = ()=>{\n        console.log('[LogAnalysisPage] handleProcessingStart - START');\n        setIsLoading(true);\n        setError(null);\n        setSelectedBlocksForChart([]);\n        console.log('[LogAnalysisPage] handleProcessingStart - END');\n    };\n    const handleDataProcessed = (workerData)=>{\n        console.log('[LogAnalysisPage] handleDataProcessed - START. Received data count:', workerData.length);\n        const processedData = workerData.map((block)=>({\n                ...block,\n                data: [] // Initialize empty data array, can be populated later if needed\n            }));\n        setDataChunks(processedData);\n        setIsLoading(false);\n        toast({\n            title: \"数据已加载\",\n            description: \"成功处理了 \".concat(workerData.length, \" 个数据块。\")\n        });\n        console.log('[LogAnalysisPage] handleDataProcessed - END');\n    };\n    const handleError = (errorMessage)=>{\n        console.log('[LogAnalysisPage] handleError - START. Received errorMessage:', errorMessage);\n        setError(errorMessage);\n        setIsLoading(false);\n        console.log('[LogAnalysisPage] handleError - END');\n    };\n    const handleBlockSelectionChanged = (selectedBlocks)=>{\n        console.log('[LogAnalysisPage] handleBlockSelectionChanged - START. Received selectedBlocks count:', selectedBlocks.length);\n        setSelectedBlocksForChart(selectedBlocks);\n        console.log('[LogAnalysisPage] handleBlockSelectionChanged - END');\n    };\n    const handleBlockSelect = (blockId)=>{\n        console.log('[LogAnalysisPage] handleBlockSelect - START. Received blockId:', blockId);\n        const block = dataChunks.find((b)=>b.block_id === blockId);\n        if (block) {\n            setSelectedBlocksForChart([\n                block\n            ]);\n        }\n        console.log('[LogAnalysisPage] handleBlockSelect - END');\n    };\n    const chartDataForView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[chartDataForView]\": ()=>{\n            console.log('[LogAnalysisPage] Recalculating chartDataForView. Selected blocks count:', selectedBlocksForChart.length);\n            return selectedBlocksForChart;\n        }\n    }[\"LogAnalysisPage.useMemo[chartDataForView]\"], [\n        selectedBlocksForChart\n    ]);\n    const initiateExportProcess = (exportIds)=>{\n        console.log('[LogAnalysisPage] initiateExportProcess - START. exportIds:', exportIds);\n        const blocksToExport = dataChunks.filter((block)=>exportIds.includes(block.block_id));\n        if (blocksToExport.length === 0) {\n            toast({\n                title: \"导出错误\",\n                description: \"没有找到要导出的数据块。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setBlocksToRenderForExport(blocksToExport);\n        setIsExportingMode(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (isExportingMode && blocksToRenderForExport.length > 0 && exportTargetContainerRef.current) {\n                const allSelectedIds = blocksToRenderForExport.map({\n                    \"LogAnalysisPage.useEffect.allSelectedIds\": (block)=>block.block_id\n                }[\"LogAnalysisPage.useEffect.allSelectedIds\"]);\n                console.log('[LogAnalysisPage] Starting ZIP export for block IDs:', allSelectedIds);\n                // 等待图表渲染完成后再开始导出\n                const startExport = {\n                    \"LogAnalysisPage.useEffect.startExport\": async ()=>{\n                        const container = exportTargetContainerRef.current;\n                        if (!container) {\n                            throw new Error('导出容器未找到');\n                        }\n                        // 智能等待：检查所有图表是否都已渲染完成\n                        const waitForAllChartsToRender = {\n                            \"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender\": async ()=>{\n                                const maxWaitTime = 30000; // 最大等待30秒\n                                const checkInterval = 500; // 每500ms检查一次\n                                let waitedTime = 0;\n                                while(waitedTime < maxWaitTime){\n                                    const renderedElements = container.querySelectorAll('[data-block-id]');\n                                    const renderedIds = Array.from(renderedElements).map({\n                                        \"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender.renderedIds\": (el)=>el.getAttribute('data-block-id')\n                                    }[\"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender.renderedIds\"]);\n                                    console.log(\"[LogAnalysisPage] Waiting for charts... Found \".concat(renderedElements.length, \" elements:\"), renderedIds);\n                                    // 检查是否所有需要的图表都已渲染\n                                    const allRendered = allSelectedIds.every({\n                                        \"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender.allRendered\": (blockId)=>renderedIds.includes(blockId)\n                                    }[\"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender.allRendered\"]);\n                                    if (allRendered) {\n                                        // 再等待一点时间确保图表内容完全加载\n                                        await new Promise({\n                                            \"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender\": (resolve)=>setTimeout(resolve, 1000)\n                                        }[\"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender\"]);\n                                        console.log('[LogAnalysisPage] All charts rendered successfully');\n                                        return;\n                                    }\n                                    await new Promise({\n                                        \"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender\": (resolve)=>setTimeout(resolve, checkInterval)\n                                    }[\"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender\"]);\n                                    waitedTime += checkInterval;\n                                }\n                                throw new Error(\"等待图表渲染超时。需要: \".concat(allSelectedIds.join(', '), \", 已渲染: \").concat(Array.from(container.querySelectorAll('[data-block-id]')).map({\n                                    \"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender\": (el)=>el.getAttribute('data-block-id')\n                                }[\"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender\"]).join(', ')));\n                            }\n                        }[\"LogAnalysisPage.useEffect.startExport.waitForAllChartsToRender\"];\n                        await waitForAllChartsToRender();\n                        return (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_7__.exportElementAsImage)(container, \"exported_log_charts\", allSelectedIds, {\n                            \"LogAnalysisPage.useEffect.startExport\": (progress)=>{\n                                console.log(\"[LogAnalysisPage] Export progress: \".concat(progress.toFixed(2), \"%\"));\n                            }\n                        }[\"LogAnalysisPage.useEffect.startExport\"]);\n                    }\n                }[\"LogAnalysisPage.useEffect.startExport\"];\n                startExport().then({\n                    \"LogAnalysisPage.useEffect\": ()=>{\n                        toast({\n                            title: \"导出成功\",\n                            description: \"所有选中的图表已成功导出。\"\n                        });\n                    }\n                }[\"LogAnalysisPage.useEffect\"]).catch({\n                    \"LogAnalysisPage.useEffect\": (error)=>{\n                        console.error('[LogAnalysisPage] Export failed:', error);\n                        toast({\n                            title: \"导出失败\",\n                            description: error instanceof Error ? error.message : \"导出过程中发生错误。\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }[\"LogAnalysisPage.useEffect\"]).finally({\n                    \"LogAnalysisPage.useEffect\": ()=>{\n                        setIsExportingMode(false);\n                        setBlocksToRenderForExport([]);\n                    }\n                }[\"LogAnalysisPage.useEffect\"]);\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        isExportingMode,\n        blocksToRenderForExport,\n        dataChunks\n    ]);\n    console.log('[LogAnalysisPage] Rendering. isLoading:', isLoading, 'error:', error, 'dataChunks count:', dataChunks.length, 'selectedBlocksForChart count:', selectedBlocksForChart.length, 'chartDataForView count:', chartDataForView.length);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"日志分析\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onProcessingStart: handleProcessingStart,\n                            onDataProcessed: handleDataProcessed,\n                            onError: handleError,\n                            disabled: isLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            dataChunks: dataChunks,\n                            onSelectionChange: handleBlockSelectionChanged,\n                            onStartExport: initiateExportProcess\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow mt-4\",\n                children: [\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"正在处理文件...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"h-full flex items-center justify-center bg-destructive/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-destructive font-semibold\",\n                                    children: \"发生错误\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    !isLoading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: exportTargetContainerRef,\n                        className: \"h-full\",\n                        children: isExportingMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            dataChunks: blocksToRenderForExport,\n                            selectedBlockIds: blocksToRenderForExport.map((b)=>b.block_id),\n                            onBlockSelect: handleBlockSelect\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 15\n                        }, this) : chartDataForView.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            dataChunks: chartDataForView,\n                            selectedBlockIds: chartDataForView.map((b)=>b.block_id),\n                            onBlockSelect: handleBlockSelect\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: dataChunks.length > 0 ? \"请从左侧选择数据块以显示图表\" : \"请先上传日志文件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(LogAnalysisPage, \"MTEH1qUTRvyQTASQZTtlZZfcoEs=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = LogAnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"LogAnalysisPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx\n"));

/***/ })

});