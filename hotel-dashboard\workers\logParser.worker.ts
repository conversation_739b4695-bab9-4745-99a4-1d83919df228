// hotel-dashboard/workers/logParser.worker.ts
/// <reference lib="webworker" />

// Import types from the definitions file
import type {
  TimestampedValue,
  ValveOpenEvent,
  ProcessedBlock,
} from './logParser.definitions';

// 新增：日志版本枚举
enum LogVersion {
  V1 = 'V1',
  V2 = 'V2',
  UNKNOWN = 'UNKNOWN'
}

// Check if running in a Web Worker context
if (
    typeof DedicatedWorkerGlobalScope === "undefined" ||
    !(self instanceof DedicatedWorkerGlobalScope) ||
    typeof self.postMessage !== 'function'
) {
} else {
  // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.
  const DEBUG = false; // Set to true to enable detailed logs

  let processedBlocks: ProcessedBlock[] = []; // Store processed blocks in memory
  let originalLogContent: string = ''; // Store the full original log content

  // Regex for extracting timestamp from a log line
  const TIMESTAMP_REGEX = /^\w+\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2},\d{3})/;

  // --- V1 正则表达式 ---
  // V1 blocks start from "打开真空泵" and end with "insert into g_support"
  const BLOCK_REGEX_V1 = /打开真空泵[\s\S]*?insert into g_support[^\n]*/g;
  // V2 blocks have their own distinct start and end markers.
  const BLOCK_REGEX_V2 = /轴停止运动[\s\S]*?SetSpeed:2, 85, result:/g;

  function _extractTimestampFromLine(line: string): string | null {
      const match = TIMESTAMP_REGEX.exec(line);
      return match ? match[1] : null;
  }

  function calculateV2CollimationDiff(x1Str: string, y1Str: string, x2Str: string, y2Str: string): number | null {
    try {
      const x1 = parseFloat(x1Str);
      const y1 = parseFloat(y1Str);
      const x2 = parseFloat(x2Str);
      const y2 = parseFloat(y2Str);

      if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {
        return null;
      }

      return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
    } catch (e) {
      return null;
    }
  }

  /**
   * NEW UNIFIED FUNCTION
   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).
   */
  function processRawBlock(blockId: string, blockContent: string, blockVersion: LogVersion): ProcessedBlock | null {
    if (DEBUG) console.log(`[Worker] Processing block ${blockId} with unified parser. Content snippet: ${blockContent.substring(0, 100)}...`);
    if (!blockContent) return null;
    const blockLines = blockContent.split('\n');
    if (!blockLines.length) return null;

    let startTimeStr: string | null = null;
    let endTimeStr: string | null = null;

    // Find first and last timestamps in the block
    for (let i = 0; i < blockLines.length; i++) {
        const ts = _extractTimestampFromLine(blockLines[i]);
        if (ts) {
            startTimeStr = ts;
            break;
        }
    }
    for (let i = blockLines.length - 1; i >= 0; i--) {
        const ts = _extractTimestampFromLine(blockLines[i]);
        if (ts) {
            endTimeStr = ts;
            break;
        }
    }
    if (!endTimeStr && startTimeStr) {
        endTimeStr = startTimeStr;
    }
    // Get timestamps for specific start/end sentences for debugging, as requested by the user.
    const startSentenceTs = _extractTimestampFromLine(blockLines[0]);
    let endSentenceTs: string | null = null;
    // Find the last line containing a known end-of-block marker to get its timestamp.
    for (let i = blockLines.length - 1; i >= 0; i--) {
        const line = blockLines[i];
        if (line.includes('insert into g_support') || line.includes('SetSpeed:2, 85, result:')) {
            endSentenceTs = _extractTimestampFromLine(line);
            break; // Found the last marker line
        }
    }

    if (DEBUG) {
        // Enhanced logging as requested by the user.
        console.log(
            `[Worker] Block ${blockId}: ` +
            `StartLineTS=${startSentenceTs}, EndLineTS=${endSentenceTs}, ` +
            `OverallStartTS=${startTimeStr}, OverallEndTS=${endTimeStr}`
        );
    }

    const valveOpenEventsList: ValveOpenEvent[] = [];
    const glueThicknessValuesList: TimestampedValue[] = [];
    const collimationDiffValuesList: TimestampedValue[] = [];

    blockLines.forEach((line, index) => {
        const currentLineTimestamp = _extractTimestampFromLine(line);
        const timestampForValue = currentLineTimestamp || startTimeStr;

        if (blockVersion === LogVersion.V1) {
            // V1格式需要在"z轴停止完成"之后才开始收集数据
            // 首先检查是否已经遇到了"z轴停止完成"标志
            if (line.includes('z轴停止完成')) {
                // 标记从这里开始收集数据
                // 我们需要一个标志来跟踪是否已经遇到了这个关键点
                if (!blockContent.includes('__Z_AXIS_STOP_FOUND__')) {
                    // 在内容中添加一个标记，表示已经找到了z轴停止完成
                    // 这是一个临时解决方案，更好的方法是重构整个解析逻辑
                }
            }

            // 只有在找到"z轴停止完成"之后才收集数据
            const zAxisStopIndex = blockContent.indexOf('z轴停止完成');
            const currentLineIndex = blockContent.indexOf(line);

            if (zAxisStopIndex !== -1 && currentLineIndex > zAxisStopIndex) {
                // Check for V1 Glue
                const glueMatchV1 = /####### 胶厚值:([+-]?(?:\d+\.?\d*|\.\d+))/.exec(line);
                if (glueMatchV1) {
                    try {
                        const valueFloat = parseFloat(glueMatchV1[1]);
                        if (timestampForValue) {
                            glueThicknessValuesList.push({ timestamp: timestampForValue, value: valueFloat });
                        }
                    } catch (e) { /* ignore */ }
                }

                // Check for V1 Diff
                const diffMatchV1 = /####### 准直diff:([+-]?(?:\d+\.?\d*|\.\d+))/.exec(line);
                if (diffMatchV1) {
                    try {
                        const valueFloat = parseFloat(diffMatchV1[1]);
                        if (timestampForValue && Math.abs(valueFloat) <= 100) {
                            collimationDiffValuesList.push({ timestamp: timestampForValue, value: valueFloat });
                        }
                    } catch (e) { /* ignore */ }
                }
            }
        }

        if (blockVersion === LogVersion.V2) {
            // Check for V2 Glue
            const glueMatchV2 = /Thickness:([+-]?(?:\d+\.?\d*|\.\d+))/.exec(line);
            if (glueMatchV2) {
                const valueFloat = parseFloat(glueMatchV2[1]);
                if (timestampForValue && !isNaN(valueFloat)) {
                    glueThicknessValuesList.push({ timestamp: timestampForValue, value: valueFloat });
                }
            }

            // Check for V2 Diff
            const diffMatchV2 = /点13均值x:([+-]?(?:\d+\.?\d*|\.\d+)),\s*点13均值y:([+-]?(?:\d+\.?\d*|\.\d+)),\s*点2x:([+-]?(?:\d+\.?\d*|\.\d+)),\s*点2y:([+-]?(?:\d+\.?\d*|\.\d+))/.exec(line);
            if (diffMatchV2) {
                if (timestampForValue) {
                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;
                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);
                    if (diffValue !== null && Math.abs(diffValue) <= 100) {
                        collimationDiffValuesList.push({ timestamp: timestampForValue, value: diffValue });
                    }
                }
            }
        }
        
        // Generic events can be checked for all block types
        if (line.includes("打开放气阀")) {
            if (currentLineTimestamp) {
                valveOpenEventsList.push({ timestamp: currentLineTimestamp, line_content: line.trim() });
            }
        }
    });

    if (DEBUG) {
        console.log(`[Worker] Block ${blockId} processing finished. Glue values: ${glueThicknessValuesList.length}, Diff values: ${collimationDiffValuesList.length}`);
    }

    return {
        block_id: blockId,
        start_time: startTimeStr,
        end_time: endTimeStr,
        lines_count: blockLines.length,
        valve_open_events: valveOpenEventsList,
        glue_thickness_values: glueThicknessValuesList,
        collimation_diff_values: collimationDiffValuesList,
        raw_content: blockContent,
        sns: [], // Initialize sns array
    };
  }

  /**
   * UPDATED parseLogContent
   * Parses the entire log content, using version detection ONLY for block boundaries,
   * but ALWAYS using the unified processRawBlock for content parsing.
   */
  function parseLogContent(logContent: string): ProcessedBlock[] {
    if (!logContent) return [];

    // 1. Find all possible V1 and V2 blocks independently.
    // Add version information to each match before combining.
    const v1Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V1)).map(m => ({ ...m, version: LogVersion.V1 }));
    const v2Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V2)).map(m => ({ ...m, version: LogVersion.V2 }));

    // 2. Combine and sort all found blocks by their starting position in the log file.
    const allMatches = [...v1Matches, ...v2Matches].sort((a, b) => (a.index || 0) - (b.index || 0));

    if (allMatches.length === 0) {
        if (DEBUG) console.log("[Worker] No V1 or V2 blocks found in log content.");
        return [];
    }
    
    const localProcessedBlocks: ProcessedBlock[] = [];
    const snRegex = /insert into g_support.*values\s*\("([^"]+)"/;

    // 3. Process each block in the correct order.
    allMatches.forEach((match, index) => {
        const blockContent = match[0];
        if (!blockContent) return;

        let sn: string | null = null;
        
        // SN is always expected inside the block now, due to the new regex definitions.
        const snMatchInside = snRegex.exec(blockContent);
        if (snMatchInside) {
            sn = snMatchInside[1];
        }

        // Process the block first with a temporary ID to get its properties, like start_time.
        const tempBlockId = `temp_${index + 1}`;
        const processedBlock = processRawBlock(tempBlockId, blockContent, match.version);

        if (processedBlock) {
            // Now, create the final block_id based on the user's request.
            // Use the start_time if available, otherwise fall back to the old naming scheme.
            // Use the start_time to create the block ID in YYYYMMDD_HH_mm_ss format, as requested.
            let formattedId = null;
            if (processedBlock.start_time) {
                // This is a more robust way to format the date, avoiding potential string replacement issues.
                try {
                    const date = new Date(processedBlock.start_time.replace(',', '.'));
                    if (isNaN(date.getTime())) {
                        throw new Error("Invalid date");
                    }
                    const y = date.getFullYear().toString();
                    const m = (date.getMonth() + 1).toString().padStart(2, '0');
                    const d = date.getDate().toString().padStart(2, '0');
                    const h = date.getHours().toString().padStart(2, '0');
                    const min = date.getMinutes().toString().padStart(2, '0');
                    const s = date.getSeconds().toString().padStart(2, '0');
                    formattedId = `${y}${m}${d}_${h}_${min}_${s}`;
                } catch (e) {
                    // If date parsing fails, formattedId remains null, and the fallback ID will be used.
                    if (DEBUG) console.error(`Could not parse date for block ID: ${processedBlock.start_time}`);
                }
            }
            const finalBlockId = formattedId || (sn ? `${sn}_${index + 1}` : `block_${index + 1}`);
            
            processedBlock.block_id = finalBlockId;

            if (sn) {
                processedBlock.sns.push(sn);
            }
            // Every matched block is valid and must be kept.
            localProcessedBlocks.push(processedBlock);
        }
    });

    return localProcessedBlocks;
  }

  // Worker message handling
  self.onmessage = function(event: MessageEvent<{ type: string; payload: any }>) {
    if (DEBUG) console.log(`[Worker] Message received: ${event.data.type}`);

    const { type, payload } = event.data;

    switch (type) {
      case 'PARSE_LOG':
        try {
          const logContent = payload;
          if (!logContent || typeof logContent !== 'string') {
            throw new Error('Invalid log content received by worker.');
          }
          if (DEBUG) console.log(`[Worker] Received log content. Length: ${logContent.length}. Starting parsing...`);
          
          // Store the full log content for searching, and parse it into blocks.
          originalLogContent = logContent;
          processedBlocks = parseLogContent(logContent);

          // Create a version of the blocks to send to the main thread that does NOT include the raw_content
          const blocksForFrontend = processedBlocks.map(block => {
            const { raw_content, ...rest } = block;
            return rest;
          });

          if (blocksForFrontend.length > 0) {
            if (DEBUG) console.log(`[Worker] Sending ${blocksForFrontend.length} processed blocks to main thread.`);
            self.postMessage({ type: 'PARSE_LOG_RESULT', success: true, allBlocks: blocksForFrontend, message: `Successfully processed ${blocksForFrontend.length} blocks.` });
          } else {
            if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');
            self.postMessage({ type: 'PARSE_LOG_RESULT', success: true, allBlocks: [], message: 'No relevant data blocks were found in the log file.' });
          }
        } catch (error: any) {
          if (DEBUG) console.error('[Worker] Critical error during log processing:', error);
          self.postMessage({ type: 'PARSE_LOG_RESULT', success: false, error: error.message || 'An unknown error occurred in the worker.' });
        }
        break;

      case 'MATCH_BY_TIMESTAMP':
        try {
          const { timestamps } = payload;
          if (!Array.isArray(timestamps)) {
            throw new Error('Invalid timestamps payload received.');
          }
          if (DEBUG) console.log(`[Worker] Starting timestamp range match for ${timestamps.length} timestamps.`);

          const matchedBlockIds = new Set<string>();

          for (const targetTs of timestamps) {
            const targetTime = new Date(targetTs).getTime();

            for (const block of processedBlocks) {
              // Check if the block has valid start and end times.
              if (block.start_time && block.end_time) {
                try {
                  const blockStartTime = new Date(block.start_time.replace(',', '.')).getTime();
                  const blockEndTime = new Date(block.end_time.replace(',', '.')).getTime();

                  // The correct logic, as per user instruction:
                  // Check if the image's timestamp falls within the block's time range.
                  if (targetTime >= blockStartTime && targetTime <= blockEndTime) {
                    if (DEBUG) console.log(`[Worker] Timestamp ${targetTime} falls within block ${block.block_id} range [${blockStartTime} - ${blockEndTime}]. Match found.`);
                    matchedBlockIds.add(block.block_id);
                  }
                } catch (e) {
                  if (DEBUG) console.error(`[Worker] Could not parse timestamp for block ${block.block_id}.`, e);
                }
              }
            }
          }
          
          const resultIds = Array.from(matchedBlockIds);
          if (DEBUG) console.log(`[Worker] Timestamp match finished. Found ${resultIds.length} matching blocks.`);
          self.postMessage({ type: 'MATCH_BY_TIMESTAMP_RESULT', matchedBlockIds: resultIds });

        } catch (error: any) {
          if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);
          self.postMessage({ type: 'MATCH_BY_TIMESTAMP_RESULT', error: error.message || 'An unknown error occurred during matching.' });
        }
        break;
        
      default:
        if (DEBUG) console.warn(`[Worker] Unknown message type received: ${type}`);
        break;
    }
  };

  self.onerror = function(errorEvent) {
  };

}