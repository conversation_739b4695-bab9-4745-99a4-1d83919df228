"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    const BLOCK_REGEX_V1 = /(?:开始|打开)抽真空(?:(?!打开真空泵|开始抽真空)[\\s\\S])*?insert into g_support/g;\n    // --- V2 正则表达式 ---\n    const BLOCK_REGEX_V2 = /轴停止运动(?:(?!轴停止运动)[\\s\\S])*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function detectLogVersion(logContent) {\n        if (logContent.includes(\"轴停止运动\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V2');\n            return \"V2\";\n        }\n        if (logContent.includes(\"打开真空泵\") || logContent.includes(\"开始抽真空\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V1');\n            return \"V1\";\n        }\n        if (DEBUG) console.warn('[Worker] Could not determine log version.');\n        return \"UNKNOWN\";\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const version = detectLogVersion(logContent);\n        if (version === \"UNKNOWN\") {\n            throw new Error('Unknown log file version. Could not parse.');\n        }\n        const blockRegex = version === \"V1\" ? BLOCK_REGEX_V1 : BLOCK_REGEX_V2;\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        let match;\n        let lastIndex = 0;\n        let iterationCount = 0;\n        const MAX_ITERATIONS = 100000; // Safety break to prevent infinite loops\n        // Manually iterate with exec() to have more control and prevent crashes\n        while((match = blockRegex.exec(logContent)) !== null){\n            iterationCount++;\n            if (iterationCount > MAX_ITERATIONS) {\n                self.postMessage({\n                    type: 'PARSE_LOG_RESULT',\n                    success: false,\n                    error: 'Infinite loop detected during log parsing. Check log format and block delimiters.'\n                });\n                return []; // Stop processing\n            }\n            // This check prevents infinite loops on zero-length matches\n            if (match.index === blockRegex.lastIndex) {\n                blockRegex.lastIndex++;\n            }\n            const blockContent = match[0];\n            if (!blockContent || typeof match.index === 'undefined') continue;\n            let sn = null;\n            // 1. Try to find SN inside the block first\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n                if (DEBUG) console.log(\"[Worker] Found SN inside block \".concat(iterationCount, \": \").concat(sn));\n            } else {\n                // 2. If not found, search for the next SN *after* this block\n                const endOfCurrentBlock = match.index + blockContent.length;\n                const searchArea = logContent.substring(endOfCurrentBlock); // Search from end of block to end of file\n                const snMatchOutside = snRegex.exec(searchArea);\n                if (snMatchOutside) {\n                    // To avoid altering the main loop's regex state, create a new regex for peeking.\n                    const peekRegex = new RegExp(blockRegex.source, blockRegex.flags);\n                    peekRegex.lastIndex = endOfCurrentBlock; // Start peeking from the end of the current block\n                    const nextBlockMatch = peekRegex.exec(logContent);\n                    // Check if the found SN is located *before* the start of the next block.\n                    if (!nextBlockMatch || endOfCurrentBlock + snMatchOutside.index < nextBlockMatch.index) {\n                        sn = snMatchOutside[1];\n                        if (DEBUG) console.log(\"[Worker] Found SN outside block \".concat(iterationCount, \": \").concat(sn));\n                    } else {\n                        // The SN found belongs to a later block, so ignore it for this one.\n                        if (DEBUG) console.log(\"[Worker] SN found outside block \".concat(iterationCount, \", but it belongs to a subsequent block. Ignoring.\"));\n                    }\n                }\n            }\n            // Ensure lastIndex is correctly updated after every iteration to prevent infinite loops.\n            blockRegex.lastIndex = match.index + blockContent.length;\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(iterationCount) : \"block_\".concat(iterationCount);\n            if (DEBUG) console.log(\"[Worker] Final Block ID for index \".concat(iterationCount - 1, \": \").concat(blockId));\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0) {\n                    localProcessedBlocks.push(processedBlock);\n                }\n            }\n            lastIndex = blockRegex.lastIndex;\n        }\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the parsed blocks globally in the worker\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"抽真空\"; // Keyword is kept generic. The main fix is the block regex.\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        for (const block of processedBlocks){\n                            // Avoid re-checking a block that already has a match\n                            if (matchedBlockIds.has(block.block_id)) {\n                                continue;\n                            }\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            // Timestamps are in 'YYYY-MM-DD HH:mm:ss,SSS' format\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {\n                                        // Ignore lines with invalid date formats\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("69ec41770d255cf2")
/******/ })();
/******/ 
/******/ }
);