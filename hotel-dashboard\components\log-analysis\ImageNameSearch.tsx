"use client";

import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ImageNameSearchProps {
  onSearch: (timestamps: number[]) => void;
  disabled?: boolean;
  isLoading?: boolean;
}

const ImageNameSearch = ({ onSearch, disabled, isLoading }: ImageNameSearchProps) => {
  const [inputText, setInputText] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleSearch = () => {
    setError(null);
    const fileNames = inputText.split("\n").filter((name) => name.trim() !== "");
    if (fileNames.length === 0) {
      setError("Please enter at least one file name.");
      return;
    }

    const timestamps: number[] = [];
    let hasError = false;

    fileNames.forEach((fileName) => {
      const match = fileName.match(/^(\d{8}_\d{2}_\d{2}_\d{2})GBSN/);
      if (match) {
        const parts = match[1].split("_");
        if (parts.length !== 4) return; // Ensure we have date, hour, minute, second

        const [datePart, hourStr, minuteStr, secondStr] = parts;
        
        const year = parseInt(datePart.substring(0, 4), 10);
        const month = parseInt(datePart.substring(4, 6), 10) - 1; // Month is 0-indexed
        const day = parseInt(datePart.substring(6, 8), 10);
        const hour = parseInt(hourStr, 10);
        const minute = parseInt(minuteStr, 10);
        const second = parseInt(secondStr, 10);
        
        const date = new Date(year, month, day, hour, minute, second);
        // Check if the date is valid
        if (!isNaN(date.getTime())) {
          // The worker expects timestamps in milliseconds, not seconds
          timestamps.push(date.getTime());
        } else {
          hasError = true;
        }
      } else {
        hasError = true;
      }
    });

    if (hasError) {
      setError("One or more file names could not be parsed. Please ensure they follow the correct format (e.g., YYYYMMDD_HH_mm_ssGBSNxxxxxx.png) and that there are no extra characters or lines.");
    }
    
    if (timestamps.length > 0) {
        onSearch(timestamps);
    } else if (!hasError) {
        setError("No valid timestamps could be extracted. Please check the format.");
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid w-full gap-1.5">
        <Label htmlFor="image-names">Image File Names</Label>
        <Textarea
          id="image-names"
          placeholder="Paste image file names here, one per line..."
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          rows={5}
          disabled={disabled || isLoading}
        />
        <p className="text-sm text-muted-foreground">
          Example: `20250629_02_24_54GBSN888888.png`
        </p>
      </div>
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <Button onClick={handleSearch} disabled={disabled || isLoading}>
        {isLoading ? "Searching..." : "Search by Image Names"}
      </Button>
    </div>
  );
};

export default ImageNameSearch;