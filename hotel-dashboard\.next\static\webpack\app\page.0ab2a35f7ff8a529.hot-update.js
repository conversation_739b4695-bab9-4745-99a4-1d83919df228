"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx":
/*!****************************************************!*\
  !*** ./components/log-analysis/LogDisplayArea.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// LogChartView is removed as it will be handled by the parent page\n\n\n\n\n\n\n\nconst LogDisplayArea = (param)=>{\n    let { dataChunks, onSelectionChange, onStartExport } = param;\n    _s();\n    console.log('[LogDisplayArea] Rendering. ProcessedDataChunks count:', dataChunks.length);\n    const [selectedBlockId, setSelectedBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''); // 单选显示\n    const [exportBlockIds, setExportBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 多选导出\n    const displayAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isExportingAll, setIsExportingAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exportProgress, setExportProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const onSelectionChangeRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(onSelectionChange);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            onSelectionChangeRef.current = onSelectionChange;\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        onSelectionChange\n    ]);\n    // 当有新的数据块时，自动选择第一个\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            console.log('[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:', dataChunks.length, 'current selectedBlockId:', selectedBlockId);\n            if (dataChunks.length === 0) {\n                if (selectedBlockId !== '') {\n                    console.log('[LogDisplayArea] No data chunks, clearing selectedBlockId.');\n                    setSelectedBlockId('');\n                }\n            } else {\n                const currentSelectionIsValid = selectedBlockId && selectedBlockId.trim() !== '' && dataChunks.some({\n                    \"LogDisplayArea.useEffect\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect\"]);\n                if (!currentSelectionIsValid) {\n                    console.log('[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.');\n                    const firstValidBlock = dataChunks.find({\n                        \"LogDisplayArea.useEffect.firstValidBlock\": (chunk)=>chunk.block_id && typeof chunk.block_id === 'string' && chunk.block_id.trim() !== ''\n                    }[\"LogDisplayArea.useEffect.firstValidBlock\"]);\n                    if (firstValidBlock) {\n                        console.log('[LogDisplayArea] Found first valid block. Setting selectedBlockId to:', firstValidBlock.block_id);\n                        setSelectedBlockId(firstValidBlock.block_id);\n                    } else {\n                        if (selectedBlockId !== '') {\n                            console.warn('[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId.');\n                            setSelectedBlockId('');\n                        }\n                    }\n                } else {\n                    console.log('[LogDisplayArea] Current selection is still valid:', selectedBlockId);\n                }\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        dataChunks,\n        selectedBlockId\n    ]); // Keep selectedBlockId in dependencies to re-evaluate if it changes externally or becomes invalid\n    // 当显示选择改变时，通知父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            if (selectedBlockId && dataChunks.length > 0) {\n                const selected = dataChunks.filter({\n                    \"LogDisplayArea.useEffect.selected\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect.selected\"]);\n                onSelectionChangeRef.current(selected); // 传递筛选出的块\n            } else {\n                onSelectionChangeRef.current([]); // 如果没有选中的ID或没有数据块，传递空数组\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        selectedBlockId,\n        dataChunks\n    ]);\n    const handleBlockSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleBlockSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleBlockSelectionChange - START. blockId:', blockId);\n            setSelectedBlockId(blockId);\n            console.log('[LogDisplayArea] handleBlockSelectionChange - END.');\n        }\n    }[\"LogDisplayArea.useCallback[handleBlockSelectionChange]\"], []);\n    const handleExportSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleExportSelectionChange - START. blockId:', blockId);\n            setExportBlockIds({\n                \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (prevSelected)=>{\n                    const newSelection = prevSelected.includes(blockId) ? prevSelected.filter({\n                        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (id)=>id !== blockId\n                    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]) : [\n                        ...prevSelected,\n                        blockId\n                    ];\n                    console.log('[LogDisplayArea] handleExportSelectionChange - New selection:', newSelection);\n                    return newSelection;\n                }\n            }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]);\n        }\n    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"], []);\n    const selectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[selectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] selectAllForExport - START');\n            const allIds = dataChunks.map({\n                \"LogDisplayArea.useCallback[selectAllForExport].allIds\": (chunk)=>chunk.block_id\n            }[\"LogDisplayArea.useCallback[selectAllForExport].allIds\"]);\n            setExportBlockIds(allIds);\n            console.log('[LogDisplayArea] selectAllForExport - END. Selected all IDs:', allIds);\n        }\n    }[\"LogDisplayArea.useCallback[selectAllForExport]\"], [\n        dataChunks\n    ]);\n    const deselectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[deselectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] deselectAllForExport - START');\n            setExportBlockIds([]);\n            console.log('[LogDisplayArea] deselectAllForExport - END');\n        }\n    }[\"LogDisplayArea.useCallback[deselectAllForExport]\"], []);\n    const handleExportAllImages = async ()=>{\n        console.log('handleExportAllImages called');\n        console.log('exportBlockIds:', exportBlockIds);\n        if (!dataChunks || dataChunks.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '没有可导出的内容。'\n            });\n            return;\n        }\n        if (exportBlockIds.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '请至少选择一个数据块进行导出。'\n            });\n            return;\n        }\n        // 使用主页面的导出模式，这样可以正确渲染所有选中的图表\n        console.log('[LogDisplayArea] Triggering export via onStartExport callback');\n        onStartExport(exportBlockIds);\n    };\n    const hasContentToExport = dataChunks && dataChunks.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"h-[450px] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"选择数据块进行分析\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"从解析的日志文件中选择一个数据块以在图表中显示。\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleExportAllImages,\n                        disabled: isExportingAll || !hasContentToExport || exportBlockIds.length === 0,\n                        size: \"sm\",\n                        children: isExportingAll ? '导出中...' : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"导出选中图片\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: displayAreaRef,\n                    className: \"h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: selectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: deselectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"取消全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground ml-2\",\n                                    children: [\n                                        \"已选择导出: \",\n                                        exportBlockIds.length,\n                                        \" 项\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined),\n                                isExportingAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 ml-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                        value: exportProgress,\n                                        className: \"h-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-2 border rounded-md\",\n                                children: dataChunks.map((chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__.RadioGroup, {\n                                                value: selectedBlockId,\n                                                onValueChange: handleBlockSelectionChange,\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__.RadioGroupItem, {\n                                                            value: chunk.block_id,\n                                                            id: \"display-\".concat(chunk.block_id)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                            htmlFor: \"display-\".concat(chunk.block_id),\n                                                            className: \"cursor-pointer\",\n                                                            children: \"显示\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"export-\".concat(chunk.block_id),\n                                                        checked: exportBlockIds.includes(chunk.block_id),\n                                                        onChange: ()=>handleExportSelectionChange(chunk.block_id),\n                                                        className: \"h-4 w-4 rounded border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"export-\".concat(chunk.block_id),\n                                                        className: \"cursor-pointer\",\n                                                        children: \"数据块 \".concat(chunk.block_id, \" (胶厚: \").concat(chunk.glue_thickness_values.length, \", 准直: \").concat(chunk.collimation_diff_values.length, \")\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, chunk.block_id, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined),\n                        (!dataChunks || dataChunks.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground p-2\",\n                            children: \"暂无数据块可供分析或导出。\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LogDisplayArea, \"cU74k/Pu9kFykXhWbSSWrR9OMXE=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = LogDisplayArea;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogDisplayArea);\nvar _c;\n$RefreshReg$(_c, \"LogDisplayArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\n"));

/***/ })

});