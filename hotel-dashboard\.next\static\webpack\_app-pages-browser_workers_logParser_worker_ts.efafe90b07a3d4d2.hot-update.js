"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // A single regex to identify any line that marks the beginning of a new data block.\n    const ANY_BLOCK_START_MARKER = /(?:(?:开始|打开)抽真空|轴停止运动)/;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const markers = [];\n        const lines = logContent.split('\\n');\n        let currentIndex = 0;\n        // 1. Find the starting index of every line that contains a block start marker.\n        for (const line of lines){\n            if (ANY_BLOCK_START_MARKER.test(line)) {\n                markers.push({\n                    index: currentIndex\n                });\n            }\n            // Add 1 for the newline character\n            currentIndex += line.length + 1;\n        }\n        if (markers.length === 0) {\n            if (DEBUG) console.log(\"[Worker] No block start markers found in log content.\");\n            return [];\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // 2. Create blocks from the content between markers.\n        for(let i = 0; i < markers.length; i++){\n            const startIdx = markers[i].index;\n            const endIdx = i + 1 < markers.length ? markers[i + 1].index : logContent.length;\n            const blockContent = logContent.substring(startIdx, endIdx).trim();\n            if (!blockContent) continue;\n            let sn = null;\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            }\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(i + 1) : \"block_\".concat(i + 1);\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // A block is considered valid if it has any data at all, or contains a g_support statement.\n                if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0 || processedBlock.valve_open_events.length > 0 || blockContent.includes('insert into g_support')) {\n                    localProcessedBlocks.push(processedBlock);\n                }\n            }\n        }\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the full log content for searching, and parse it into blocks.\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"抽真空\";\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        // With the blocks now correctly defined, this search logic will work.\n                        for (const block of processedBlocks){\n                            if (matchedBlockIds.has(block.block_id)) continue;\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                // Check if the line contains the keyword first for efficiency\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            // Check if the timestamp of the line is within the desired search window\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {}\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("9d7c57d1b0d50d5e")
/******/ })();
/******/ 
/******/ }
);