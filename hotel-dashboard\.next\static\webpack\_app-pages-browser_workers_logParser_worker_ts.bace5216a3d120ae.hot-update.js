"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // V1 blocks are now strictly defined from a start phrase to an end phrase.\n    const BLOCK_REGEX_V1 = /(?:开始|打开)抽真空[\\s\\S]*?insert into g_support/g;\n    // V2 blocks have their own distinct start and end markers.\n    const BLOCK_REGEX_V2 = /轴停止运动[\\s\\S]*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        // Get timestamps for specific start/end sentences for debugging, as requested by the user.\n        const startSentenceTs = _extractTimestampFromLine(blockLines[0]);\n        let endSentenceTs = null;\n        // Find the last line containing a known end-of-block marker to get its timestamp.\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const line = blockLines[i];\n            if (line.includes('insert into g_support') || line.includes('SetSpeed:2, 85, result:')) {\n                endSentenceTs = _extractTimestampFromLine(line);\n                break; // Found the last marker line\n            }\n        }\n        if (DEBUG) {\n            // Enhanced logging as requested by the user.\n            console.log(\"[Worker] Block \".concat(blockId, \": \") + \"StartLineTS=\".concat(startSentenceTs, \", EndLineTS=\").concat(endSentenceTs, \", \") + \"OverallStartTS=\".concat(startTimeStr, \", OverallEndTS=\").concat(endTimeStr));\n        }\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        // 1. Find all possible V1 and V2 blocks independently.\n        const v1Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V1));\n        const v2Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V2));\n        // 2. Combine and sort all found blocks by their starting position in the log file.\n        const allMatches = [\n            ...v1Matches,\n            ...v2Matches\n        ].sort((a, b)=>(a.index || 0) - (b.index || 0));\n        if (allMatches.length === 0) {\n            if (DEBUG) console.log(\"[Worker] No V1 or V2 blocks found in log content.\");\n            return [];\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // 3. Process each block in the correct order.\n        allMatches.forEach((match, index)=>{\n            const blockContent = match[0];\n            if (!blockContent) return;\n            let sn = null;\n            // SN is always expected inside the block now, due to the new regex definitions.\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            }\n            // Process the block first with a temporary ID to get its properties, like start_time.\n            const tempBlockId = \"temp_\".concat(index + 1);\n            const processedBlock = processRawBlock(tempBlockId, blockContent);\n            if (processedBlock) {\n                // Now, create the final block_id based on the user's request.\n                // Use the start_time if available, otherwise fall back to the old naming scheme.\n                // Sanitize the start_time to make it a valid ID by replacing special characters.\n                const finalBlockId = processedBlock.start_time ? processedBlock.start_time.replace(/[,:\\s]/g, '-') : sn ? \"\".concat(sn, \"_\").concat(index + 1) : \"block_\".concat(index + 1);\n                processedBlock.block_id = finalBlockId;\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Every matched block is valid and must be kept.\n                localProcessedBlocks.push(processedBlock);\n            }\n        });\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the full log content for searching, and parse it into blocks.\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp range match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        for (const block of processedBlocks){\n                            // Check if the block has valid start and end times.\n                            if (block.start_time && block.end_time) {\n                                try {\n                                    const blockStartTime = new Date(block.start_time.replace(',', '.')).getTime();\n                                    const blockEndTime = new Date(block.end_time.replace(',', '.')).getTime();\n                                    // The correct logic, as per user instruction:\n                                    // Check if the image's timestamp falls within the block's time range.\n                                    if (targetTime >= blockStartTime && targetTime <= blockEndTime) {\n                                        if (DEBUG) console.log(\"[Worker] Timestamp \".concat(targetTime, \" falls within block \").concat(block.block_id, \" range [\").concat(blockStartTime, \" - \").concat(blockEndTime, \"]. Match found.\"));\n                                        matchedBlockIds.add(block.block_id);\n                                    }\n                                } catch (e) {\n                                    if (DEBUG) console.error(\"[Worker] Could not parse timestamp for block \".concat(block.block_id, \".\"), e);\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("6af96fe4257e9892")
/******/ })();
/******/ 
/******/ }
);