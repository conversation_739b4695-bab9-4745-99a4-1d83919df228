"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/drawing-board/Canvas.tsx":
/*!*********************************************!*\
  !*** ./components/drawing-board/Canvas.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst Canvas = (param)=>{\n    let { width, height, grid, shapes, addShape, deleteShape, backgroundColor } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Canvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            const dpr = window.devicePixelRatio || 1;\n            // Set the actual size of the canvas in memory to match the device's pixel ratio.\n            canvas.width = width * dpr;\n            canvas.height = height * dpr;\n            // Set the display size of the canvas.\n            canvas.style.width = \"\".concat(width, \"px\");\n            canvas.style.height = \"\".concat(height, \"px\");\n            // Scale the context to ensure all drawing operations are scaled up.\n            ctx.scale(dpr, dpr);\n            // Clear canvas with background color.\n            ctx.fillStyle = backgroundColor;\n            ctx.fillRect(0, 0, width, height);\n            // Draw grid\n            drawGrid(ctx);\n            // Define drawing functions inside useEffect to ensure they are not part of the component's\n            // top-level scope, which can cause issues with SSR in Next.js.\n            const getShapeCenter = {\n                \"Canvas.useEffect.getShapeCenter\": (shape)=>{\n                    const cellWidth = width / grid.cols;\n                    const cellHeight = height / grid.rows;\n                    const radius = shape.diameter / 2;\n                    const cellTopLeftX = shape.cell.col * cellWidth;\n                    const cellTopLeftY = shape.cell.row * cellHeight;\n                    let cx, cy;\n                    switch(shape.alignment){\n                        case 'center':\n                            cx = cellTopLeftX + cellWidth / 2;\n                            cy = cellTopLeftY + cellHeight / 2;\n                            break;\n                        case 'topLeft':\n                            cx = cellTopLeftX + radius;\n                            cy = cellTopLeftY + radius;\n                            break;\n                        case 'coordinates':\n                            if (shape.coordinates) {\n                                cx = cellTopLeftX + shape.coordinates.x;\n                                cy = cellTopLeftY + shape.coordinates.y;\n                            } else {\n                                cx = cellTopLeftX + cellWidth / 2;\n                                cy = cellTopLeftY + cellHeight / 2;\n                            }\n                            break;\n                    }\n                    return {\n                        cx,\n                        cy\n                    };\n                }\n            }[\"Canvas.useEffect.getShapeCenter\"];\n            const drawMtfPattern = {\n                \"Canvas.useEffect.drawMtfPattern\": function(ictx, x, y, size, color, idpr) {\n                    let lineWidth = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : 2;\n                    const tempCanvas = document.createElement('canvas');\n                    tempCanvas.width = size * idpr;\n                    tempCanvas.height = size * idpr;\n                    const tempCtx = tempCanvas.getContext('2d');\n                    if (!tempCtx) return;\n                    tempCtx.scale(idpr, idpr);\n                    tempCtx.fillStyle = '#FFFFFF';\n                    tempCtx.fillRect(0, 0, size, size);\n                    tempCtx.fillStyle = color;\n                    tempCtx.imageSmoothingEnabled = false;\n                    const center_x = size / 2;\n                    const center_y = size / 2;\n                    const gap = lineWidth;\n                    const step = lineWidth + gap;\n                    tempCtx.fillRect(Math.round(0), Math.round(center_y - lineWidth / 2), Math.round(size), Math.round(lineWidth));\n                    tempCtx.fillRect(Math.round(center_x - lineWidth / 2), Math.round(0), Math.round(lineWidth), Math.round(size));\n                    for(let i = center_x - lineWidth / 2 - gap; i > 0; i -= step){\n                        tempCtx.fillRect(Math.round(i - lineWidth), Math.round(0), Math.round(lineWidth), Math.round(size / 2 - lineWidth / 2));\n                    }\n                    for(let i = center_y - lineWidth / 2 - gap; i > 0; i -= step){\n                        tempCtx.fillRect(Math.round(center_x + lineWidth / 2), Math.round(i - lineWidth), Math.round(size / 2 - lineWidth / 2), Math.round(lineWidth));\n                    }\n                    for(let i = center_y + lineWidth / 2 + gap; i < size; i += step){\n                        tempCtx.fillRect(Math.round(0), Math.round(i), Math.round(size / 2 - lineWidth / 2), Math.round(lineWidth));\n                    }\n                    for(let i = center_x + lineWidth / 2 + gap; i < size; i += step){\n                        tempCtx.fillRect(Math.round(i), Math.round(center_y + lineWidth / 2), Math.round(lineWidth), Math.round(size / 2 - lineWidth / 2));\n                    }\n                    ictx.imageSmoothingEnabled = false;\n                    ictx.drawImage(tempCanvas, x, y, size, size);\n                }\n            }[\"Canvas.useEffect.drawMtfPattern\"];\n            const drawShape = {\n                \"Canvas.useEffect.drawShape\": (ictx, shape, idpr)=>{\n                    const { cx, cy } = getShapeCenter(shape);\n                    const radius = shape.diameter / 2;\n                    if (shape.type === 'circle') {\n                        ictx.save();\n                        // 禁用抗锯齿以避免边缘颜色混合\n                        ictx.imageSmoothingEnabled = false;\n                        // 使用像素级精确绘制来避免边缘颜色问题\n                        const tempCanvas = document.createElement('canvas');\n                        const tempSize = Math.ceil(shape.diameter * idpr) + 2; // 添加一些边距\n                        tempCanvas.width = tempSize;\n                        tempCanvas.height = tempSize;\n                        const tempCtx = tempCanvas.getContext('2d');\n                        if (tempCtx) {\n                            // 在临时画布上绘制圆形\n                            tempCtx.imageSmoothingEnabled = false;\n                            tempCtx.fillStyle = shape.color;\n                            const tempRadius = shape.diameter * idpr / 2;\n                            const tempCenter = tempSize / 2;\n                            // 使用像素级绘制，避免抗锯齿\n                            for(let x = 0; x < tempSize; x++){\n                                for(let y = 0; y < tempSize; y++){\n                                    const distance = Math.sqrt((x - tempCenter) ** 2 + (y - tempCenter) ** 2);\n                                    if (distance <= tempRadius) {\n                                        tempCtx.fillRect(x, y, 1, 1);\n                                    }\n                                }\n                            }\n                            // 将临时画布绘制到主画布上\n                            ictx.drawImage(tempCanvas, cx - shape.diameter / 2, cy - shape.diameter / 2, shape.diameter, shape.diameter);\n                        } else {\n                            // 如果临时画布创建失败，使用原始方法\n                            ictx.fillStyle = shape.color;\n                            ictx.beginPath();\n                            ictx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ictx.fill();\n                        }\n                        ictx.restore();\n                    } else if (shape.type === 'square') {\n                        drawMtfPattern(ictx, cx - radius, cy - radius, shape.diameter, shape.color, idpr, shape.mtfWidth);\n                    }\n                }\n            }[\"Canvas.useEffect.drawShape\"];\n            // Draw shapes\n            shapes.forEach({\n                \"Canvas.useEffect\": (shape)=>drawShape(ctx, shape, dpr)\n            }[\"Canvas.useEffect\"]);\n        }\n    }[\"Canvas.useEffect\"], [\n        width,\n        height,\n        grid,\n        shapes,\n        backgroundColor\n    ]);\n    const drawGrid = (ctx)=>{\n        ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';\n        ctx.setLineDash([\n            5,\n            5\n        ]);\n        ctx.lineWidth = 1;\n        const cellWidth = width / grid.cols;\n        const cellHeight = height / grid.rows;\n        for(let i = 1; i < grid.cols; i++){\n            ctx.beginPath();\n            ctx.moveTo(i * cellWidth, 0);\n            ctx.lineTo(i * cellWidth, height);\n            ctx.stroke();\n        }\n        for(let i = 1; i < grid.rows; i++){\n            ctx.beginPath();\n            ctx.moveTo(0, i * cellHeight);\n            ctx.lineTo(width, i * cellHeight);\n            ctx.stroke();\n        }\n        ctx.setLineDash([]);\n    };\n    const findShapeAt = (x, y)=>{\n        // This function is called from an event handler, so it's safe to be outside useEffect.\n        // However, it needs to calculate shape centers, so we duplicate that logic or pass it.\n        // For simplicity, we can just call getShapeCenter which is now defined in the component scope.\n        // But getShapeCenter is NOT in the component scope anymore.\n        // So we must redefine it or move findShapeAt into useEffect as well, which is not ideal.\n        // Let's redefine the calculation logic here.\n        const cellWidth = width / grid.cols;\n        const cellHeight = height / grid.rows;\n        // Iterate in reverse to find the top-most shape\n        for(let i = shapes.length - 1; i >= 0; i--){\n            const shape = shapes[i];\n            const radius = shape.diameter / 2;\n            const cellTopLeftX = shape.cell.col * cellWidth;\n            const cellTopLeftY = shape.cell.row * cellHeight;\n            let cx, cy;\n            switch(shape.alignment){\n                case 'center':\n                    cx = cellTopLeftX + cellWidth / 2;\n                    cy = cellTopLeftY + cellHeight / 2;\n                    break;\n                case 'topLeft':\n                    cx = cellTopLeftX + radius;\n                    cy = cellTopLeftY + radius;\n                    break;\n                case 'coordinates':\n                    if (shape.coordinates) {\n                        cx = cellTopLeftX + shape.coordinates.x;\n                        cy = cellTopLeftY + shape.coordinates.y;\n                    } else {\n                        cx = cellTopLeftX + cellWidth / 2;\n                        cy = cellTopLeftY + cellHeight / 2;\n                    }\n                    break;\n            }\n            if (shape.type === 'circle') {\n                const distance = Math.sqrt((x - cx) ** 2 + (y - cy) ** 2);\n                if (distance <= radius) {\n                    return shape;\n                }\n            } else if (shape.type === 'square') {\n                if (x >= cx - radius && x <= cx + radius && y >= cy - radius && y <= cy + radius) {\n                    return shape;\n                }\n            }\n        }\n        return undefined;\n    };\n    const handleCanvasInteraction = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        if (event.button === 2) {\n            event.preventDefault();\n            const shapeToDelete = findShapeAt(x, y);\n            if (shapeToDelete) {\n                deleteShape(shapeToDelete.id);\n            }\n            return;\n        }\n        // Left-click to add shape\n        const cellWidth = width / grid.cols;\n        const cellHeight = height / grid.rows;\n        const col = Math.floor(x / cellWidth);\n        const row = Math.floor(y / cellHeight);\n        addShape({\n            row,\n            col\n        }, 'center');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        width: width,\n        height: height,\n        onMouseDown: handleCanvasInteraction,\n        onContextMenu: (e)=>e.preventDefault(),\n        className: \"border border-gray-400\"\n    }, void 0, false, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\drawing-board\\\\Canvas.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Canvas, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c = Canvas;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Canvas);\nvar _c;\n$RefreshReg$(_c, \"Canvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/drawing-board/Canvas.tsx\n"));

/***/ })

});