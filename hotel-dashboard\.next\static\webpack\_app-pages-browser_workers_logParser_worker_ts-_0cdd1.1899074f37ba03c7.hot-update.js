"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts-_0cdd1",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {\n    console.warn('[Worker] logParser.worker.ts loaded in non-worker context (or main thread). Exports are available, but worker-specific code will not run.');\n} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    const BLOCK_REGEX_V1 = /打开真空泵(?:(?!打开真空泵)[\\s\\S])*?insert into g_support/g;\n    // --- V2 正则表达式 ---\n    const BLOCK_REGEX_V2 = /轴停止运动(?:(?!轴停止运动)[\\s\\S])*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function detectLogVersion(logContent) {\n        if (logContent.includes(\"轴停止运动\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V2');\n            return \"V2\";\n        }\n        if (logContent.includes(\"打开真空泵\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V1');\n            return \"V1\";\n        }\n        if (DEBUG) console.warn('[Worker] Could not determine log version.');\n        return \"UNKNOWN\";\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const version = detectLogVersion(logContent);\n        if (version === \"UNKNOWN\") {\n            throw new Error('Unknown log file version. Could not parse.');\n        }\n        const blockRegex = version === \"V1\" ? BLOCK_REGEX_V1 : BLOCK_REGEX_V2;\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        let match;\n        let lastIndex = 0;\n        let iterationCount = 0;\n        const MAX_ITERATIONS = 100000; // Safety break to prevent infinite loops\n        // Manually iterate with exec() to have more control and prevent crashes\n        while((match = blockRegex.exec(logContent)) !== null){\n            iterationCount++;\n            if (iterationCount > MAX_ITERATIONS) {\n                console.error('[Worker] Infinite loop detected in block matching. Aborting.');\n                self.postMessage({\n                    type: 'PARSE_LOG_RESULT',\n                    success: false,\n                    error: 'Infinite loop detected during log parsing. Check log format and block delimiters.'\n                });\n                return []; // Stop processing\n            }\n            // This check prevents infinite loops on zero-length matches\n            if (match.index === blockRegex.lastIndex) {\n                blockRegex.lastIndex++;\n            }\n            const blockContent = match[0];\n            if (!blockContent || typeof match.index === 'undefined') continue;\n            let sn = null;\n            // 1. Try to find SN inside the block first\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n                if (DEBUG) console.log(\"[Worker] Found SN inside block \".concat(iterationCount, \": \").concat(sn));\n            } else {\n                // 2. If not found, search for the next SN *after* this block\n                const endOfCurrentBlock = match.index + blockContent.length;\n                const searchArea = logContent.substring(endOfCurrentBlock); // Search from end of block to end of file\n                const snMatchOutside = snRegex.exec(searchArea);\n                if (snMatchOutside) {\n                    // We need to ensure we don't read past the *next* block's start\n                    const nextBlockMatch = blockRegex.exec(logContent); // Peek ahead\n                    if (nextBlockMatch && endOfCurrentBlock + snMatchOutside.index > nextBlockMatch.index) {\n                        // The SN found belongs to a later block, so ignore it for this one.\n                        if (DEBUG) console.log(\"[Worker] SN found outside block \".concat(iterationCount, \", but it belongs to a subsequent block. Ignoring.\"));\n                    } else {\n                        sn = snMatchOutside[1];\n                        if (DEBUG) console.log(\"[Worker] Found SN outside block \".concat(iterationCount, \": \").concat(sn));\n                    }\n                    // IMPORTANT: Reset regex index after peeking\n                    blockRegex.lastIndex = match.index + blockContent.length;\n                }\n            }\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(iterationCount) : \"block_\".concat(iterationCount);\n            if (DEBUG) console.log(\"[Worker] Final Block ID for index \".concat(iterationCount - 1, \": \").concat(blockId));\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0) {\n                    localProcessedBlocks.push(processedBlock);\n                }\n            }\n            lastIndex = blockRegex.lastIndex;\n        }\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the parsed blocks globally in the worker\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"打开真空泵\";\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        for (const block of processedBlocks){\n                            // Avoid re-checking a block that already has a match\n                            if (matchedBlockIds.has(block.block_id)) {\n                                continue;\n                            }\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            // Timestamps are in 'YYYY-MM-DD HH:mm:ss,SSS' format\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {\n                                        // Ignore lines with invalid date formats\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {\n        console.error('[Worker] Uncaught error in worker script:', errorEvent);\n    };\n    console.log('[Worker] logParser.worker.ts script loaded and event listener for \"message\" set up.');\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("660546a6fdaf6b55")
/******/ })();
/******/ 
/******/ }
);