"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // The regexes need to be aware of each other's starting delimiters to prevent overlap.\n    const BLOCK_REGEX_V1 = /(?:开始|打开)抽真空(?:(?!打开真空泵|开始抽真空|轴停止运动)[\\s\\S])*?insert into g_support/g;\n    const BLOCK_REGEX_V2 = /轴停止运动(?:(?!轴停止运动|打开真空泵|开始抽真空)[\\s\\S])*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const allMatches = [];\n        let lastIndex = 0;\n        const MAX_ITERATIONS_OUTER = 50000; // Safety break\n        let outerIteration = 0;\n        // Iteratively find all V1 and V2 blocks in the correct order\n        while(lastIndex < logContent.length){\n            outerIteration++;\n            if (outerIteration > MAX_ITERATIONS_OUTER) {\n                console.error(\"Outer loop safety break triggered during block finding.\");\n                break;\n            }\n            BLOCK_REGEX_V1.lastIndex = lastIndex;\n            BLOCK_REGEX_V2.lastIndex = lastIndex;\n            const matchV1 = BLOCK_REGEX_V1.exec(logContent);\n            const matchV2 = BLOCK_REGEX_V2.exec(logContent);\n            let firstMatch = null;\n            // Determine which match (if any) comes first\n            if (matchV1 && matchV2) {\n                firstMatch = matchV1.index < matchV2.index ? matchV1 : matchV2;\n            } else {\n                firstMatch = matchV1 || matchV2;\n            }\n            if (firstMatch && typeof firstMatch.index !== 'undefined') {\n                allMatches.push(firstMatch);\n                // Advance lastIndex past the end of the found block to continue searching\n                lastIndex = firstMatch.index + firstMatch[0].length;\n            } else {\n                break; // No more matches found\n            }\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // Process all the matches that were found in order\n        allMatches.forEach((match, index)=>{\n            const blockContent = match[0];\n            if (!blockContent || typeof match.index === 'undefined') return;\n            let sn = null;\n            // 1. Try to find SN inside the current block\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            } else {\n                // 2. If not found, search for the next SN *after* this block but *before* the next one\n                const endOfCurrentBlock = match.index + blockContent.length;\n                const nextMatch = allMatches[index + 1];\n                const searchArea = logContent.substring(endOfCurrentBlock);\n                const snMatchOutside = snRegex.exec(searchArea);\n                if (snMatchOutside) {\n                    // Check if the found SN is located *before* the start of the next block.\n                    if (!nextMatch || endOfCurrentBlock + snMatchOutside.index < (nextMatch.index || 0)) {\n                        sn = snMatchOutside[1];\n                    }\n                }\n            }\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(index + 1) : \"block_\".concat(index + 1);\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Only add blocks that contain meaningful data\n                if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0) {\n                    localProcessedBlocks.push(processedBlock);\n                }\n            }\n        });\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store both the original content and the parsed blocks\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"抽真空\"; // Keyword is kept generic. The main fix is the block regex.\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        for (const block of processedBlocks){\n                            // Avoid re-checking a block that already has a match\n                            if (matchedBlockIds.has(block.block_id)) {\n                                continue;\n                            }\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            // Timestamps are in 'YYYY-MM-DD HH:mm:ss,SSS' format\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {\n                                        // Ignore lines with invalid date formats\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("efafe90b07a3d4d2")
/******/ })();
/******/ 
/******/ }
);