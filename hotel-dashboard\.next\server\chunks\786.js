"use strict";exports.id=786,exports.ids=[786],exports.modules={46786:(e,t,l)=>{l.d(t,{default:()=>f});var a=l(60687),r=l(43210),o=l.n(r),s=l(55192),i=l(39390),n=l(68988),d=l(24934),c=l(63974);let h=({canvasSize:e,setCanvasSize:t,setGrid:l,grid:r,selectedColor:h,setSelectedColor:u,selectedShapeType:f,setSelectedShapeType:m,diameter:x,setDiameter:g,replaceColor:p,resetCanvas:w,exportCanvas:v,mtfWidth:j,setMtfWidth:M})=>{let[y,b]=o().useState("#000000"),[S,C]=o().useState("#ff0000"),[F,N]=o().useState({width:0,height:0});return o().useEffect(()=>{r.cols>0&&r.rows>0&&N({width:e.width/r.cols,height:e.height/r.rows})},[e,r]),(0,a.jsxs)(s.Zp,{className:"w-full md:w-80 h-full flex flex-col",children:[(0,a.jsx)(s.aR,{className:"flex-shrink-0",children:(0,a.jsx)(s.ZB,{children:"工具栏"})}),(0,a.jsxs)(s.Wu,{className:"space-y-4 flex-1 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{children:"画布尺寸 (px)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.p,{type:"number",defaultValue:1920,onChange:l=>t({width:parseInt(l.target.value),height:e.height}),placeholder:"宽度"}),(0,a.jsx)(n.p,{type:"number",defaultValue:1080,onChange:l=>t({width:e.width,height:parseInt(l.target.value)}),placeholder:"高度"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{children:"网格 (列x行)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.p,{type:"number",value:r.cols,onChange:e=>l({...r,cols:parseInt(e.target.value)}),placeholder:"列"}),(0,a.jsx)(n.p,{type:"number",value:r.rows,onChange:e=>l({...r,rows:parseInt(e.target.value)}),placeholder:"行"})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(i.J,{children:"单元格尺寸"}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground p-2 border rounded-md",children:[(0,a.jsxs)("div",{children:["宽: ",F.width.toFixed(2)," px"]}),(0,a.jsxs)("div",{children:["高: ",F.height.toFixed(2)," px"]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{children:"图形类型"}),(0,a.jsxs)(c.l6,{value:f,onValueChange:e=>m(e),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"选择图形"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"circle",children:"圆形"}),(0,a.jsx)(c.eb,{value:"square",children:"MTF"})]})]})]}),"square"===f&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{children:"MTF 线宽 (px)"}),(0,a.jsxs)(c.l6,{value:String(j),onValueChange:e=>M(parseInt(e,10)),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"选择宽度"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"1",children:"1"}),(0,a.jsx)(c.eb,{value:"2",children:"2"}),(0,a.jsx)(c.eb,{value:"3",children:"3"}),(0,a.jsx)(c.eb,{value:"4",children:"4"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{children:"直径"}),(0,a.jsx)(n.p,{type:"number",value:x,onChange:e=>g(parseInt(e.target.value,10)||0)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{children:"图形颜色"}),(0,a.jsx)(n.p,{type:"color",value:h,onChange:e=>u(e.target.value),className:"w-full"})]}),(0,a.jsxs)("div",{className:"space-y-2 border-t pt-4",children:[(0,a.jsx)(i.J,{children:"替换颜色"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.p,{placeholder:"旧颜色",type:"color",value:y,onChange:e=>b(e.target.value)}),(0,a.jsx)("span",{children:">"}),(0,a.jsx)(n.p,{placeholder:"新颜色",type:"color",value:S,onChange:e=>C(e.target.value)})]}),(0,a.jsx)(d.$,{onClick:()=>p(y,S),className:"w-full",children:"替换"})]}),(0,a.jsxs)("div",{className:"space-y-2 border-t pt-4",children:[(0,a.jsx)(i.J,{children:"画布操作"}),(0,a.jsx)(d.$,{onClick:w,className:"w-full",variant:"destructive",children:"重置画布"}),(0,a.jsx)(d.$,{onClick:()=>v("png"),className:"w-full",children:"导出为 PNG"}),(0,a.jsx)(d.$,{onClick:()=>v("jpeg"),className:"w-full",children:"导出为 JPEG"})]})]})]})},u=({width:e,height:t,grid:l,shapes:o,addShape:s,deleteShape:i,backgroundColor:n})=>{let d=(0,r.useRef)(null);(0,r.useEffect)(()=>{let a=d.current;if(!a)return;let r=a.getContext("2d");if(!r)return;let s=window.devicePixelRatio||1;a.width=e*s,a.height=t*s,a.style.width=`${e}px`,a.style.height=`${t}px`,r.scale(s,s),r.fillStyle=n,r.fillRect(0,0,e,t),c(r);let i=a=>{let r,o;let s=e/l.cols,i=t/l.rows,n=a.diameter/2,d=a.cell.col*s,c=a.cell.row*i;switch(a.alignment){case"center":r=d+s/2,o=c+i/2;break;case"topLeft":r=d+n,o=c+n;break;case"coordinates":a.coordinates?(r=d+a.coordinates.x,o=c+a.coordinates.y):(r=d+s/2,o=c+i/2)}return{cx:r,cy:o}},h=(e,t,l,a,r,o,s=2)=>{let i=document.createElement("canvas");i.width=a*o,i.height=a*o;let n=i.getContext("2d");if(!n)return;n.scale(o,o),n.fillStyle="#FFFFFF",n.fillRect(0,0,a,a),n.fillStyle=r,n.imageSmoothingEnabled=!1;let d=a/2,c=a/2,h=s+s;n.fillRect(Math.round(d-s/2),0,Math.round(s),Math.round(a)),n.fillRect(0,Math.round(c-s/2),Math.round(a),Math.round(s));for(let e=d-s/2-h;e>-s;e-=h)n.fillRect(Math.round(e),0,Math.round(s),Math.round(c-s/2));for(let e=c+s/2+s;e<a;e+=h)n.fillRect(0,Math.round(e),Math.round(d-s/2),Math.round(s));for(let e=c-s/2-h;e>-s;e-=h)n.fillRect(Math.round(d+s/2),Math.round(e),Math.round(a-(d+s/2)),Math.round(s));for(let e=d+s/2+s;e<a;e+=h)n.fillRect(Math.round(e),Math.round(c+s/2),Math.round(s),Math.round(a-(c+s/2)));e.imageSmoothingEnabled=!1,e.drawImage(i,t,l,a,a)},u=(e,t,l)=>{let{cx:a,cy:r}=i(t),o=t.diameter/2;if("circle"===t.type){e.save(),e.imageSmoothingEnabled=!1;let s=document.createElement("canvas"),i=Math.ceil(t.diameter*l)+2;s.width=i,s.height=i;let n=s.getContext("2d");if(n){n.imageSmoothingEnabled=!1,n.fillStyle=t.color;let o=t.diameter*l/2,d=i/2;for(let e=0;e<i;e++)for(let t=0;t<i;t++)Math.sqrt((e-d)**2+(t-d)**2)<=o&&n.fillRect(e,t,1,1);e.drawImage(s,a-t.diameter/2,r-t.diameter/2,t.diameter,t.diameter)}else e.fillStyle=t.color,e.beginPath(),e.arc(a,r,o,0,2*Math.PI),e.fill();e.restore()}else"square"===t.type&&h(e,a-o,r-o,t.diameter,t.color,l,t.mtfWidth)};o.forEach(e=>u(r,e,s))},[e,t,l,o,n]);let c=a=>{a.strokeStyle="rgba(0, 0, 0, 0.5)",a.setLineDash([5,5]),a.lineWidth=1;let r=e/l.cols,o=t/l.rows;for(let e=1;e<l.cols;e++)a.beginPath(),a.moveTo(e*r,0),a.lineTo(e*r,t),a.stroke();for(let t=1;t<l.rows;t++)a.beginPath(),a.moveTo(0,t*o),a.lineTo(e,t*o),a.stroke();a.setLineDash([])},h=(a,r)=>{let s=e/l.cols,i=t/l.rows;for(let e=o.length-1;e>=0;e--){let t,l;let n=o[e],d=n.diameter/2,c=n.cell.col*s,h=n.cell.row*i;switch(n.alignment){case"center":t=c+s/2,l=h+i/2;break;case"topLeft":t=c+d,l=h+d;break;case"coordinates":n.coordinates?(t=c+n.coordinates.x,l=h+n.coordinates.y):(t=c+s/2,l=h+i/2)}if("circle"===n.type){if(Math.sqrt((a-t)**2+(r-l)**2)<=d)return n}else if("square"===n.type&&a>=t-d&&a<=t+d&&r>=l-d&&r<=l+d)return n}};return(0,a.jsx)("canvas",{ref:d,width:e,height:t,onMouseDown:a=>{let r=d.current;if(!r)return;let o=r.getBoundingClientRect(),n=a.clientX-o.left,c=a.clientY-o.top;if(2===a.button){a.preventDefault();let e=h(n,c);e&&i(e.id);return}let u=e/l.cols;s({row:Math.floor(c/(t/l.rows)),col:Math.floor(n/u)},"center")},onContextMenu:e=>e.preventDefault(),className:"border border-gray-400"})},f=()=>{let[e,t]=(0,r.useState)({width:1920,height:1080}),[l,o]=(0,r.useState)({rows:9,cols:17}),[s,i]=(0,r.useState)([]),[n,d]=(0,r.useState)("#000000"),[c,f]=(0,r.useState)("#FFFFFF"),[m,x]=(0,r.useState)("circle"),[g,p]=(0,r.useState)(50),[w,v]=(0,r.useState)(2),j=async t=>{if("undefined"==typeof document)return;let a=document.createElement("canvas");a.width=e.width,a.height=e.height;let r=a.getContext("2d");if(!r){console.error("Could not get temporary canvas context for exporting.");return}r.fillStyle=c,r.fillRect(0,0,a.width,a.height);let o=e=>{let t,r;let o=a.width/l.cols,s=a.height/l.rows,i=e.diameter/2,n=e.cell.col*o,d=e.cell.row*s;switch(e.alignment){case"center":t=n+o/2,r=d+s/2;break;case"topLeft":t=n+i,r=d+i;break;case"coordinates":e.coordinates?(t=n+e.coordinates.x,r=d+e.coordinates.y):(t=n+o/2,r=d+s/2)}return{cx:t,cy:r}},i=(e,t,l,a,r,o=2)=>{let s=document.createElement("canvas");s.width=a,s.height=a;let i=s.getContext("2d");if(!i)return;i.fillStyle="#FFFFFF",i.fillRect(0,0,a,a),i.fillStyle=r,i.imageSmoothingEnabled=!1;let n=a/2,d=a/2,c=o+o;i.fillRect(Math.round(0),Math.round(d-o/2),Math.round(a),Math.round(o)),i.fillRect(Math.round(n-o/2),Math.round(0),Math.round(o),Math.round(a));for(let e=n-o/2-o;e>0;e-=c)i.fillRect(Math.round(e-o),Math.round(0),Math.round(o),Math.round(a/2-o/2));for(let e=d-o/2-o;e>0;e-=c)i.fillRect(Math.round(n+o/2),Math.round(e-o),Math.round(a/2-o/2),Math.round(o));for(let e=d+o/2+o;e<a;e+=c)i.fillRect(Math.round(0),Math.round(e),Math.round(a/2-o/2),Math.round(o));for(let e=n+o/2+o;e<a;e+=c)i.fillRect(Math.round(e),Math.round(d+o/2),Math.round(o),Math.round(a/2-o/2));e.imageSmoothingEnabled=!1,e.drawImage(s,t,l)};for(let e of s){let{cx:t,cy:l}=o(e),a=e.diameter/2;if("circle"===e.type){let o=document.createElement("canvas"),s=Math.ceil(e.diameter)+2;o.width=s,o.height=s;let i=o.getContext("2d");if(i){i.imageSmoothingEnabled=!1,i.fillStyle=e.color;let a=e.diameter/2,n=s/2;for(let e=0;e<s;e++)for(let t=0;t<s;t++)Math.sqrt((e-n)**2+(t-n)**2)<=a&&i.fillRect(e,t,1,1);r.imageSmoothingEnabled=!1,r.drawImage(o,t-e.diameter/2,l-e.diameter/2,e.diameter,e.diameter)}else r.fillStyle=e.color,r.beginPath(),r.arc(t,l,a,0,2*Math.PI),r.fill()}else"square"===e.type&&i(r,t-a,l-a,e.diameter,e.color,e.mtfWidth)}let n=a.toDataURL(`image/${t}`),d=document.createElement("a");d.href=n,d.download=`drawing-board-${Date.now()}.${t}`,document.body.appendChild(d),d.click(),document.body.removeChild(d)};return(0,a.jsxs)("div",{className:"flex flex-col md:flex-row h-full gap-4",children:[(0,a.jsx)(h,{canvasSize:e,setCanvasSize:t,setGrid:o,grid:l,selectedColor:n,setSelectedColor:d,selectedShapeType:m,setSelectedShapeType:x,diameter:g,setDiameter:p,replaceColor:(e,t)=>{let l=e.toLowerCase(),a=t.toLowerCase();c.toLowerCase()===l&&f(a),i(e=>e.map(e=>e.color.toLowerCase()===l?{...e,color:a}:e))},resetCanvas:()=>{i([]),f("#FFFFFF")},exportCanvas:j,mtfWidth:w,setMtfWidth:v}),(0,a.jsx)("div",{className:"flex-grow overflow-auto",children:(0,a.jsx)(u,{width:e.width,height:e.height,grid:l,shapes:s,addShape:(e,t,l)=>{i([...s,{id:Date.now(),type:m,cell:e,color:n,diameter:g,alignment:t,coordinates:l,..."square"===m&&{mtfWidth:w}}])},deleteShape:e=>{i(s.filter(t=>t.id!==e))},backgroundColor:c})})]})}}};