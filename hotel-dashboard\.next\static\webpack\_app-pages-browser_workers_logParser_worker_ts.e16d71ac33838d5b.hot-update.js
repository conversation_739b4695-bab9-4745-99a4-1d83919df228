"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // V1 blocks are now strictly defined from a start phrase to an end phrase.\n    const BLOCK_REGEX_V1 = /(?:开始|打开)抽真空[\\s\\S]*?insert into g_support/g;\n    // V2 blocks have their own distinct start and end markers.\n    const BLOCK_REGEX_V2 = /轴停止运动[\\s\\S]*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        // Get timestamps for specific start/end sentences for debugging, as requested by the user.\n        const startSentenceTs = _extractTimestampFromLine(blockLines[0]);\n        let endSentenceTs = null;\n        // Find the last line containing a known end-of-block marker to get its timestamp.\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const line = blockLines[i];\n            if (line.includes('insert into g_support') || line.includes('SetSpeed:2, 85, result:')) {\n                endSentenceTs = _extractTimestampFromLine(line);\n                break; // Found the last marker line\n            }\n        }\n        if (DEBUG) {\n            // Enhanced logging as requested by the user.\n            console.log(\"[Worker] Block \".concat(blockId, \": \") + \"StartLineTS=\".concat(startSentenceTs, \", EndLineTS=\").concat(endSentenceTs, \", \") + \"OverallStartTS=\".concat(startTimeStr, \", OverallEndTS=\").concat(endTimeStr));\n        }\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        // 1. Find all possible V1 and V2 blocks independently.\n        const v1Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V1));\n        const v2Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V2));\n        // 2. Combine and sort all found blocks by their starting position in the log file.\n        const allMatches = [\n            ...v1Matches,\n            ...v2Matches\n        ].sort((a, b)=>(a.index || 0) - (b.index || 0));\n        if (allMatches.length === 0) {\n            if (DEBUG) console.log(\"[Worker] No V1 or V2 blocks found in log content.\");\n            return [];\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // 3. Process each block in the correct order.\n        allMatches.forEach((match, index)=>{\n            const blockContent = match[0];\n            if (!blockContent) return;\n            let sn = null;\n            // SN is always expected inside the block now, due to the new regex definitions.\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            }\n            // Process the block first with a temporary ID to get its properties, like start_time.\n            const tempBlockId = \"temp_\".concat(index + 1);\n            const processedBlock = processRawBlock(tempBlockId, blockContent);\n            if (processedBlock) {\n                // Now, create the final block_id based on the user's request.\n                // Use the start_time if available, otherwise fall back to the old naming scheme.\n                // Use the start_time to create the block ID in YYYYMMDD_HH_mm_ss format, as requested.\n                let formattedId = null;\n                if (processedBlock.start_time) {\n                    // Converts \"YYYY-MM-DD HH:mm:ss,SSS\" to \"YYYYMMDD_HH_mm_ss\"\n                    formattedId = processedBlock.start_time.split(',')[0].replace(/-/g, '').replace(' ', '_').replace(/:/g, '_');\n                }\n                const finalBlockId = formattedId || (sn ? \"\".concat(sn, \"_\").concat(index + 1) : \"block_\".concat(index + 1));\n                processedBlock.block_id = finalBlockId;\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Every matched block is valid and must be kept.\n                localProcessedBlocks.push(processedBlock);\n            }\n        });\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the full log content for searching, and parse it into blocks.\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp range match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        for (const block of processedBlocks){\n                            // Check if the block has valid start and end times.\n                            if (block.start_time && block.end_time) {\n                                try {\n                                    const blockStartTime = new Date(block.start_time.replace(',', '.')).getTime();\n                                    const blockEndTime = new Date(block.end_time.replace(',', '.')).getTime();\n                                    // The correct logic, as per user instruction:\n                                    // Check if the image's timestamp falls within the block's time range.\n                                    if (targetTime >= blockStartTime && targetTime <= blockEndTime) {\n                                        if (DEBUG) console.log(\"[Worker] Timestamp \".concat(targetTime, \" falls within block \").concat(block.block_id, \" range [\").concat(blockStartTime, \" - \").concat(blockEndTime, \"]. Match found.\"));\n                                        matchedBlockIds.add(block.block_id);\n                                    }\n                                } catch (e) {\n                                    if (DEBUG) console.error(\"[Worker] Could not parse timestamp for block \".concat(block.block_id, \".\"), e);\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("09c3bf7642b5d3b1")
/******/ })();
/******/ 
/******/ }
);