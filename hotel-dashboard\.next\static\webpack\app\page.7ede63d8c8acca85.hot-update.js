"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx":
/*!***********************************************!*\
  !*** ./app/(dashboard)/log-analysis/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogAnalysisPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jschardet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jschardet */ \"(app-pages-browser)/./node_modules/jschardet/index.js\");\n/* harmony import */ var _components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/log-analysis/LogDisplayArea */ \"(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\");\n/* harmony import */ var _components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/log-analysis/LogChartView */ \"(app-pages-browser)/./components/log-analysis/LogChartView.tsx\");\n/* harmony import */ var _components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/log-analysis/LogFileUpload */ \"(app-pages-browser)/./components/log-analysis/LogFileUpload.tsx\");\n/* harmony import */ var _components_log_analysis_SnSearchBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/log-analysis/SnSearchBar */ \"(app-pages-browser)/./components/log-analysis/SnSearchBar.tsx\");\n/* harmony import */ var _components_log_analysis_ImageNameSearch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/log-analysis/ImageNameSearch */ \"(app-pages-browser)/./components/log-analysis/ImageNameSearch.tsx\");\n/* harmony import */ var _components_log_analysis_BatchExportCSV__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/log-analysis/BatchExportCSV */ \"(app-pages-browser)/./components/log-analysis/BatchExportCSV.tsx\");\n/* harmony import */ var _utils_snHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/snHelper */ \"(app-pages-browser)/./utils/snHelper.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_exportUtils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/exportUtils */ \"(app-pages-browser)/./lib/exportUtils.ts\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LogAnalysisPage() {\n    _s();\n    const [dataChunks, setDataChunks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Unified selection state\n    const [selectedBlockIds, setSelectedBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // SN Search states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSnSearching, setIsSnSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Image Name Search states\n    const [isImageNameSearching, setIsImageNameSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // --- Queued export states ---\n    const [exportQueue, setExportQueue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentlyExportingBlockId, setCurrentlyExportingBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedImages, setGeneratedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exportProgress, setExportProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const exportTargetContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const logParserWorker = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // --- Worker Initialization and Message Handling ---\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            logParserWorker.current = new Worker(__webpack_require__.tu(new URL(/* worker import */ __webpack_require__.p + __webpack_require__.u(\"_app-pages-browser_workers_logParser_worker_ts\"), __webpack_require__.b)));\n            logParserWorker.current.onmessage = ({\n                \"LogAnalysisPage.useEffect\": (event)=>{\n                    const { type, payload, error, allBlocks, matchedBlockIds } = event.data;\n                    switch(type){\n                        case 'PARSE_LOG_RESULT':\n                            setIsLoading(false);\n                            if (error) {\n                                handleError(error);\n                            } else {\n                                handleDataProcessed(allBlocks || []);\n                            }\n                            break;\n                        case 'MATCH_BY_TIMESTAMP_RESULT':\n                            setIsImageNameSearching(false);\n                            if (error) {\n                                toast({\n                                    title: \"图片名称搜索失败\",\n                                    description: error,\n                                    variant: \"destructive\"\n                                });\n                            } else if (matchedBlockIds) {\n                                setSelectedBlockIds({\n                                    \"LogAnalysisPage.useEffect\": (prevIds)=>new Set([\n                                            ...Array.from(prevIds),\n                                            ...matchedBlockIds\n                                        ])\n                                }[\"LogAnalysisPage.useEffect\"]);\n                                toast({\n                                    title: \"图片名称搜索完成\",\n                                    description: \"匹配到 \".concat(matchedBlockIds.length, \" 个新的数据块。\")\n                                });\n                            }\n                            break;\n                        default:\n                            break;\n                    }\n                }\n            })[\"LogAnalysisPage.useEffect\"];\n            return ({\n                \"LogAnalysisPage.useEffect\": ()=>{\n                    var _logParserWorker_current;\n                    (_logParserWorker_current = logParserWorker.current) === null || _logParserWorker_current === void 0 ? void 0 : _logParserWorker_current.terminate();\n                }\n            })[\"LogAnalysisPage.useEffect\"];\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        toast\n    ]);\n    const handleFilesSelected = async (files)=>{\n        if (!logParserWorker.current) {\n            handleError(\"日志解析器未初始化。\");\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSelectedBlockIds(new Set());\n        setDataChunks([]);\n        toast({\n            title: \"开始处理\",\n            description: \"正在处理 \".concat(files.length, \" 个文件...\")\n        });\n        try {\n            const readAndDecodeFile = (file)=>{\n                return new Promise(async (resolve, reject)=>{\n                    try {\n                        const arrayBuffer = await file.arrayBuffer();\n                        if (!arrayBuffer || arrayBuffer.byteLength === 0) {\n                            resolve(\"\");\n                            return;\n                        }\n                        const buffer = Buffer.from(new Uint8Array(arrayBuffer));\n                        const detectionResult = jschardet__WEBPACK_IMPORTED_MODULE_2__.detect(buffer);\n                        let encoding = 'utf-8';\n                        if (detectionResult && detectionResult.encoding) {\n                            const detectedEncoding = detectionResult.encoding.toUpperCase();\n                            if ([\n                                'GB2312',\n                                'GBK',\n                                'BIG5',\n                                'EUC-TW',\n                                'HZ-GB-2312'\n                            ].includes(detectedEncoding)) {\n                                encoding = 'gbk';\n                            } else if ([\n                                'UTF-8',\n                                'UTF-16LE',\n                                'UTF-16BE'\n                            ].includes(detectedEncoding)) {\n                                encoding = detectedEncoding.toLowerCase();\n                            }\n                        }\n                        try {\n                            const decoder = new TextDecoder(encoding, {\n                                fatal: true\n                            });\n                            resolve(decoder.decode(arrayBuffer));\n                        } catch (e) {\n                            const decoder = new TextDecoder('gbk', {\n                                fatal: false\n                            });\n                            resolve(decoder.decode(arrayBuffer));\n                        }\n                    } catch (e) {\n                        reject(e);\n                    }\n                });\n            };\n            const decodedContents = await Promise.all(files.map((file)=>readAndDecodeFile(file)));\n            const combinedContent = decodedContents.join('\\n');\n            if (combinedContent.trim().length === 0) {\n                handleError('所有选定文件均为空或读取失败。');\n                return;\n            }\n            const message = {\n                type: 'PARSE_LOG',\n                payload: combinedContent\n            };\n            logParserWorker.current.postMessage(message);\n        } catch (error) {\n            handleError(\"文件处理失败: \".concat(error.message, \".\"));\n        }\n    };\n    const handleDataProcessed = (workerData)=>{\n        const processedData = workerData.map((block)=>({\n                ...block,\n                data: []\n            }));\n        setDataChunks(processedData);\n        toast({\n            title: \"处理完成\",\n            description: \"日志文件已成功解析。\"\n        });\n    };\n    const handleError = (errorMessage)=>{\n        setError(errorMessage);\n        setIsLoading(false);\n        toast({\n            title: \"处理错误\",\n            description: errorMessage,\n            variant: \"destructive\"\n        });\n    };\n    const handleBlockSelectionChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogAnalysisPage.useCallback[handleBlockSelectionChanged]\": (selectedIds)=>{\n            setSelectedBlockIds(selectedIds);\n        }\n    }[\"LogAnalysisPage.useCallback[handleBlockSelectionChanged]\"], []);\n    const selectedBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[selectedBlocks]\": ()=>{\n            return dataChunks.filter({\n                \"LogAnalysisPage.useMemo[selectedBlocks]\": (block)=>selectedBlockIds.has(block.block_id)\n            }[\"LogAnalysisPage.useMemo[selectedBlocks]\"]);\n        }\n    }[\"LogAnalysisPage.useMemo[selectedBlocks]\"], [\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const chartDataForView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[chartDataForView]\": ()=>{\n            return selectedBlocks;\n        }\n    }[\"LogAnalysisPage.useMemo[chartDataForView]\"], [\n        selectedBlocks\n    ]);\n    const handleSnSearch = (query)=>{\n        setSearchQuery(query);\n        setIsSnSearching(true);\n        const snsToSearch = (0,_utils_snHelper__WEBPACK_IMPORTED_MODULE_9__.parseSnInput)(query).map((sn)=>sn.toUpperCase());\n        if (snsToSearch.length === 0) {\n            setSelectedBlockIds(new Set());\n            setIsSnSearching(false);\n            return;\n        }\n        const results = new Set();\n        dataChunks.forEach((block)=>{\n            const snFromId = block.block_id.split('_')[0].toUpperCase();\n            for (const sn of snsToSearch){\n                const isSnInSnsArray = Array.isArray(block.sns) && block.sns.some((blockSn)=>blockSn.toUpperCase() === sn);\n                if (snFromId === sn || isSnInSnsArray) {\n                    results.add(block.block_id);\n                    break;\n                }\n            }\n        });\n        setSelectedBlockIds(results);\n        toast({\n            title: \"SN搜索完成\",\n            description: \"找到 \".concat(results.size, \" 个相关数据块。\")\n        });\n    };\n    const handleClearSearch = ()=>{\n        setSearchQuery('');\n        setSelectedBlockIds(new Set());\n        setIsSnSearching(false);\n    };\n    const generateExportFilename = (block)=>{\n        if (!block) return \"unknown_block.png\";\n        // 1. Format Timestamp\n        let timestampPart = 'NODATE';\n        if (block.start_time) {\n            try {\n                const date = new Date(block.start_time.replace(',', '.'));\n                const y = date.getFullYear();\n                const m = (date.getMonth() + 1).toString().padStart(2, '0');\n                const d = date.getDate().toString().padStart(2, '0');\n                const h = date.getHours().toString().padStart(2, '0');\n                const min = date.getMinutes().toString().padStart(2, '0');\n                const s = date.getSeconds().toString().padStart(2, '0');\n                timestampPart = \"\".concat(y).concat(m).concat(d, \"_\").concat(h).concat(min).concat(s); // Corrected: Removed underscores between time components\n            } catch (e) {\n            // Keep 'NODATE' on parsing error\n            }\n        }\n        // 2. Find SN\n        let snPart = 'NOSN';\n        if (Array.isArray(block.sns) && block.sns.length > 0 && block.sns[0]) {\n            snPart = block.sns[0];\n        } else {\n            const snFromId = block.block_id.split('_')[0];\n            if (snFromId && snFromId.toLowerCase() !== 'block') {\n                snPart = snFromId;\n            }\n        }\n        return \"\".concat(timestampPart, \"GBSN\").concat(snPart, \".png\");\n    };\n    const handleImageNameSearch = (timestamps)=>{\n        if (!logParserWorker.current) {\n            toast({\n                title: \"错误\",\n                description: \"日志解析器未初始化。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (timestamps.length === 0) {\n            toast({\n                title: \"提示\",\n                description: \"没有从文件名中解析出有效的时间戳。\",\n                variant: \"default\"\n            });\n            return;\n        }\n        setIsImageNameSearching(true);\n        toast({\n            title: \"正在搜索...\",\n            description: \"根据 \".concat(timestamps.length, \" 个时间戳进行匹配。\")\n        });\n        const message = {\n            type: 'MATCH_BY_TIMESTAMP',\n            payload: {\n                timestamps\n            }\n        };\n        logParserWorker.current.postMessage(message);\n    };\n    const displayedDataChunks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[displayedDataChunks]\": ()=>{\n            if (!isSnSearching) {\n                return dataChunks;\n            }\n            return dataChunks.filter({\n                \"LogAnalysisPage.useMemo[displayedDataChunks]\": (chunk)=>selectedBlockIds.has(chunk.block_id)\n            }[\"LogAnalysisPage.useMemo[displayedDataChunks]\"]);\n        }\n    }[\"LogAnalysisPage.useMemo[displayedDataChunks]\"], [\n        isSnSearching,\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const initiateExportProcess = (exportIds)=>{\n        if (exportIds.length === 0) {\n            toast({\n                title: \"没有内容可导出\",\n                description: \"请选择至少一个数据块进行导出。\",\n                variant: \"default\"\n            });\n            return;\n        }\n        setExportProgress({\n            completed: 0,\n            total: exportIds.length\n        });\n        setGeneratedImages([]);\n        setExportQueue([\n            ...exportIds\n        ]);\n        setCurrentlyExportingBlockId(exportIds[0]);\n        toast({\n            title: \"导出已开始\",\n            description: \"准备导出 \".concat(exportIds.length, \" 个图表...\")\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (!currentlyExportingBlockId) return;\n            const processBlock = {\n                \"LogAnalysisPage.useEffect.processBlock\": async ()=>{\n                    await new Promise({\n                        \"LogAnalysisPage.useEffect.processBlock\": (resolve)=>setTimeout(resolve, 100)\n                    }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                    const container = exportTargetContainerRef.current;\n                    if (!container) {\n                        console.error(\"Export container not found.\");\n                        return;\n                    }\n                    const blockToExport = dataChunks.find({\n                        \"LogAnalysisPage.useEffect.processBlock.blockToExport\": (b)=>b.block_id === currentlyExportingBlockId\n                    }[\"LogAnalysisPage.useEffect.processBlock.blockToExport\"]);\n                    if (!blockToExport) {\n                        console.error(\"Block with ID \".concat(currentlyExportingBlockId, \" not found in dataChunks.\"));\n                        // Move to the next item in the queue even if the block is not found\n                        setExportQueue({\n                            \"LogAnalysisPage.useEffect.processBlock\": (prevQueue)=>{\n                                const newQueue = prevQueue.slice(1);\n                                setCurrentlyExportingBlockId(newQueue[0] || null);\n                                return newQueue;\n                            }\n                        }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                        return;\n                    }\n                    const chartElement = container.querySelector('[data-block-id=\"'.concat(currentlyExportingBlockId, '\"]'));\n                    if (chartElement) {\n                        try {\n                            await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_12__.waitForChartReady)(chartElement);\n                            const blob = await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_12__.generateSingleImageBlob)(chartElement);\n                            const filename = generateExportFilename(blockToExport);\n                            setGeneratedImages({\n                                \"LogAnalysisPage.useEffect.processBlock\": (prev)=>[\n                                        ...prev,\n                                        {\n                                            filename,\n                                            blob\n                                        }\n                                    ]\n                            }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                        } catch (error) {\n                            toast({\n                                title: \"图表生成失败\",\n                                description: \"无法为数据块 \".concat(currentlyExportingBlockId, \" 生成图片。\"),\n                                variant: \"destructive\"\n                            });\n                        }\n                    } else {\n                        console.warn(\"Chart element for block ID \".concat(currentlyExportingBlockId, \" not found in DOM.\"));\n                    }\n                    // Advance the queue\n                    setExportQueue({\n                        \"LogAnalysisPage.useEffect.processBlock\": (prevQueue)=>{\n                            const newQueue = prevQueue.slice(1);\n                            setCurrentlyExportingBlockId(newQueue[0] || null);\n                            return newQueue;\n                        }\n                    }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                }\n            }[\"LogAnalysisPage.useEffect.processBlock\"];\n            processBlock();\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        currentlyExportingBlockId,\n        dataChunks,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (exportProgress) {\n                setExportProgress({\n                    \"LogAnalysisPage.useEffect\": (prev)=>({\n                            ...prev,\n                            completed: generatedImages.length\n                        })\n                }[\"LogAnalysisPage.useEffect\"]);\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        generatedImages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (exportProgress && !currentlyExportingBlockId && exportQueue.length === 0) {\n                if (generatedImages.length > 0 && generatedImages.length === exportProgress.total) {\n                    const zipAndDownload = {\n                        \"LogAnalysisPage.useEffect.zipAndDownload\": async ()=>{\n                            try {\n                                await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_12__.zipAndDownloadImages)(generatedImages, 'exported_log_charts');\n                                toast({\n                                    title: \"导出成功\",\n                                    description: \"已将 \".concat(generatedImages.length, \" 个图表导出为压缩包。\")\n                                });\n                            } catch (error) {\n                                toast({\n                                    title: \"导出失败\",\n                                    description: \"无法创建或下载ZIP文件。\",\n                                    variant: \"destructive\"\n                                });\n                            } finally{\n                                setExportQueue([]);\n                                setCurrentlyExportingBlockId(null);\n                                setGeneratedImages([]);\n                                setExportProgress(null);\n                            }\n                        }\n                    }[\"LogAnalysisPage.useEffect.zipAndDownload\"];\n                    zipAndDownload();\n                } else if (exportProgress.total > 0) {\n                    toast({\n                        title: \"导出完成\",\n                        description: \"成功导出 \".concat(generatedImages.length, \" 个图表，\").concat(exportProgress.total - generatedImages.length, \" 个失败。\"),\n                        variant: \"default\"\n                    });\n                    setExportQueue([]);\n                    setCurrentlyExportingBlockId(null);\n                    setGeneratedImages([]);\n                    setExportProgress(null);\n                }\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        currentlyExportingBlockId,\n        exportProgress,\n        generatedImages,\n        exportQueue.length,\n        toast\n    ]);\n    const blockToRenderOffscreen = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[blockToRenderOffscreen]\": ()=>{\n            if (!currentlyExportingBlockId) return null;\n            return dataChunks.find({\n                \"LogAnalysisPage.useMemo[blockToRenderOffscreen]\": (b)=>b.block_id === currentlyExportingBlockId\n            }[\"LogAnalysisPage.useMemo[blockToRenderOffscreen]\"]) || null;\n        }\n    }[\"LogAnalysisPage.useMemo[blockToRenderOffscreen]\"], [\n        currentlyExportingBlockId,\n        dataChunks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"日志分析与查询\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-1 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onFilesSelected: handleFilesSelected,\n                                isProcessing: isLoading,\n                                disabled: isLoading || !!exportProgress || isImageNameSearching\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_SnSearchBar__WEBPACK_IMPORTED_MODULE_6__.SnSearchBar, {\n                                onSearch: handleSnSearch,\n                                onClear: handleClearSearch,\n                                isLoading: isLoading || isImageNameSearching\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 12\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_ImageNameSearch__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                onSearch: handleImageNameSearch,\n                                isLoading: isImageNameSearching,\n                                disabled: dataChunks.length === 0 || isLoading || !!exportProgress\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            dataChunks: displayedDataChunks,\n                            onSelectionChange: handleBlockSelectionChanged,\n                            onStartExport: initiateExportProcess,\n                            selectedBlockIds: selectedBlockIds,\n                            isSearching: isSnSearching\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            exportProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_13__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_13__.AlertTitle, {\n                        children: \"正在导出图表...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_13__.AlertDescription, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_14__.Progress, {\n                                    value: exportProgress.completed / exportProgress.total * 100,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\".concat(exportProgress.completed, \" / \").concat(exportProgress.total)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 427,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_BatchExportCSV__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    selectedBlocks: selectedBlocks\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow mt-4\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"正在处理文件...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"h-full flex items-center justify-center bg-destructive/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-destructive font-semibold\",\n                                children: \"发生错误\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this) : isSnSearching && displayedDataChunks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                '未找到与 \"',\n                                searchQuery,\n                                '\" 相关的日志块。'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 16\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 14\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 12\n                }, this) : chartDataForView.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    dataChunks: chartDataForView,\n                    selectedBlockIds: Array.from(selectedBlockIds),\n                    onBlockSelect: ()=>{},\n                    isHighlighted: isSnSearching\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: dataChunks.length > 0 ? \"请从左侧选择数据块以显示图表\" : \"请先上传日志文件\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: exportTargetContainerRef,\n                style: {\n                    position: 'absolute',\n                    left: '-9999px',\n                    top: '-9999px',\n                    width: '1200px',\n                    height: '800px'\n                },\n                children: blockToRenderOffscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    dataChunks: [\n                        blockToRenderOffscreen\n                    ],\n                    selectedBlockIds: [\n                        blockToRenderOffscreen.block_id\n                    ],\n                    onBlockSelect: ()=>{}\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 485,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, this);\n}\n_s(LogAnalysisPage, \"R1fGctfGnV+52OYaOD91BrHcg+Y=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = LogAnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"LogAnalysisPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx\n"));

/***/ })

});