"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/drawing-board/Canvas.tsx":
/*!*********************************************!*\
  !*** ./components/drawing-board/Canvas.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst Canvas = (param)=>{\n    let { width, height, grid, shapes, addShape, deleteShape, backgroundColor } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Canvas.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            const dpr = window.devicePixelRatio || 1;\n            // Set the actual size of the canvas in memory to match the device's pixel ratio.\n            canvas.width = width * dpr;\n            canvas.height = height * dpr;\n            // Set the display size of the canvas.\n            canvas.style.width = \"\".concat(width, \"px\");\n            canvas.style.height = \"\".concat(height, \"px\");\n            // Scale the context to ensure all drawing operations are scaled up.\n            ctx.scale(dpr, dpr);\n            // Clear canvas with background color.\n            ctx.fillStyle = backgroundColor;\n            ctx.fillRect(0, 0, width, height);\n            // Draw grid\n            drawGrid(ctx);\n            // Define drawing functions inside useEffect to ensure they are not part of the component's\n            // top-level scope, which can cause issues with SSR in Next.js.\n            const getShapeCenter = {\n                \"Canvas.useEffect.getShapeCenter\": (shape)=>{\n                    const cellWidth = width / grid.cols;\n                    const cellHeight = height / grid.rows;\n                    const radius = shape.diameter / 2;\n                    const cellTopLeftX = shape.cell.col * cellWidth;\n                    const cellTopLeftY = shape.cell.row * cellHeight;\n                    let cx, cy;\n                    switch(shape.alignment){\n                        case 'center':\n                            cx = cellTopLeftX + cellWidth / 2;\n                            cy = cellTopLeftY + cellHeight / 2;\n                            break;\n                        case 'topLeft':\n                            cx = cellTopLeftX + radius;\n                            cy = cellTopLeftY + radius;\n                            break;\n                        case 'coordinates':\n                            if (shape.coordinates) {\n                                cx = cellTopLeftX + shape.coordinates.x;\n                                cy = cellTopLeftY + shape.coordinates.y;\n                            } else {\n                                cx = cellTopLeftX + cellWidth / 2;\n                                cy = cellTopLeftY + cellHeight / 2;\n                            }\n                            break;\n                    }\n                    return {\n                        cx,\n                        cy\n                    };\n                }\n            }[\"Canvas.useEffect.getShapeCenter\"];\n            const drawMtfPattern = {\n                \"Canvas.useEffect.drawMtfPattern\": function(ictx, x, y, size, color, idpr) {\n                    let lineWidth = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : 2;\n                    const tempCanvas = document.createElement('canvas');\n                    tempCanvas.width = size * idpr;\n                    tempCanvas.height = size * idpr;\n                    const tempCtx = tempCanvas.getContext('2d');\n                    if (!tempCtx) return;\n                    tempCtx.scale(idpr, idpr);\n                    tempCtx.fillStyle = '#FFFFFF';\n                    tempCtx.fillRect(0, 0, size, size);\n                    tempCtx.fillStyle = color;\n                    tempCtx.imageSmoothingEnabled = false;\n                    const center_x = size / 2;\n                    const center_y = size / 2;\n                    // Ensure the gap width is determined by the line width.\n                    const gap = lineWidth;\n                    const step = lineWidth + gap; // This is 2 * lineWidth\n                    // Central cross\n                    tempCtx.fillRect(Math.round(center_x - lineWidth / 2), 0, Math.round(lineWidth), Math.round(size));\n                    tempCtx.fillRect(0, Math.round(center_y - lineWidth / 2), Math.round(size), Math.round(lineWidth));\n                    // Top-left quadrant (vertical lines)\n                    for(let i = center_x - lineWidth / 2 - step; i > -lineWidth; i -= step){\n                        tempCtx.fillRect(Math.round(i), 0, Math.round(lineWidth), Math.round(center_y - lineWidth / 2));\n                    }\n                    // Bottom-left quadrant (horizontal lines)\n                    for(let i = center_y + lineWidth / 2 + gap; i < size; i += step){\n                        tempCtx.fillRect(0, Math.round(i), Math.round(center_x - lineWidth / 2), Math.round(lineWidth));\n                    }\n                    // Top-right quadrant (horizontal lines)\n                    for(let i = center_y - lineWidth / 2 - step; i > -lineWidth; i -= step){\n                        tempCtx.fillRect(Math.round(center_x + lineWidth / 2), Math.round(i), Math.round(size - (center_x + lineWidth / 2)), Math.round(lineWidth));\n                    }\n                    // Bottom-right quadrant (vertical lines)\n                    for(let i = center_x + lineWidth / 2 + gap; i < size; i += step){\n                        tempCtx.fillRect(Math.round(i), Math.round(center_y + lineWidth / 2), Math.round(lineWidth), Math.round(size - (center_y + lineWidth / 2)));\n                    }\n                    ictx.imageSmoothingEnabled = false;\n                    ictx.drawImage(tempCanvas, x, y, size, size);\n                }\n            }[\"Canvas.useEffect.drawMtfPattern\"];\n            const drawShape = {\n                \"Canvas.useEffect.drawShape\": (ictx, shape, idpr)=>{\n                    const { cx, cy } = getShapeCenter(shape);\n                    const radius = shape.diameter / 2;\n                    if (shape.type === 'circle') {\n                        ictx.save();\n                        // 禁用抗锯齿以避免边缘颜色混合\n                        ictx.imageSmoothingEnabled = false;\n                        // 使用像素级精确绘制来避免边缘颜色问题\n                        const tempCanvas = document.createElement('canvas');\n                        const tempSize = Math.ceil(shape.diameter * idpr) + 2; // 添加一些边距\n                        tempCanvas.width = tempSize;\n                        tempCanvas.height = tempSize;\n                        const tempCtx = tempCanvas.getContext('2d');\n                        if (tempCtx) {\n                            // 在临时画布上绘制圆形\n                            tempCtx.imageSmoothingEnabled = false;\n                            tempCtx.fillStyle = shape.color;\n                            const tempRadius = shape.diameter * idpr / 2;\n                            const tempCenter = tempSize / 2;\n                            // 使用像素级绘制，避免抗锯齿\n                            for(let x = 0; x < tempSize; x++){\n                                for(let y = 0; y < tempSize; y++){\n                                    const distance = Math.sqrt((x - tempCenter) ** 2 + (y - tempCenter) ** 2);\n                                    if (distance <= tempRadius) {\n                                        tempCtx.fillRect(x, y, 1, 1);\n                                    }\n                                }\n                            }\n                            // 将临时画布绘制到主画布上\n                            ictx.drawImage(tempCanvas, cx - shape.diameter / 2, cy - shape.diameter / 2, shape.diameter, shape.diameter);\n                        } else {\n                            // 如果临时画布创建失败，使用原始方法\n                            ictx.fillStyle = shape.color;\n                            ictx.beginPath();\n                            ictx.arc(cx, cy, radius, 0, 2 * Math.PI);\n                            ictx.fill();\n                        }\n                        ictx.restore();\n                    } else if (shape.type === 'square') {\n                        drawMtfPattern(ictx, cx - radius, cy - radius, shape.diameter, shape.color, idpr, shape.mtfWidth);\n                    }\n                }\n            }[\"Canvas.useEffect.drawShape\"];\n            // Draw shapes\n            shapes.forEach({\n                \"Canvas.useEffect\": (shape)=>drawShape(ctx, shape, dpr)\n            }[\"Canvas.useEffect\"]);\n        }\n    }[\"Canvas.useEffect\"], [\n        width,\n        height,\n        grid,\n        shapes,\n        backgroundColor\n    ]);\n    const drawGrid = (ctx)=>{\n        ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';\n        ctx.setLineDash([\n            5,\n            5\n        ]);\n        ctx.lineWidth = 1;\n        const cellWidth = width / grid.cols;\n        const cellHeight = height / grid.rows;\n        for(let i = 1; i < grid.cols; i++){\n            ctx.beginPath();\n            ctx.moveTo(i * cellWidth, 0);\n            ctx.lineTo(i * cellWidth, height);\n            ctx.stroke();\n        }\n        for(let i = 1; i < grid.rows; i++){\n            ctx.beginPath();\n            ctx.moveTo(0, i * cellHeight);\n            ctx.lineTo(width, i * cellHeight);\n            ctx.stroke();\n        }\n        ctx.setLineDash([]);\n    };\n    const findShapeAt = (x, y)=>{\n        // This function is called from an event handler, so it's safe to be outside useEffect.\n        // However, it needs to calculate shape centers, so we duplicate that logic or pass it.\n        // For simplicity, we can just call getShapeCenter which is now defined in the component scope.\n        // But getShapeCenter is NOT in the component scope anymore.\n        // So we must redefine it or move findShapeAt into useEffect as well, which is not ideal.\n        // Let's redefine the calculation logic here.\n        const cellWidth = width / grid.cols;\n        const cellHeight = height / grid.rows;\n        // Iterate in reverse to find the top-most shape\n        for(let i = shapes.length - 1; i >= 0; i--){\n            const shape = shapes[i];\n            const radius = shape.diameter / 2;\n            const cellTopLeftX = shape.cell.col * cellWidth;\n            const cellTopLeftY = shape.cell.row * cellHeight;\n            let cx, cy;\n            switch(shape.alignment){\n                case 'center':\n                    cx = cellTopLeftX + cellWidth / 2;\n                    cy = cellTopLeftY + cellHeight / 2;\n                    break;\n                case 'topLeft':\n                    cx = cellTopLeftX + radius;\n                    cy = cellTopLeftY + radius;\n                    break;\n                case 'coordinates':\n                    if (shape.coordinates) {\n                        cx = cellTopLeftX + shape.coordinates.x;\n                        cy = cellTopLeftY + shape.coordinates.y;\n                    } else {\n                        cx = cellTopLeftX + cellWidth / 2;\n                        cy = cellTopLeftY + cellHeight / 2;\n                    }\n                    break;\n            }\n            if (shape.type === 'circle') {\n                const distance = Math.sqrt((x - cx) ** 2 + (y - cy) ** 2);\n                if (distance <= radius) {\n                    return shape;\n                }\n            } else if (shape.type === 'square') {\n                if (x >= cx - radius && x <= cx + radius && y >= cy - radius && y <= cy + radius) {\n                    return shape;\n                }\n            }\n        }\n        return undefined;\n    };\n    const handleCanvasInteraction = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        if (event.button === 2) {\n            event.preventDefault();\n            const shapeToDelete = findShapeAt(x, y);\n            if (shapeToDelete) {\n                deleteShape(shapeToDelete.id);\n            }\n            return;\n        }\n        // Left-click to add shape\n        const cellWidth = width / grid.cols;\n        const cellHeight = height / grid.rows;\n        const col = Math.floor(x / cellWidth);\n        const row = Math.floor(y / cellHeight);\n        addShape({\n            row,\n            col\n        }, 'center');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        width: width,\n        height: height,\n        onMouseDown: handleCanvasInteraction,\n        onContextMenu: (e)=>e.preventDefault(),\n        className: \"border border-gray-400\"\n    }, void 0, false, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\drawing-board\\\\Canvas.tsx\",\n        lineNumber: 283,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Canvas, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c = Canvas;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Canvas);\nvar _c;\n$RefreshReg$(_c, \"Canvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZHJhd2luZy1ib2FyZC9DYW52YXMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQWFqRCxNQUFNRyxTQUFnQztRQUFDLEVBQUVDLEtBQUssRUFBRUMsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxXQUFXLEVBQUVDLGVBQWUsRUFBRTs7SUFDNUcsTUFBTUMsWUFBWVQsNkNBQU1BLENBQW9CO0lBRTVDRCxnREFBU0E7NEJBQUM7WUFDUixNQUFNVyxTQUFTRCxVQUFVRSxPQUFPO1lBQ2hDLElBQUksQ0FBQ0QsUUFBUTtZQUNiLE1BQU1FLE1BQU1GLE9BQU9HLFVBQVUsQ0FBQztZQUM5QixJQUFJLENBQUNELEtBQUs7WUFFVixNQUFNRSxNQUFNQyxPQUFPQyxnQkFBZ0IsSUFBSTtZQUV2QyxpRkFBaUY7WUFDakZOLE9BQU9SLEtBQUssR0FBR0EsUUFBUVk7WUFDdkJKLE9BQU9QLE1BQU0sR0FBR0EsU0FBU1c7WUFFekIsc0NBQXNDO1lBQ3RDSixPQUFPTyxLQUFLLENBQUNmLEtBQUssR0FBRyxHQUFTLE9BQU5BLE9BQU07WUFDOUJRLE9BQU9PLEtBQUssQ0FBQ2QsTUFBTSxHQUFHLEdBQVUsT0FBUEEsUUFBTztZQUVoQyxvRUFBb0U7WUFDcEVTLElBQUlNLEtBQUssQ0FBQ0osS0FBS0E7WUFFZixzQ0FBc0M7WUFDdENGLElBQUlPLFNBQVMsR0FBR1g7WUFDaEJJLElBQUlRLFFBQVEsQ0FBQyxHQUFHLEdBQUdsQixPQUFPQztZQUUxQixZQUFZO1lBQ1prQixTQUFTVDtZQUVULDJGQUEyRjtZQUMzRiwrREFBK0Q7WUFDL0QsTUFBTVU7bURBQWlCLENBQUNDO29CQUN0QixNQUFNQyxZQUFZdEIsUUFBUUUsS0FBS3FCLElBQUk7b0JBQ25DLE1BQU1DLGFBQWF2QixTQUFTQyxLQUFLdUIsSUFBSTtvQkFDckMsTUFBTUMsU0FBU0wsTUFBTU0sUUFBUSxHQUFHO29CQUNoQyxNQUFNQyxlQUFlUCxNQUFNUSxJQUFJLENBQUNDLEdBQUcsR0FBR1I7b0JBQ3RDLE1BQU1TLGVBQWVWLE1BQU1RLElBQUksQ0FBQ0csR0FBRyxHQUFHUjtvQkFDdEMsSUFBSVMsSUFBSUM7b0JBQ1IsT0FBUWIsTUFBTWMsU0FBUzt3QkFDbkIsS0FBSzs0QkFDREYsS0FBS0wsZUFBZU4sWUFBWTs0QkFDaENZLEtBQUtILGVBQWVQLGFBQWE7NEJBQ2pDO3dCQUNKLEtBQUs7NEJBQ0RTLEtBQUtMLGVBQWVGOzRCQUNwQlEsS0FBS0gsZUFBZUw7NEJBQ3BCO3dCQUNKLEtBQUs7NEJBQ0QsSUFBSUwsTUFBTWUsV0FBVyxFQUFFO2dDQUNuQkgsS0FBS0wsZUFBZVAsTUFBTWUsV0FBVyxDQUFDQyxDQUFDO2dDQUN2Q0gsS0FBS0gsZUFBZVYsTUFBTWUsV0FBVyxDQUFDRSxDQUFDOzRCQUMzQyxPQUFPO2dDQUNITCxLQUFLTCxlQUFlTixZQUFZO2dDQUNoQ1ksS0FBS0gsZUFBZVAsYUFBYTs0QkFDckM7NEJBQ0E7b0JBQ1I7b0JBQ0EsT0FBTzt3QkFBRVM7d0JBQUlDO29CQUFHO2dCQUNsQjs7WUFFQSxNQUFNSzttREFBaUIsU0FBQ0MsTUFBZ0NILEdBQVdDLEdBQVdHLE1BQWNDLE9BQWVDO3dCQUFjQyw2RUFBb0I7b0JBQzNJLE1BQU1DLGFBQWFDLFNBQVNDLGFBQWEsQ0FBQztvQkFDMUNGLFdBQVc3QyxLQUFLLEdBQUd5QyxPQUFPRTtvQkFDMUJFLFdBQVc1QyxNQUFNLEdBQUd3QyxPQUFPRTtvQkFDM0IsTUFBTUssVUFBVUgsV0FBV2xDLFVBQVUsQ0FBQztvQkFDdEMsSUFBSSxDQUFDcUMsU0FBUztvQkFDZEEsUUFBUWhDLEtBQUssQ0FBQzJCLE1BQU1BO29CQUNwQkssUUFBUS9CLFNBQVMsR0FBRztvQkFDcEIrQixRQUFROUIsUUFBUSxDQUFDLEdBQUcsR0FBR3VCLE1BQU1BO29CQUM3Qk8sUUFBUS9CLFNBQVMsR0FBR3lCO29CQUNwQk0sUUFBUUMscUJBQXFCLEdBQUc7b0JBQ2hDLE1BQU1DLFdBQVdULE9BQU87b0JBQ3hCLE1BQU1VLFdBQVdWLE9BQU87b0JBRXhCLHdEQUF3RDtvQkFDeEQsTUFBTVcsTUFBTVI7b0JBQ1osTUFBTVMsT0FBT1QsWUFBWVEsS0FBSyx3QkFBd0I7b0JBRXRELGdCQUFnQjtvQkFDaEJKLFFBQVE5QixRQUFRLENBQUNvQyxLQUFLQyxLQUFLLENBQUNMLFdBQVdOLFlBQVksSUFBSSxHQUFHVSxLQUFLQyxLQUFLLENBQUNYLFlBQVlVLEtBQUtDLEtBQUssQ0FBQ2Q7b0JBQzVGTyxRQUFROUIsUUFBUSxDQUFDLEdBQUdvQyxLQUFLQyxLQUFLLENBQUNKLFdBQVdQLFlBQVksSUFBSVUsS0FBS0MsS0FBSyxDQUFDZCxPQUFPYSxLQUFLQyxLQUFLLENBQUNYO29CQUV2RixxQ0FBcUM7b0JBQ3JDLElBQUssSUFBSVksSUFBSU4sV0FBV04sWUFBWSxJQUFJUyxNQUFNRyxJQUFJLENBQUNaLFdBQVdZLEtBQUtILEtBQU07d0JBQ3ZFTCxRQUFROUIsUUFBUSxDQUFDb0MsS0FBS0MsS0FBSyxDQUFDQyxJQUFJLEdBQUdGLEtBQUtDLEtBQUssQ0FBQ1gsWUFBWVUsS0FBS0MsS0FBSyxDQUFDSixXQUFXUCxZQUFZO29CQUM5RjtvQkFFQSwwQ0FBMEM7b0JBQzFDLElBQUssSUFBSVksSUFBSUwsV0FBV1AsWUFBWSxJQUFJUSxLQUFLSSxJQUFJZixNQUFNZSxLQUFLSCxLQUFNO3dCQUNoRUwsUUFBUTlCLFFBQVEsQ0FBQyxHQUFHb0MsS0FBS0MsS0FBSyxDQUFDQyxJQUFJRixLQUFLQyxLQUFLLENBQUNMLFdBQVdOLFlBQVksSUFBSVUsS0FBS0MsS0FBSyxDQUFDWDtvQkFDdEY7b0JBRUEsd0NBQXdDO29CQUN4QyxJQUFLLElBQUlZLElBQUlMLFdBQVdQLFlBQVksSUFBSVMsTUFBTUcsSUFBSSxDQUFDWixXQUFXWSxLQUFLSCxLQUFNO3dCQUN2RUwsUUFBUTlCLFFBQVEsQ0FBQ29DLEtBQUtDLEtBQUssQ0FBQ0wsV0FBV04sWUFBWSxJQUFJVSxLQUFLQyxLQUFLLENBQUNDLElBQUlGLEtBQUtDLEtBQUssQ0FBQ2QsT0FBUVMsQ0FBQUEsV0FBV04sWUFBWSxLQUFLVSxLQUFLQyxLQUFLLENBQUNYO29CQUNsSTtvQkFFQSx5Q0FBeUM7b0JBQ3pDLElBQUssSUFBSVksSUFBSU4sV0FBV04sWUFBWSxJQUFJUSxLQUFLSSxJQUFJZixNQUFNZSxLQUFLSCxLQUFNO3dCQUNoRUwsUUFBUTlCLFFBQVEsQ0FBQ29DLEtBQUtDLEtBQUssQ0FBQ0MsSUFBSUYsS0FBS0MsS0FBSyxDQUFDSixXQUFXUCxZQUFZLElBQUlVLEtBQUtDLEtBQUssQ0FBQ1gsWUFBWVUsS0FBS0MsS0FBSyxDQUFDZCxPQUFRVSxDQUFBQSxXQUFXUCxZQUFZO29CQUN6STtvQkFFQUosS0FBS1MscUJBQXFCLEdBQUc7b0JBQzdCVCxLQUFLaUIsU0FBUyxDQUFDWixZQUFZUixHQUFHQyxHQUFHRyxNQUFNQTtnQkFDekM7O1lBRUEsTUFBTWlCOzhDQUFZLENBQUNsQixNQUFnQ25CLE9BQWNzQjtvQkFDL0QsTUFBTSxFQUFFVixFQUFFLEVBQUVDLEVBQUUsRUFBRSxHQUFHZCxlQUFlQztvQkFDbEMsTUFBTUssU0FBU0wsTUFBTU0sUUFBUSxHQUFHO29CQUNoQyxJQUFJTixNQUFNc0MsSUFBSSxLQUFLLFVBQVU7d0JBQzNCbkIsS0FBS29CLElBQUk7d0JBRVQsaUJBQWlCO3dCQUNqQnBCLEtBQUtTLHFCQUFxQixHQUFHO3dCQUU3QixxQkFBcUI7d0JBQ3JCLE1BQU1KLGFBQWFDLFNBQVNDLGFBQWEsQ0FBQzt3QkFDMUMsTUFBTWMsV0FBV1AsS0FBS1EsSUFBSSxDQUFDekMsTUFBTU0sUUFBUSxHQUFHZ0IsUUFBUSxHQUFHLFNBQVM7d0JBQ2hFRSxXQUFXN0MsS0FBSyxHQUFHNkQ7d0JBQ25CaEIsV0FBVzVDLE1BQU0sR0FBRzREO3dCQUNwQixNQUFNYixVQUFVSCxXQUFXbEMsVUFBVSxDQUFDO3dCQUV0QyxJQUFJcUMsU0FBUzs0QkFDWCxhQUFhOzRCQUNiQSxRQUFRQyxxQkFBcUIsR0FBRzs0QkFDaENELFFBQVEvQixTQUFTLEdBQUdJLE1BQU1xQixLQUFLOzRCQUUvQixNQUFNcUIsYUFBYSxNQUFPcEMsUUFBUSxHQUFHZ0IsT0FBUTs0QkFDN0MsTUFBTXFCLGFBQWFILFdBQVc7NEJBRTlCLGdCQUFnQjs0QkFDaEIsSUFBSyxJQUFJeEIsSUFBSSxHQUFHQSxJQUFJd0IsVUFBVXhCLElBQUs7Z0NBQ2pDLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJdUIsVUFBVXZCLElBQUs7b0NBQ2pDLE1BQU0yQixXQUFXWCxLQUFLWSxJQUFJLENBQUMsQ0FBQzdCLElBQUkyQixVQUFTLEtBQU0sSUFBSSxDQUFDMUIsSUFBSTBCLFVBQVMsS0FBTTtvQ0FDdkUsSUFBSUMsWUFBWUYsWUFBWTt3Q0FDMUJmLFFBQVE5QixRQUFRLENBQUNtQixHQUFHQyxHQUFHLEdBQUc7b0NBQzVCO2dDQUNGOzRCQUNGOzRCQUVBLGVBQWU7NEJBQ2ZFLEtBQUtpQixTQUFTLENBQ1paLFlBQ0FaLEtBQUtaLE1BQU1NLFFBQVEsR0FBRyxHQUN0Qk8sS0FBS2IsTUFBTU0sUUFBUSxHQUFHLEdBQ3RCTixNQUFNTSxRQUFRLEVBQ2ROLE1BQU1NLFFBQVE7d0JBRWxCLE9BQU87NEJBQ0wsb0JBQW9COzRCQUNwQmEsS0FBS3ZCLFNBQVMsR0FBR0ksTUFBTXFCLEtBQUs7NEJBQzVCRixLQUFLMkIsU0FBUzs0QkFDZDNCLEtBQUs0QixHQUFHLENBQUNuQyxJQUFJQyxJQUFJUixRQUFRLEdBQUcsSUFBSTRCLEtBQUtlLEVBQUU7NEJBQ3ZDN0IsS0FBSzhCLElBQUk7d0JBQ1g7d0JBRUE5QixLQUFLK0IsT0FBTztvQkFDZCxPQUFPLElBQUlsRCxNQUFNc0MsSUFBSSxLQUFLLFVBQVU7d0JBQ2xDcEIsZUFBZUMsTUFBTVAsS0FBS1AsUUFBUVEsS0FBS1IsUUFBUUwsTUFBTU0sUUFBUSxFQUFFTixNQUFNcUIsS0FBSyxFQUFFQyxNQUFNdEIsTUFBTW1ELFFBQVE7b0JBQ2xHO2dCQUNGOztZQUVBLGNBQWM7WUFDZHJFLE9BQU9zRSxPQUFPO29DQUFDcEQsQ0FBQUEsUUFBU3FDLFVBQVVoRCxLQUFLVyxPQUFPVDs7UUFFaEQ7MkJBQUc7UUFBQ1o7UUFBT0M7UUFBUUM7UUFBTUM7UUFBUUc7S0FBZ0I7SUFFakQsTUFBTWEsV0FBVyxDQUFDVDtRQUNoQkEsSUFBSWdFLFdBQVcsR0FBRztRQUNsQmhFLElBQUlpRSxXQUFXLENBQUM7WUFBQztZQUFHO1NBQUU7UUFDdEJqRSxJQUFJa0MsU0FBUyxHQUFHO1FBRWhCLE1BQU10QixZQUFZdEIsUUFBUUUsS0FBS3FCLElBQUk7UUFDbkMsTUFBTUMsYUFBYXZCLFNBQVNDLEtBQUt1QixJQUFJO1FBRXJDLElBQUssSUFBSStCLElBQUksR0FBR0EsSUFBSXRELEtBQUtxQixJQUFJLEVBQUVpQyxJQUFLO1lBQ2xDOUMsSUFBSXlELFNBQVM7WUFDYnpELElBQUlrRSxNQUFNLENBQUNwQixJQUFJbEMsV0FBVztZQUMxQlosSUFBSW1FLE1BQU0sQ0FBQ3JCLElBQUlsQyxXQUFXckI7WUFDMUJTLElBQUlvRSxNQUFNO1FBQ1o7UUFFQSxJQUFLLElBQUl0QixJQUFJLEdBQUdBLElBQUl0RCxLQUFLdUIsSUFBSSxFQUFFK0IsSUFBSztZQUNsQzlDLElBQUl5RCxTQUFTO1lBQ2J6RCxJQUFJa0UsTUFBTSxDQUFDLEdBQUdwQixJQUFJaEM7WUFDbEJkLElBQUltRSxNQUFNLENBQUM3RSxPQUFPd0QsSUFBSWhDO1lBQ3RCZCxJQUFJb0UsTUFBTTtRQUNaO1FBQ0FwRSxJQUFJaUUsV0FBVyxDQUFDLEVBQUU7SUFDcEI7SUFFQSxNQUFNSSxjQUFjLENBQUMxQyxHQUFXQztRQUM5Qix1RkFBdUY7UUFDdkYsdUZBQXVGO1FBQ3ZGLCtGQUErRjtRQUMvRiw0REFBNEQ7UUFDNUQseUZBQXlGO1FBQ3pGLDZDQUE2QztRQUM3QyxNQUFNaEIsWUFBWXRCLFFBQVFFLEtBQUtxQixJQUFJO1FBQ25DLE1BQU1DLGFBQWF2QixTQUFTQyxLQUFLdUIsSUFBSTtRQUVyQyxnREFBZ0Q7UUFDaEQsSUFBSyxJQUFJK0IsSUFBSXJELE9BQU82RSxNQUFNLEdBQUcsR0FBR3hCLEtBQUssR0FBR0EsSUFBSztZQUN6QyxNQUFNbkMsUUFBUWxCLE1BQU0sQ0FBQ3FELEVBQUU7WUFDdkIsTUFBTTlCLFNBQVNMLE1BQU1NLFFBQVEsR0FBRztZQUNoQyxNQUFNQyxlQUFlUCxNQUFNUSxJQUFJLENBQUNDLEdBQUcsR0FBR1I7WUFDdEMsTUFBTVMsZUFBZVYsTUFBTVEsSUFBSSxDQUFDRyxHQUFHLEdBQUdSO1lBQ3RDLElBQUlTLElBQUlDO1lBQ1IsT0FBUWIsTUFBTWMsU0FBUztnQkFDbkIsS0FBSztvQkFDREYsS0FBS0wsZUFBZU4sWUFBWTtvQkFDaENZLEtBQUtILGVBQWVQLGFBQWE7b0JBQ2pDO2dCQUNKLEtBQUs7b0JBQ0RTLEtBQUtMLGVBQWVGO29CQUNwQlEsS0FBS0gsZUFBZUw7b0JBQ3BCO2dCQUNKLEtBQUs7b0JBQ0QsSUFBSUwsTUFBTWUsV0FBVyxFQUFFO3dCQUNuQkgsS0FBS0wsZUFBZVAsTUFBTWUsV0FBVyxDQUFDQyxDQUFDO3dCQUN2Q0gsS0FBS0gsZUFBZVYsTUFBTWUsV0FBVyxDQUFDRSxDQUFDO29CQUMzQyxPQUFPO3dCQUNITCxLQUFLTCxlQUFlTixZQUFZO3dCQUNoQ1ksS0FBS0gsZUFBZVAsYUFBYTtvQkFDckM7b0JBQ0E7WUFDUjtZQUVBLElBQUlILE1BQU1zQyxJQUFJLEtBQUssVUFBVTtnQkFDekIsTUFBTU0sV0FBV1gsS0FBS1ksSUFBSSxDQUFDLENBQUM3QixJQUFJSixFQUFDLEtBQU0sSUFBSSxDQUFDSyxJQUFJSixFQUFDLEtBQU07Z0JBQ3ZELElBQUkrQixZQUFZdkMsUUFBUTtvQkFDcEIsT0FBT0w7Z0JBQ1g7WUFDSixPQUFPLElBQUlBLE1BQU1zQyxJQUFJLEtBQUssVUFBVTtnQkFDaEMsSUFBSXRCLEtBQUtKLEtBQUtQLFVBQVVXLEtBQUtKLEtBQUtQLFVBQVVZLEtBQUtKLEtBQUtSLFVBQVVZLEtBQUtKLEtBQUtSLFFBQVE7b0JBQzlFLE9BQU9MO2dCQUNYO1lBQ0o7UUFDSjtRQUNBLE9BQU80RDtJQUNUO0lBRUEsTUFBTUMsMEJBQTBCLENBQUNDO1FBQy9CLE1BQU0zRSxTQUFTRCxVQUFVRSxPQUFPO1FBQ2hDLElBQUksQ0FBQ0QsUUFBUTtRQUNiLE1BQU00RSxPQUFPNUUsT0FBTzZFLHFCQUFxQjtRQUN6QyxNQUFNaEQsSUFBSThDLE1BQU1HLE9BQU8sR0FBR0YsS0FBS0csSUFBSTtRQUNuQyxNQUFNakQsSUFBSTZDLE1BQU1LLE9BQU8sR0FBR0osS0FBS0ssR0FBRztRQUVsQyxJQUFJTixNQUFNTyxNQUFNLEtBQUssR0FBRztZQUNwQlAsTUFBTVEsY0FBYztZQUNwQixNQUFNQyxnQkFBZ0JiLFlBQVkxQyxHQUFHQztZQUNyQyxJQUFJc0QsZUFBZTtnQkFDZnZGLFlBQVl1RixjQUFjQyxFQUFFO1lBQ2hDO1lBQ0E7UUFDSjtRQUVBLDBCQUEwQjtRQUMxQixNQUFNdkUsWUFBWXRCLFFBQVFFLEtBQUtxQixJQUFJO1FBQ25DLE1BQU1DLGFBQWF2QixTQUFTQyxLQUFLdUIsSUFBSTtRQUNyQyxNQUFNSyxNQUFNd0IsS0FBS3dDLEtBQUssQ0FBQ3pELElBQUlmO1FBQzNCLE1BQU1VLE1BQU1zQixLQUFLd0MsS0FBSyxDQUFDeEQsSUFBSWQ7UUFDM0JwQixTQUFTO1lBQUU0QjtZQUFLRjtRQUFJLEdBQUc7SUFDekI7SUFFQSxxQkFDRSw4REFBQ3RCO1FBQ0N1RixLQUFLeEY7UUFDTFAsT0FBT0E7UUFDUEMsUUFBUUE7UUFDUitGLGFBQWFkO1FBQ2JlLGVBQWUsQ0FBQ0MsSUFBTUEsRUFBRVAsY0FBYztRQUN0Q1EsV0FBVTs7Ozs7O0FBR2hCO0dBcFJNcEc7S0FBQUE7QUFzUk4saUVBQWVBLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxob3RlbC1kYXNoYm9hcmRcXGNvbXBvbmVudHNcXGRyYXdpbmctYm9hcmRcXENhbnZhcy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IFNoYXBlLCBBbGlnbm1lbnQgfSBmcm9tICdAL3R5cGVzL2RyYXdpbmctYm9hcmQnO1xyXG5cclxuaW50ZXJmYWNlIENhbnZhc1Byb3BzIHtcclxuICB3aWR0aDogbnVtYmVyO1xyXG4gIGhlaWdodDogbnVtYmVyO1xyXG4gIGdyaWQ6IHsgcm93czogbnVtYmVyOyBjb2xzOiBudW1iZXIgfTtcclxuICBzaGFwZXM6IFNoYXBlW107XHJcbiAgYWRkU2hhcGU6IChjZWxsOiB7IHJvdzogbnVtYmVyOyBjb2w6IG51bWJlciB9LCBhbGlnbm1lbnQ6IEFsaWdubWVudCwgY29vcmRpbmF0ZXM/OiB7IHg6IG51bWJlcjsgeTogbnVtYmVyIH0pID0+IHZvaWQ7XHJcbiAgZGVsZXRlU2hhcGU6IChzaGFwZUlkOiBudW1iZXIpID0+IHZvaWQ7XHJcbiAgYmFja2dyb3VuZENvbG9yOiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IENhbnZhczogUmVhY3QuRkM8Q2FudmFzUHJvcHM+ID0gKHsgd2lkdGgsIGhlaWdodCwgZ3JpZCwgc2hhcGVzLCBhZGRTaGFwZSwgZGVsZXRlU2hhcGUsIGJhY2tncm91bmRDb2xvciB9KSA9PiB7XHJcbiAgY29uc3QgY2FudmFzUmVmID0gdXNlUmVmPEhUTUxDYW52YXNFbGVtZW50PihudWxsKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGNhbnZhcyA9IGNhbnZhc1JlZi5jdXJyZW50O1xyXG4gICAgaWYgKCFjYW52YXMpIHJldHVybjtcclxuICAgIGNvbnN0IGN0eCA9IGNhbnZhcy5nZXRDb250ZXh0KCcyZCcpO1xyXG4gICAgaWYgKCFjdHgpIHJldHVybjtcclxuXHJcbiAgICBjb25zdCBkcHIgPSB3aW5kb3cuZGV2aWNlUGl4ZWxSYXRpbyB8fCAxO1xyXG5cclxuICAgIC8vIFNldCB0aGUgYWN0dWFsIHNpemUgb2YgdGhlIGNhbnZhcyBpbiBtZW1vcnkgdG8gbWF0Y2ggdGhlIGRldmljZSdzIHBpeGVsIHJhdGlvLlxyXG4gICAgY2FudmFzLndpZHRoID0gd2lkdGggKiBkcHI7XHJcbiAgICBjYW52YXMuaGVpZ2h0ID0gaGVpZ2h0ICogZHByO1xyXG5cclxuICAgIC8vIFNldCB0aGUgZGlzcGxheSBzaXplIG9mIHRoZSBjYW52YXMuXHJcbiAgICBjYW52YXMuc3R5bGUud2lkdGggPSBgJHt3aWR0aH1weGA7XHJcbiAgICBjYW52YXMuc3R5bGUuaGVpZ2h0ID0gYCR7aGVpZ2h0fXB4YDtcclxuXHJcbiAgICAvLyBTY2FsZSB0aGUgY29udGV4dCB0byBlbnN1cmUgYWxsIGRyYXdpbmcgb3BlcmF0aW9ucyBhcmUgc2NhbGVkIHVwLlxyXG4gICAgY3R4LnNjYWxlKGRwciwgZHByKTtcclxuXHJcbiAgICAvLyBDbGVhciBjYW52YXMgd2l0aCBiYWNrZ3JvdW5kIGNvbG9yLlxyXG4gICAgY3R4LmZpbGxTdHlsZSA9IGJhY2tncm91bmRDb2xvcjtcclxuICAgIGN0eC5maWxsUmVjdCgwLCAwLCB3aWR0aCwgaGVpZ2h0KTtcclxuXHJcbiAgICAvLyBEcmF3IGdyaWRcclxuICAgIGRyYXdHcmlkKGN0eCk7XHJcblxyXG4gICAgLy8gRGVmaW5lIGRyYXdpbmcgZnVuY3Rpb25zIGluc2lkZSB1c2VFZmZlY3QgdG8gZW5zdXJlIHRoZXkgYXJlIG5vdCBwYXJ0IG9mIHRoZSBjb21wb25lbnQnc1xyXG4gICAgLy8gdG9wLWxldmVsIHNjb3BlLCB3aGljaCBjYW4gY2F1c2UgaXNzdWVzIHdpdGggU1NSIGluIE5leHQuanMuXHJcbiAgICBjb25zdCBnZXRTaGFwZUNlbnRlciA9IChzaGFwZTogU2hhcGUpOiB7IGN4OiBudW1iZXIsIGN5OiBudW1iZXIgfSA9PiB7XHJcbiAgICAgIGNvbnN0IGNlbGxXaWR0aCA9IHdpZHRoIC8gZ3JpZC5jb2xzO1xyXG4gICAgICBjb25zdCBjZWxsSGVpZ2h0ID0gaGVpZ2h0IC8gZ3JpZC5yb3dzO1xyXG4gICAgICBjb25zdCByYWRpdXMgPSBzaGFwZS5kaWFtZXRlciAvIDI7XHJcbiAgICAgIGNvbnN0IGNlbGxUb3BMZWZ0WCA9IHNoYXBlLmNlbGwuY29sICogY2VsbFdpZHRoO1xyXG4gICAgICBjb25zdCBjZWxsVG9wTGVmdFkgPSBzaGFwZS5jZWxsLnJvdyAqIGNlbGxIZWlnaHQ7XHJcbiAgICAgIGxldCBjeCwgY3k7XHJcbiAgICAgIHN3aXRjaCAoc2hhcGUuYWxpZ25tZW50KSB7XHJcbiAgICAgICAgICBjYXNlICdjZW50ZXInOlxyXG4gICAgICAgICAgICAgIGN4ID0gY2VsbFRvcExlZnRYICsgY2VsbFdpZHRoIC8gMjtcclxuICAgICAgICAgICAgICBjeSA9IGNlbGxUb3BMZWZ0WSArIGNlbGxIZWlnaHQgLyAyO1xyXG4gICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgY2FzZSAndG9wTGVmdCc6XHJcbiAgICAgICAgICAgICAgY3ggPSBjZWxsVG9wTGVmdFggKyByYWRpdXM7XHJcbiAgICAgICAgICAgICAgY3kgPSBjZWxsVG9wTGVmdFkgKyByYWRpdXM7XHJcbiAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICBjYXNlICdjb29yZGluYXRlcyc6XHJcbiAgICAgICAgICAgICAgaWYgKHNoYXBlLmNvb3JkaW5hdGVzKSB7XHJcbiAgICAgICAgICAgICAgICAgIGN4ID0gY2VsbFRvcExlZnRYICsgc2hhcGUuY29vcmRpbmF0ZXMueDtcclxuICAgICAgICAgICAgICAgICAgY3kgPSBjZWxsVG9wTGVmdFkgKyBzaGFwZS5jb29yZGluYXRlcy55O1xyXG4gICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgIGN4ID0gY2VsbFRvcExlZnRYICsgY2VsbFdpZHRoIC8gMjtcclxuICAgICAgICAgICAgICAgICAgY3kgPSBjZWxsVG9wTGVmdFkgKyBjZWxsSGVpZ2h0IC8gMjtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIHsgY3gsIGN5IH07XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZHJhd010ZlBhdHRlcm4gPSAoaWN0eDogQ2FudmFzUmVuZGVyaW5nQ29udGV4dDJELCB4OiBudW1iZXIsIHk6IG51bWJlciwgc2l6ZTogbnVtYmVyLCBjb2xvcjogc3RyaW5nLCBpZHByOiBudW1iZXIsIGxpbmVXaWR0aDogbnVtYmVyID0gMikgPT4ge1xyXG4gICAgICBjb25zdCB0ZW1wQ2FudmFzID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnY2FudmFzJyk7XHJcbiAgICAgIHRlbXBDYW52YXMud2lkdGggPSBzaXplICogaWRwcjtcclxuICAgICAgdGVtcENhbnZhcy5oZWlnaHQgPSBzaXplICogaWRwcjtcclxuICAgICAgY29uc3QgdGVtcEN0eCA9IHRlbXBDYW52YXMuZ2V0Q29udGV4dCgnMmQnKTtcclxuICAgICAgaWYgKCF0ZW1wQ3R4KSByZXR1cm47XHJcbiAgICAgIHRlbXBDdHguc2NhbGUoaWRwciwgaWRwcik7XHJcbiAgICAgIHRlbXBDdHguZmlsbFN0eWxlID0gJyNGRkZGRkYnO1xyXG4gICAgICB0ZW1wQ3R4LmZpbGxSZWN0KDAsIDAsIHNpemUsIHNpemUpO1xyXG4gICAgICB0ZW1wQ3R4LmZpbGxTdHlsZSA9IGNvbG9yO1xyXG4gICAgICB0ZW1wQ3R4LmltYWdlU21vb3RoaW5nRW5hYmxlZCA9IGZhbHNlO1xyXG4gICAgICBjb25zdCBjZW50ZXJfeCA9IHNpemUgLyAyO1xyXG4gICAgICBjb25zdCBjZW50ZXJfeSA9IHNpemUgLyAyO1xyXG5cclxuICAgICAgLy8gRW5zdXJlIHRoZSBnYXAgd2lkdGggaXMgZGV0ZXJtaW5lZCBieSB0aGUgbGluZSB3aWR0aC5cclxuICAgICAgY29uc3QgZ2FwID0gbGluZVdpZHRoO1xyXG4gICAgICBjb25zdCBzdGVwID0gbGluZVdpZHRoICsgZ2FwOyAvLyBUaGlzIGlzIDIgKiBsaW5lV2lkdGhcclxuXHJcbiAgICAgIC8vIENlbnRyYWwgY3Jvc3NcclxuICAgICAgdGVtcEN0eC5maWxsUmVjdChNYXRoLnJvdW5kKGNlbnRlcl94IC0gbGluZVdpZHRoIC8gMiksIDAsIE1hdGgucm91bmQobGluZVdpZHRoKSwgTWF0aC5yb3VuZChzaXplKSk7XHJcbiAgICAgIHRlbXBDdHguZmlsbFJlY3QoMCwgTWF0aC5yb3VuZChjZW50ZXJfeSAtIGxpbmVXaWR0aCAvIDIpLCBNYXRoLnJvdW5kKHNpemUpLCBNYXRoLnJvdW5kKGxpbmVXaWR0aCkpO1xyXG5cclxuICAgICAgLy8gVG9wLWxlZnQgcXVhZHJhbnQgKHZlcnRpY2FsIGxpbmVzKVxyXG4gICAgICBmb3IgKGxldCBpID0gY2VudGVyX3ggLSBsaW5lV2lkdGggLyAyIC0gc3RlcDsgaSA+IC1saW5lV2lkdGg7IGkgLT0gc3RlcCkge1xyXG4gICAgICAgIHRlbXBDdHguZmlsbFJlY3QoTWF0aC5yb3VuZChpKSwgMCwgTWF0aC5yb3VuZChsaW5lV2lkdGgpLCBNYXRoLnJvdW5kKGNlbnRlcl95IC0gbGluZVdpZHRoIC8gMikpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBCb3R0b20tbGVmdCBxdWFkcmFudCAoaG9yaXpvbnRhbCBsaW5lcylcclxuICAgICAgZm9yIChsZXQgaSA9IGNlbnRlcl95ICsgbGluZVdpZHRoIC8gMiArIGdhcDsgaSA8IHNpemU7IGkgKz0gc3RlcCkge1xyXG4gICAgICAgIHRlbXBDdHguZmlsbFJlY3QoMCwgTWF0aC5yb3VuZChpKSwgTWF0aC5yb3VuZChjZW50ZXJfeCAtIGxpbmVXaWR0aCAvIDIpLCBNYXRoLnJvdW5kKGxpbmVXaWR0aCkpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBUb3AtcmlnaHQgcXVhZHJhbnQgKGhvcml6b250YWwgbGluZXMpXHJcbiAgICAgIGZvciAobGV0IGkgPSBjZW50ZXJfeSAtIGxpbmVXaWR0aCAvIDIgLSBzdGVwOyBpID4gLWxpbmVXaWR0aDsgaSAtPSBzdGVwKSB7XHJcbiAgICAgICAgdGVtcEN0eC5maWxsUmVjdChNYXRoLnJvdW5kKGNlbnRlcl94ICsgbGluZVdpZHRoIC8gMiksIE1hdGgucm91bmQoaSksIE1hdGgucm91bmQoc2l6ZSAtIChjZW50ZXJfeCArIGxpbmVXaWR0aCAvIDIpKSwgTWF0aC5yb3VuZChsaW5lV2lkdGgpKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQm90dG9tLXJpZ2h0IHF1YWRyYW50ICh2ZXJ0aWNhbCBsaW5lcylcclxuICAgICAgZm9yIChsZXQgaSA9IGNlbnRlcl94ICsgbGluZVdpZHRoIC8gMiArIGdhcDsgaSA8IHNpemU7IGkgKz0gc3RlcCkge1xyXG4gICAgICAgIHRlbXBDdHguZmlsbFJlY3QoTWF0aC5yb3VuZChpKSwgTWF0aC5yb3VuZChjZW50ZXJfeSArIGxpbmVXaWR0aCAvIDIpLCBNYXRoLnJvdW5kKGxpbmVXaWR0aCksIE1hdGgucm91bmQoc2l6ZSAtIChjZW50ZXJfeSArIGxpbmVXaWR0aCAvIDIpKSk7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIGljdHguaW1hZ2VTbW9vdGhpbmdFbmFibGVkID0gZmFsc2U7XHJcbiAgICAgIGljdHguZHJhd0ltYWdlKHRlbXBDYW52YXMsIHgsIHksIHNpemUsIHNpemUpO1xyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBkcmF3U2hhcGUgPSAoaWN0eDogQ2FudmFzUmVuZGVyaW5nQ29udGV4dDJELCBzaGFwZTogU2hhcGUsIGlkcHI6IG51bWJlcikgPT4ge1xyXG4gICAgICBjb25zdCB7IGN4LCBjeSB9ID0gZ2V0U2hhcGVDZW50ZXIoc2hhcGUpO1xyXG4gICAgICBjb25zdCByYWRpdXMgPSBzaGFwZS5kaWFtZXRlciAvIDI7XHJcbiAgICAgIGlmIChzaGFwZS50eXBlID09PSAnY2lyY2xlJykge1xyXG4gICAgICAgIGljdHguc2F2ZSgpO1xyXG5cclxuICAgICAgICAvLyDnpoHnlKjmipfplK/pvb/ku6Xpgb/lhY3ovrnnvJjpopzoibLmt7flkIhcclxuICAgICAgICBpY3R4LmltYWdlU21vb3RoaW5nRW5hYmxlZCA9IGZhbHNlO1xyXG5cclxuICAgICAgICAvLyDkvb/nlKjlg4/ntKDnuqfnsr7noa7nu5jliLbmnaXpgb/lhY3ovrnnvJjpopzoibLpl67pophcclxuICAgICAgICBjb25zdCB0ZW1wQ2FudmFzID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnY2FudmFzJyk7XHJcbiAgICAgICAgY29uc3QgdGVtcFNpemUgPSBNYXRoLmNlaWwoc2hhcGUuZGlhbWV0ZXIgKiBpZHByKSArIDI7IC8vIOa3u+WKoOS4gOS6m+i+uei3nVxyXG4gICAgICAgIHRlbXBDYW52YXMud2lkdGggPSB0ZW1wU2l6ZTtcclxuICAgICAgICB0ZW1wQ2FudmFzLmhlaWdodCA9IHRlbXBTaXplO1xyXG4gICAgICAgIGNvbnN0IHRlbXBDdHggPSB0ZW1wQ2FudmFzLmdldENvbnRleHQoJzJkJyk7XHJcblxyXG4gICAgICAgIGlmICh0ZW1wQ3R4KSB7XHJcbiAgICAgICAgICAvLyDlnKjkuLTml7bnlLvluIPkuIrnu5jliLblnIblvaJcclxuICAgICAgICAgIHRlbXBDdHguaW1hZ2VTbW9vdGhpbmdFbmFibGVkID0gZmFsc2U7XHJcbiAgICAgICAgICB0ZW1wQ3R4LmZpbGxTdHlsZSA9IHNoYXBlLmNvbG9yO1xyXG5cclxuICAgICAgICAgIGNvbnN0IHRlbXBSYWRpdXMgPSAoc2hhcGUuZGlhbWV0ZXIgKiBpZHByKSAvIDI7XHJcbiAgICAgICAgICBjb25zdCB0ZW1wQ2VudGVyID0gdGVtcFNpemUgLyAyO1xyXG5cclxuICAgICAgICAgIC8vIOS9v+eUqOWDj+e0oOe6p+e7mOWItu+8jOmBv+WFjeaKl+mUr+m9v1xyXG4gICAgICAgICAgZm9yIChsZXQgeCA9IDA7IHggPCB0ZW1wU2l6ZTsgeCsrKSB7XHJcbiAgICAgICAgICAgIGZvciAobGV0IHkgPSAwOyB5IDwgdGVtcFNpemU7IHkrKykge1xyXG4gICAgICAgICAgICAgIGNvbnN0IGRpc3RhbmNlID0gTWF0aC5zcXJ0KCh4IC0gdGVtcENlbnRlcikgKiogMiArICh5IC0gdGVtcENlbnRlcikgKiogMik7XHJcbiAgICAgICAgICAgICAgaWYgKGRpc3RhbmNlIDw9IHRlbXBSYWRpdXMpIHtcclxuICAgICAgICAgICAgICAgIHRlbXBDdHguZmlsbFJlY3QoeCwgeSwgMSwgMSk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLy8g5bCG5Li05pe255S75biD57uY5Yi25Yiw5Li755S75biD5LiKXHJcbiAgICAgICAgICBpY3R4LmRyYXdJbWFnZShcclxuICAgICAgICAgICAgdGVtcENhbnZhcyxcclxuICAgICAgICAgICAgY3ggLSBzaGFwZS5kaWFtZXRlciAvIDIsXHJcbiAgICAgICAgICAgIGN5IC0gc2hhcGUuZGlhbWV0ZXIgLyAyLFxyXG4gICAgICAgICAgICBzaGFwZS5kaWFtZXRlcixcclxuICAgICAgICAgICAgc2hhcGUuZGlhbWV0ZXJcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIC8vIOWmguaenOS4tOaXtueUu+W4g+WIm+W7uuWksei0pe+8jOS9v+eUqOWOn+Wni+aWueazlVxyXG4gICAgICAgICAgaWN0eC5maWxsU3R5bGUgPSBzaGFwZS5jb2xvcjtcclxuICAgICAgICAgIGljdHguYmVnaW5QYXRoKCk7XHJcbiAgICAgICAgICBpY3R4LmFyYyhjeCwgY3ksIHJhZGl1cywgMCwgMiAqIE1hdGguUEkpO1xyXG4gICAgICAgICAgaWN0eC5maWxsKCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpY3R4LnJlc3RvcmUoKTtcclxuICAgICAgfSBlbHNlIGlmIChzaGFwZS50eXBlID09PSAnc3F1YXJlJykge1xyXG4gICAgICAgIGRyYXdNdGZQYXR0ZXJuKGljdHgsIGN4IC0gcmFkaXVzLCBjeSAtIHJhZGl1cywgc2hhcGUuZGlhbWV0ZXIsIHNoYXBlLmNvbG9yLCBpZHByLCBzaGFwZS5tdGZXaWR0aCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgLy8gRHJhdyBzaGFwZXNcclxuICAgIHNoYXBlcy5mb3JFYWNoKHNoYXBlID0+IGRyYXdTaGFwZShjdHgsIHNoYXBlLCBkcHIpKTtcclxuXHJcbiAgfSwgW3dpZHRoLCBoZWlnaHQsIGdyaWQsIHNoYXBlcywgYmFja2dyb3VuZENvbG9yXSk7XHJcblxyXG4gIGNvbnN0IGRyYXdHcmlkID0gKGN0eDogQ2FudmFzUmVuZGVyaW5nQ29udGV4dDJEKSA9PiB7XHJcbiAgICBjdHguc3Ryb2tlU3R5bGUgPSAncmdiYSgwLCAwLCAwLCAwLjUpJztcclxuICAgIGN0eC5zZXRMaW5lRGFzaChbNSwgNV0pO1xyXG4gICAgY3R4LmxpbmVXaWR0aCA9IDE7XHJcblxyXG4gICAgY29uc3QgY2VsbFdpZHRoID0gd2lkdGggLyBncmlkLmNvbHM7XHJcbiAgICBjb25zdCBjZWxsSGVpZ2h0ID0gaGVpZ2h0IC8gZ3JpZC5yb3dzO1xyXG5cclxuICAgIGZvciAobGV0IGkgPSAxOyBpIDwgZ3JpZC5jb2xzOyBpKyspIHtcclxuICAgICAgY3R4LmJlZ2luUGF0aCgpO1xyXG4gICAgICBjdHgubW92ZVRvKGkgKiBjZWxsV2lkdGgsIDApO1xyXG4gICAgICBjdHgubGluZVRvKGkgKiBjZWxsV2lkdGgsIGhlaWdodCk7XHJcbiAgICAgIGN0eC5zdHJva2UoKTtcclxuICAgIH1cclxuXHJcbiAgICBmb3IgKGxldCBpID0gMTsgaSA8IGdyaWQucm93czsgaSsrKSB7XHJcbiAgICAgIGN0eC5iZWdpblBhdGgoKTtcclxuICAgICAgY3R4Lm1vdmVUbygwLCBpICogY2VsbEhlaWdodCk7XHJcbiAgICAgIGN0eC5saW5lVG8od2lkdGgsIGkgKiBjZWxsSGVpZ2h0KTtcclxuICAgICAgY3R4LnN0cm9rZSgpO1xyXG4gICAgfVxyXG4gICAgY3R4LnNldExpbmVEYXNoKFtdKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBmaW5kU2hhcGVBdCA9ICh4OiBudW1iZXIsIHk6IG51bWJlcik6IFNoYXBlIHwgdW5kZWZpbmVkID0+IHtcclxuICAgIC8vIFRoaXMgZnVuY3Rpb24gaXMgY2FsbGVkIGZyb20gYW4gZXZlbnQgaGFuZGxlciwgc28gaXQncyBzYWZlIHRvIGJlIG91dHNpZGUgdXNlRWZmZWN0LlxyXG4gICAgLy8gSG93ZXZlciwgaXQgbmVlZHMgdG8gY2FsY3VsYXRlIHNoYXBlIGNlbnRlcnMsIHNvIHdlIGR1cGxpY2F0ZSB0aGF0IGxvZ2ljIG9yIHBhc3MgaXQuXHJcbiAgICAvLyBGb3Igc2ltcGxpY2l0eSwgd2UgY2FuIGp1c3QgY2FsbCBnZXRTaGFwZUNlbnRlciB3aGljaCBpcyBub3cgZGVmaW5lZCBpbiB0aGUgY29tcG9uZW50IHNjb3BlLlxyXG4gICAgLy8gQnV0IGdldFNoYXBlQ2VudGVyIGlzIE5PVCBpbiB0aGUgY29tcG9uZW50IHNjb3BlIGFueW1vcmUuXHJcbiAgICAvLyBTbyB3ZSBtdXN0IHJlZGVmaW5lIGl0IG9yIG1vdmUgZmluZFNoYXBlQXQgaW50byB1c2VFZmZlY3QgYXMgd2VsbCwgd2hpY2ggaXMgbm90IGlkZWFsLlxyXG4gICAgLy8gTGV0J3MgcmVkZWZpbmUgdGhlIGNhbGN1bGF0aW9uIGxvZ2ljIGhlcmUuXHJcbiAgICBjb25zdCBjZWxsV2lkdGggPSB3aWR0aCAvIGdyaWQuY29scztcclxuICAgIGNvbnN0IGNlbGxIZWlnaHQgPSBoZWlnaHQgLyBncmlkLnJvd3M7XHJcblxyXG4gICAgLy8gSXRlcmF0ZSBpbiByZXZlcnNlIHRvIGZpbmQgdGhlIHRvcC1tb3N0IHNoYXBlXHJcbiAgICBmb3IgKGxldCBpID0gc2hhcGVzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XHJcbiAgICAgICAgY29uc3Qgc2hhcGUgPSBzaGFwZXNbaV07XHJcbiAgICAgICAgY29uc3QgcmFkaXVzID0gc2hhcGUuZGlhbWV0ZXIgLyAyO1xyXG4gICAgICAgIGNvbnN0IGNlbGxUb3BMZWZ0WCA9IHNoYXBlLmNlbGwuY29sICogY2VsbFdpZHRoO1xyXG4gICAgICAgIGNvbnN0IGNlbGxUb3BMZWZ0WSA9IHNoYXBlLmNlbGwucm93ICogY2VsbEhlaWdodDtcclxuICAgICAgICBsZXQgY3gsIGN5O1xyXG4gICAgICAgIHN3aXRjaCAoc2hhcGUuYWxpZ25tZW50KSB7XHJcbiAgICAgICAgICAgIGNhc2UgJ2NlbnRlcic6XHJcbiAgICAgICAgICAgICAgICBjeCA9IGNlbGxUb3BMZWZ0WCArIGNlbGxXaWR0aCAvIDI7XHJcbiAgICAgICAgICAgICAgICBjeSA9IGNlbGxUb3BMZWZ0WSArIGNlbGxIZWlnaHQgLyAyO1xyXG4gICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgIGNhc2UgJ3RvcExlZnQnOlxyXG4gICAgICAgICAgICAgICAgY3ggPSBjZWxsVG9wTGVmdFggKyByYWRpdXM7XHJcbiAgICAgICAgICAgICAgICBjeSA9IGNlbGxUb3BMZWZ0WSArIHJhZGl1cztcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBjYXNlICdjb29yZGluYXRlcyc6XHJcbiAgICAgICAgICAgICAgICBpZiAoc2hhcGUuY29vcmRpbmF0ZXMpIHtcclxuICAgICAgICAgICAgICAgICAgICBjeCA9IGNlbGxUb3BMZWZ0WCArIHNoYXBlLmNvb3JkaW5hdGVzLng7XHJcbiAgICAgICAgICAgICAgICAgICAgY3kgPSBjZWxsVG9wTGVmdFkgKyBzaGFwZS5jb29yZGluYXRlcy55O1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBjeCA9IGNlbGxUb3BMZWZ0WCArIGNlbGxXaWR0aCAvIDI7XHJcbiAgICAgICAgICAgICAgICAgICAgY3kgPSBjZWxsVG9wTGVmdFkgKyBjZWxsSGVpZ2h0IC8gMjtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKHNoYXBlLnR5cGUgPT09ICdjaXJjbGUnKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRpc3RhbmNlID0gTWF0aC5zcXJ0KCh4IC0gY3gpICoqIDIgKyAoeSAtIGN5KSAqKiAyKTtcclxuICAgICAgICAgICAgaWYgKGRpc3RhbmNlIDw9IHJhZGl1cykge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHNoYXBlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIGlmIChzaGFwZS50eXBlID09PSAnc3F1YXJlJykge1xyXG4gICAgICAgICAgICBpZiAoeCA+PSBjeCAtIHJhZGl1cyAmJiB4IDw9IGN4ICsgcmFkaXVzICYmIHkgPj0gY3kgLSByYWRpdXMgJiYgeSA8PSBjeSArIHJhZGl1cykge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHNoYXBlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDYW52YXNJbnRlcmFjdGlvbiA9IChldmVudDogUmVhY3QuTW91c2VFdmVudDxIVE1MQ2FudmFzRWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IGNhbnZhcyA9IGNhbnZhc1JlZi5jdXJyZW50O1xyXG4gICAgaWYgKCFjYW52YXMpIHJldHVybjtcclxuICAgIGNvbnN0IHJlY3QgPSBjYW52YXMuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XHJcbiAgICBjb25zdCB4ID0gZXZlbnQuY2xpZW50WCAtIHJlY3QubGVmdDtcclxuICAgIGNvbnN0IHkgPSBldmVudC5jbGllbnRZIC0gcmVjdC50b3A7XHJcblxyXG4gICAgaWYgKGV2ZW50LmJ1dHRvbiA9PT0gMikgeyAvLyBSaWdodC1jbGlja1xyXG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICAgICAgY29uc3Qgc2hhcGVUb0RlbGV0ZSA9IGZpbmRTaGFwZUF0KHgsIHkpO1xyXG4gICAgICAgIGlmIChzaGFwZVRvRGVsZXRlKSB7XHJcbiAgICAgICAgICAgIGRlbGV0ZVNoYXBlKHNoYXBlVG9EZWxldGUuaWQpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTGVmdC1jbGljayB0byBhZGQgc2hhcGVcclxuICAgIGNvbnN0IGNlbGxXaWR0aCA9IHdpZHRoIC8gZ3JpZC5jb2xzO1xyXG4gICAgY29uc3QgY2VsbEhlaWdodCA9IGhlaWdodCAvIGdyaWQucm93cztcclxuICAgIGNvbnN0IGNvbCA9IE1hdGguZmxvb3IoeCAvIGNlbGxXaWR0aCk7XHJcbiAgICBjb25zdCByb3cgPSBNYXRoLmZsb29yKHkgLyBjZWxsSGVpZ2h0KTtcclxuICAgIGFkZFNoYXBlKHsgcm93LCBjb2wgfSwgJ2NlbnRlcicpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8Y2FudmFzXHJcbiAgICAgIHJlZj17Y2FudmFzUmVmfVxyXG4gICAgICB3aWR0aD17d2lkdGh9XHJcbiAgICAgIGhlaWdodD17aGVpZ2h0fVxyXG4gICAgICBvbk1vdXNlRG93bj17aGFuZGxlQ2FudmFzSW50ZXJhY3Rpb259XHJcbiAgICAgIG9uQ29udGV4dE1lbnU9eyhlKSA9PiBlLnByZXZlbnREZWZhdWx0KCl9IC8vIFByZXZlbnQgY29udGV4dCBtZW51IG9uIHJpZ2h0LWNsaWNrXHJcbiAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS00MDBcIlxyXG4gICAgLz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ2FudmFzOyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVJlZiIsIkNhbnZhcyIsIndpZHRoIiwiaGVpZ2h0IiwiZ3JpZCIsInNoYXBlcyIsImFkZFNoYXBlIiwiZGVsZXRlU2hhcGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJjYW52YXNSZWYiLCJjYW52YXMiLCJjdXJyZW50IiwiY3R4IiwiZ2V0Q29udGV4dCIsImRwciIsIndpbmRvdyIsImRldmljZVBpeGVsUmF0aW8iLCJzdHlsZSIsInNjYWxlIiwiZmlsbFN0eWxlIiwiZmlsbFJlY3QiLCJkcmF3R3JpZCIsImdldFNoYXBlQ2VudGVyIiwic2hhcGUiLCJjZWxsV2lkdGgiLCJjb2xzIiwiY2VsbEhlaWdodCIsInJvd3MiLCJyYWRpdXMiLCJkaWFtZXRlciIsImNlbGxUb3BMZWZ0WCIsImNlbGwiLCJjb2wiLCJjZWxsVG9wTGVmdFkiLCJyb3ciLCJjeCIsImN5IiwiYWxpZ25tZW50IiwiY29vcmRpbmF0ZXMiLCJ4IiwieSIsImRyYXdNdGZQYXR0ZXJuIiwiaWN0eCIsInNpemUiLCJjb2xvciIsImlkcHIiLCJsaW5lV2lkdGgiLCJ0ZW1wQ2FudmFzIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwidGVtcEN0eCIsImltYWdlU21vb3RoaW5nRW5hYmxlZCIsImNlbnRlcl94IiwiY2VudGVyX3kiLCJnYXAiLCJzdGVwIiwiTWF0aCIsInJvdW5kIiwiaSIsImRyYXdJbWFnZSIsImRyYXdTaGFwZSIsInR5cGUiLCJzYXZlIiwidGVtcFNpemUiLCJjZWlsIiwidGVtcFJhZGl1cyIsInRlbXBDZW50ZXIiLCJkaXN0YW5jZSIsInNxcnQiLCJiZWdpblBhdGgiLCJhcmMiLCJQSSIsImZpbGwiLCJyZXN0b3JlIiwibXRmV2lkdGgiLCJmb3JFYWNoIiwic3Ryb2tlU3R5bGUiLCJzZXRMaW5lRGFzaCIsIm1vdmVUbyIsImxpbmVUbyIsInN0cm9rZSIsImZpbmRTaGFwZUF0IiwibGVuZ3RoIiwidW5kZWZpbmVkIiwiaGFuZGxlQ2FudmFzSW50ZXJhY3Rpb24iLCJldmVudCIsInJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJjbGllbnRYIiwibGVmdCIsImNsaWVudFkiLCJ0b3AiLCJidXR0b24iLCJwcmV2ZW50RGVmYXVsdCIsInNoYXBlVG9EZWxldGUiLCJpZCIsImZsb29yIiwicmVmIiwib25Nb3VzZURvd24iLCJvbkNvbnRleHRNZW51IiwiZSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/drawing-board/Canvas.tsx\n"));

/***/ })

});