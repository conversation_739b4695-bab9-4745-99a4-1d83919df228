"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // A single regex to identify any line that marks the beginning of a new data block.\n    const ANY_BLOCK_START_MARKER = /(?:(?:开始|打开)抽真空|轴停止运动)/;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const markers = [];\n        const lines = logContent.split('\\n');\n        let currentIndex = 0;\n        // 1. Find the starting index of every line that contains a block start marker.\n        for (const line of lines){\n            if (ANY_BLOCK_START_MARKER.test(line)) {\n                markers.push({\n                    index: currentIndex\n                });\n            }\n            // Add 1 for the newline character\n            currentIndex += line.length + 1;\n        }\n        if (markers.length === 0) {\n            if (DEBUG) console.log(\"[Worker] No block start markers found in log content.\");\n            return [];\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // 2. Create blocks from the content between markers.\n        for(let i = 0; i < markers.length; i++){\n            const startIdx = markers[i].index;\n            const endIdx = i + 1 < markers.length ? markers[i + 1].index : logContent.length;\n            const blockContent = logContent.substring(startIdx, endIdx).trim();\n            if (!blockContent) continue;\n            let sn = null;\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            }\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(i + 1) : \"block_\".concat(i + 1);\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Any block created by a marker is considered valid and must be kept for searching.\n                // The frontend UI can decide later whether to display a block based on its content,\n                // but the parser must not discard it.\n                localProcessedBlocks.push(processedBlock);\n            }\n        }\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the full log content for searching, and parse it into blocks.\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"抽真空\";\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        // With the blocks now correctly defined, this search logic will work.\n                        for (const block of processedBlocks){\n                            if (matchedBlockIds.has(block.block_id)) continue;\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                // Check if the line contains the keyword first for efficiency\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            // Check if the timestamp of the line is within the desired search window\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {}\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("9ab776909ddb0496")
/******/ })();
/******/ 
/******/ }
);