/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "(ssr)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // V1 blocks start from \"打开真空泵\" and end with \"insert into g_support\"\n    const BLOCK_REGEX_V1 = /打开真空泵[\\s\\S]*?insert into g_support[^\\n]*/g;\n    // V2 blocks have their own distinct start and end markers.\n    const BLOCK_REGEX_V2 = /轴停止运动[\\s\\S]*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent, blockVersion) {\n        if (DEBUG) console.log(`[Worker] Processing block ${blockId} with unified parser. Content snippet: ${blockContent.substring(0, 100)}...`);\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        // Get timestamps for specific start/end sentences for debugging, as requested by the user.\n        const startSentenceTs = _extractTimestampFromLine(blockLines[0]);\n        let endSentenceTs = null;\n        // Find the last line containing a known end-of-block marker to get its timestamp.\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const line = blockLines[i];\n            if (line.includes('insert into g_support') || line.includes('SetSpeed:2, 85, result:')) {\n                endSentenceTs = _extractTimestampFromLine(line);\n                break; // Found the last marker line\n            }\n        }\n        if (DEBUG) {\n            // Enhanced logging as requested by the user.\n            console.log(`[Worker] Block ${blockId}: ` + `StartLineTS=${startSentenceTs}, EndLineTS=${endSentenceTs}, ` + `OverallStartTS=${startTimeStr}, OverallEndTS=${endTimeStr}`);\n        }\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            if (blockVersion === \"V1\") {\n                // V1格式需要在\"z轴停止完成\"之后才开始收集数据\n                // 首先检查是否已经遇到了\"z轴停止完成\"标志\n                if (line.includes('z轴停止完成')) {\n                    // 标记从这里开始收集数据\n                    // 我们需要一个标志来跟踪是否已经遇到了这个关键点\n                    if (!blockContent.includes('__Z_AXIS_STOP_FOUND__')) {\n                    // 在内容中添加一个标记，表示已经找到了z轴停止完成\n                    // 这是一个临时解决方案，更好的方法是重构整个解析逻辑\n                    }\n                }\n                // 只有在找到\"z轴停止完成\"之后才收集数据\n                const zAxisStopIndex = blockContent.indexOf('z轴停止完成');\n                const currentLineIndex = blockContent.indexOf(line);\n                if (zAxisStopIndex !== -1 && currentLineIndex > zAxisStopIndex) {\n                    // Check for V1 Glue\n                    const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n                    if (glueMatchV1) {\n                        try {\n                            const valueFloat = parseFloat(glueMatchV1[1]);\n                            if (timestampForValue) {\n                                glueThicknessValuesList.push({\n                                    timestamp: timestampForValue,\n                                    value: valueFloat\n                                });\n                            }\n                        } catch (e) {}\n                    }\n                    // Check for V1 Diff\n                    const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n                    if (diffMatchV1) {\n                        try {\n                            const valueFloat = parseFloat(diffMatchV1[1]);\n                            if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                                collimationDiffValuesList.push({\n                                    timestamp: timestampForValue,\n                                    value: valueFloat\n                                });\n                            }\n                        } catch (e) {}\n                    }\n                }\n            }\n            if (blockVersion === \"V2\") {\n                // Check for V2 Glue\n                const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n                if (glueMatchV2) {\n                    const valueFloat = parseFloat(glueMatchV2[1]);\n                    if (timestampForValue && !isNaN(valueFloat)) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                    }\n                }\n                // Check for V2 Diff\n                const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n                if (diffMatchV2) {\n                    if (timestampForValue) {\n                        const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                        const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                        if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                            collimationDiffValuesList.push({\n                                timestamp: timestampForValue,\n                                value: diffValue\n                            });\n                        }\n                    }\n                }\n            }\n            // Generic events can be checked for all block types\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(`[Worker] Block ${blockId} processing finished. Glue values: ${glueThicknessValuesList.length}, Diff values: ${collimationDiffValuesList.length}`);\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        // 1. Find all possible V1 and V2 blocks independently.\n        // Add version information to each match before combining.\n        const v1Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V1)).map((m)=>({\n                ...m,\n                version: \"V1\"\n            }));\n        const v2Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V2)).map((m)=>({\n                ...m,\n                version: \"V2\"\n            }));\n        // 2. Combine and sort all found blocks by their starting position in the log file.\n        const allMatches = [\n            ...v1Matches,\n            ...v2Matches\n        ].sort((a, b)=>(a.index || 0) - (b.index || 0));\n        if (allMatches.length === 0) {\n            if (DEBUG) console.log(\"[Worker] No V1 or V2 blocks found in log content.\");\n            return [];\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // 3. Process each block in the correct order.\n        allMatches.forEach((match, index)=>{\n            const blockContent = match[0];\n            if (!blockContent) return;\n            let sn = null;\n            // SN is always expected inside the block now, due to the new regex definitions.\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            }\n            // Process the block first with a temporary ID to get its properties, like start_time.\n            const tempBlockId = `temp_${index + 1}`;\n            const processedBlock = processRawBlock(tempBlockId, blockContent, match.version);\n            if (processedBlock) {\n                // Now, create the final block_id based on the user's request.\n                // Use the start_time if available, otherwise fall back to the old naming scheme.\n                // Use the start_time to create the block ID in YYYYMMDD_HH_mm_ss format, as requested.\n                let formattedId = null;\n                if (processedBlock.start_time) {\n                    // This is a more robust way to format the date, avoiding potential string replacement issues.\n                    try {\n                        const date = new Date(processedBlock.start_time.replace(',', '.'));\n                        if (isNaN(date.getTime())) {\n                            throw new Error(\"Invalid date\");\n                        }\n                        const y = date.getFullYear().toString();\n                        const m = (date.getMonth() + 1).toString().padStart(2, '0');\n                        const d = date.getDate().toString().padStart(2, '0');\n                        const h = date.getHours().toString().padStart(2, '0');\n                        const min = date.getMinutes().toString().padStart(2, '0');\n                        const s = date.getSeconds().toString().padStart(2, '0');\n                        formattedId = `${y}${m}${d}_${h}_${min}_${s}`;\n                    } catch (e) {\n                        // If date parsing fails, formattedId remains null, and the fallback ID will be used.\n                        if (DEBUG) console.error(`Could not parse date for block ID: ${processedBlock.start_time}`);\n                    }\n                }\n                const finalBlockId = formattedId || (sn ? `${sn}_${index + 1}` : `block_${index + 1}`);\n                processedBlock.block_id = finalBlockId;\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Every matched block is valid and must be kept.\n                localProcessedBlocks.push(processedBlock);\n            }\n        });\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(`[Worker] Message received: ${event.data.type}`);\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(`[Worker] Received log content. Length: ${logContent.length}. Starting parsing...`);\n                    // Store the full log content for searching, and parse it into blocks.\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(`[Worker] Sending ${blocksForFrontend.length} processed blocks to main thread.`);\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: `Successfully processed ${blocksForFrontend.length} blocks.`\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(`[Worker] Starting timestamp range match for ${timestamps.length} timestamps.`);\n                    const matchedBlockIds = new Set();\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        for (const block of processedBlocks){\n                            // Check if the block has valid start and end times.\n                            if (block.start_time && block.end_time) {\n                                try {\n                                    const blockStartTime = new Date(block.start_time.replace(',', '.')).getTime();\n                                    const blockEndTime = new Date(block.end_time.replace(',', '.')).getTime();\n                                    // The correct logic, as per user instruction:\n                                    // Check if the image's timestamp falls within the block's time range.\n                                    if (targetTime >= blockStartTime && targetTime <= blockEndTime) {\n                                        if (DEBUG) console.log(`[Worker] Timestamp ${targetTime} falls within block ${block.block_id} range [${blockStartTime} - ${blockEndTime}]. Match found.`);\n                                        matchedBlockIds.add(block.block_id);\n                                    }\n                                } catch (e) {\n                                    if (DEBUG) console.error(`[Worker] Could not parse timestamp for block ${block.block_id}.`, e);\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(`[Worker] Timestamp match finished. Found ${resultIds.length} matching blocks.`);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(`[Worker] Unknown message type received: ${type}`);\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./workers/logParser.worker.ts\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The require scope
/******/ 	var __webpack_require__ = {};
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__["(ssr)/./workers/logParser.worker.ts"](0, __webpack_exports__, __webpack_require__);
/******/ 	module.exports = __webpack_exports__;
/******/ 	
/******/ })()
;