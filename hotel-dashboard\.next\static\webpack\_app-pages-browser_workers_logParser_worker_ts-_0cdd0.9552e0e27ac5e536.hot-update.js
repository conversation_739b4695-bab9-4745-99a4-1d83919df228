"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts-_0cdd0",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {\n    console.warn('[Worker] logParser.worker.ts loaded in non-worker context (or main thread). Exports are available, but worker-specific code will not run.');\n} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    const BLOCK_REGEX_V1 = /打开真空泵(?:(?!打开真空泵)[\\s\\S])*?insert into g_support/g;\n    // --- V2 正则表达式 ---\n    const BLOCK_REGEX_V2 = /轴停止运动(?:(?!轴停止运动)[\\s\\S])*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function detectLogVersion(logContent) {\n        if (logContent.includes(\"轴停止运动\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V2');\n            return \"V2\";\n        }\n        if (logContent.includes(\"打开真空泵\")) {\n            if (DEBUG) console.log('[Worker] Detected Log Version: V1');\n            return \"V1\";\n        }\n        if (DEBUG) console.warn('[Worker] Could not determine log version.');\n        return \"UNKNOWN\";\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const version = detectLogVersion(logContent);\n        if (version === \"UNKNOWN\") {\n            throw new Error('Unknown log file version. Could not parse.');\n        }\n        const blockRegex = version === \"V1\" ? BLOCK_REGEX_V1 : BLOCK_REGEX_V2;\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        let match;\n        let lastIndex = 0;\n        let iterationCount = 0;\n        const MAX_ITERATIONS = 100000; // Safety break to prevent infinite loops\n        // Manually iterate with exec() to have more control and prevent crashes\n        while((match = blockRegex.exec(logContent)) !== null){\n            iterationCount++;\n            if (iterationCount > MAX_ITERATIONS) {\n                console.error('[Worker] Infinite loop detected in block matching. Aborting.');\n                self.postMessage({\n                    type: 'PARSE_LOG_RESULT',\n                    success: false,\n                    error: 'Infinite loop detected during log parsing. Check log format and block delimiters.'\n                });\n                return []; // Stop processing\n            }\n            // This check prevents infinite loops on zero-length matches\n            if (match.index === blockRegex.lastIndex) {\n                blockRegex.lastIndex++;\n            }\n            const blockContent = match[0];\n            if (!blockContent || typeof match.index === 'undefined') continue;\n            let sn = null;\n            // 1. Try to find SN inside the block first\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n                if (DEBUG) console.log(\"[Worker] Found SN inside block \".concat(iterationCount, \": \").concat(sn));\n            } else {\n                // 2. If not found, search for the next SN *after* this block\n                const endOfCurrentBlock = match.index + blockContent.length;\n                const searchArea = logContent.substring(endOfCurrentBlock); // Search from end of block to end of file\n                const snMatchOutside = snRegex.exec(searchArea);\n                if (snMatchOutside) {\n                    // To avoid altering the main loop's regex state, create a new regex for peeking.\n                    const peekRegex = new RegExp(blockRegex.source, blockRegex.flags);\n                    peekRegex.lastIndex = endOfCurrentBlock; // Start peeking from the end of the current block\n                    const nextBlockMatch = peekRegex.exec(logContent);\n                    // Check if the found SN is located *before* the start of the next block.\n                    if (!nextBlockMatch || endOfCurrentBlock + snMatchOutside.index < nextBlockMatch.index) {\n                        sn = snMatchOutside[1];\n                        if (DEBUG) console.log(\"[Worker] Found SN outside block \".concat(iterationCount, \": \").concat(sn));\n                    } else {\n                        // The SN found belongs to a later block, so ignore it for this one.\n                        if (DEBUG) console.log(\"[Worker] SN found outside block \".concat(iterationCount, \", but it belongs to a subsequent block. Ignoring.\"));\n                    }\n                }\n            }\n            // Ensure lastIndex is correctly updated after every iteration to prevent infinite loops.\n            blockRegex.lastIndex = match.index + blockContent.length;\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(iterationCount) : \"block_\".concat(iterationCount);\n            if (DEBUG) console.log(\"[Worker] Final Block ID for index \".concat(iterationCount - 1, \": \").concat(blockId));\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0) {\n                    localProcessedBlocks.push(processedBlock);\n                }\n            }\n            lastIndex = blockRegex.lastIndex;\n        }\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the parsed blocks globally in the worker\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    console.log('[Worker] Received MATCH_BY_TIMESTAMP with payload:', payload);\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"打开真空泵\";\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        for (const block of processedBlocks){\n                            // Avoid re-checking a block that already has a match\n                            if (matchedBlockIds.has(block.block_id)) {\n                                continue;\n                            }\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            // Timestamps are in 'YYYY-MM-DD HH:mm:ss,SSS' format\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {\n                                        // Ignore lines with invalid date formats\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    console.log('[Worker] Sending back matched IDs:', resultIds);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {\n        console.error('[Worker] Uncaught error in worker script:', errorEvent);\n    };\n    console.log('[Worker] logParser.worker.ts script loaded and event listener for \"message\" set up.');\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("9f74113352e9222b")
/******/ })();
/******/ 
/******/ }
);