// hotel-dashboard/workers/logParser.definitions.ts

export interface TimestampedValue {
  timestamp: string;
  value: number | null; // Allow null for values
}

export interface ValveOpenEvent {
  timestamp: string;
  line_content: string;
}

export interface ProcessedBlock {
  block_id: string;
  start_time: string | null;
  end_time: string | null;
  lines_count: number;
  valve_open_events: ValveOpenEvent[];
  glue_thickness_values: TimestampedValue[];
  collimation_diff_values: TimestampedValue[];
  sns: string[];
  raw_content: string; // Add raw content for further processing
}

export interface FormattedDataPoint {
  timestamp: string;
  glue_thickness: number | null;
  collimation_diff: number | null;
}

export interface FormattedBlockData {
  block_id: string;
  data_points: FormattedDataPoint[];
  start_time: string | null;
  end_time: string | null;
  valve_open_events: ValveOpenEvent[];
}

export function formatBlockDataForFrontend(blockData: ProcessedBlock): FormattedBlockData | null {
    if (!blockData) return null;

    const glueValues = blockData.glue_thickness_values || [];
    const collimValues = blockData.collimation_diff_values || [];

    const glueMap = new Map(glueValues.map(item => [item.timestamp, item.value]));
    const collimMap = new Map(collimValues.map(item => [item.timestamp, item.value]));

    const timestampSet: { [key: string]: boolean } = {};
    for (const item of glueValues) {
        if (item.timestamp) timestampSet[item.timestamp] = true;
    }
    for (const item of collimValues) {
        if (item.timestamp) timestampSet[item.timestamp] = true;
    }
    const allTimestamps = Object.keys(timestampSet);
    allTimestamps.sort();

    const dataPoints: FormattedDataPoint[] = allTimestamps.map(ts => ({
        timestamp: ts,
        glue_thickness: glueMap.get(ts) ?? null,
        collimation_diff: collimMap.get(ts) ?? null,
    })).filter(dp => dp.timestamp);
    
    return {
        block_id: blockData.block_id || "N/A",
        data_points: dataPoints,
        start_time: blockData.start_time,
        end_time: blockData.end_time,
        valve_open_events: blockData.valve_open_events || [],
    };
}