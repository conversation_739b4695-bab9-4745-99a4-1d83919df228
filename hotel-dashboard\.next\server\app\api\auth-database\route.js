(()=>{var e={};e.id=194,e.ids=[194],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{"use strict";e.exports=require("os")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51479:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>P,routeModule:()=>j,serverHooks:()=>b,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>v});var r={};t.r(r),t.d(r,{GET:()=>g,POST:()=>f});var a=t(96559),o=t(48088),n=t(37719),i=t(32190),u=t(29021),c=t(33873),p=t.n(c);t(55511);var d=t(21820),l=t.n(d);let w=[p().join(process.cwd(),"database-password.txt"),p().join("C:","ProgramData","HotelDashboard","database-password.txt"),p().join(l().homedir(),"AppData","Local","HotelDashboard","database-password.txt"),p().join(process.cwd(),"..","database-password.txt")],x="admin123";async function m(){for(let e of w)try{let s=await u.promises.readFile(e,"utf-8");return console.log(`找到密码文件: ${e}`),{password:s.trim(),filePath:e}}catch(e){continue}let e=w[0];console.log(`密码文件不存在，在以下位置创建默认密码文件: ${e}`);try{return await u.promises.mkdir(p().dirname(e),{recursive:!0}),await u.promises.writeFile(e,x,"utf-8"),{password:x,filePath:e}}catch(e){throw console.error("创建密码文件失败:",e),Error("无法创建密码文件")}}async function h(e,s){try{await u.promises.mkdir(p().dirname(s),{recursive:!0}),await u.promises.writeFile(s,e,"utf-8")}catch(e){throw console.error("保存密码文件失败:",e),Error("无法保存密码文件")}}async function f(e){try{let{password:s,action:t}=await e.json();if("verify"===t){let{password:e}=await m();if(s===e)return i.NextResponse.json({success:!0,message:"密码验证成功"});return i.NextResponse.json({success:!1,message:"密码错误"},{status:401})}if("change"!==t)return i.NextResponse.json({success:!1,message:"无效的操作"},{status:400});{let{oldPassword:s,newPassword:t}=await e.json(),{password:r,filePath:a}=await m();if(s!==r)return i.NextResponse.json({success:!1,message:"原密码错误"},{status:401});return await h(t,a),i.NextResponse.json({success:!0,message:"密码修改成功"})}}catch(e){return console.error("密码验证API错误:",e),i.NextResponse.json({success:!1,message:"服务器错误: "+e.message},{status:500})}}async function g(){try{let{password:e,filePath:s}=await m();return i.NextResponse.json({success:!0,message:"密码文件已就绪",hasPassword:!0,filePath:s,defaultPassword:e===x?x:null})}catch(e){return console.error("获取密码状态错误:",e),i.NextResponse.json({success:!1,message:"服务器错误: "+e.message},{status:500})}}let j=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth-database/route",pathname:"/api/auth-database",filename:"route",bundlePath:"app/api/auth-database/route"},resolvedPagePath:"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\api\\auth-database\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:y,workUnitAsyncStorage:v,serverHooks:b}=j;function P(){return(0,n.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:v})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[719,580],()=>t(51479));module.exports=r})();