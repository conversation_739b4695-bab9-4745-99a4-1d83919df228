"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/log-analysis/LogChartView.tsx":
/*!**************************************************!*\
  !*** ./components/log-analysis/LogChartView.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogChartView: () => (/* binding */ LogChartView),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/ComposedChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/ReferenceLine.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _lib_exportUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/exportUtils */ \"(app-pages-browser)/./lib/exportUtils.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ LogChartView,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n// Helper functions (parseTimestamp, processBlockData) defined at the top-level\n// so they are accessible by both LogChartView and ChartRenderer.\nconst parseTimestamp = (timestamp)=>{\n    if (!timestamp) return NaN;\n    try {\n        let standardizedTimestamp = timestamp.replace(\",\", \".\");\n        if (!standardizedTimestamp.includes(\"T\") && standardizedTimestamp.includes(\" \")) {\n            const parts = standardizedTimestamp.split(\" \");\n            if (parts.length > 1 && parts[0].includes(\"-\") && parts[1].includes(\":\")) {\n                standardizedTimestamp = parts[0] + \"T\" + parts.slice(1).join(\" \");\n            }\n        }\n        let date = new Date(standardizedTimestamp);\n        let time = date.getTime();\n        if (isNaN(time)) {\n            const slightlyLessStandardized = timestamp.replace(\",\", \".\");\n            date = new Date(slightlyLessStandardized);\n            time = date.getTime();\n        }\n        if (isNaN(time)) {\n            date = new Date(timestamp);\n            time = date.getTime();\n        }\n        if (isNaN(time)) {\n            console.warn('[LogChartViewHelper] Failed to parse timestamp: \"'.concat(timestamp, '\"'));\n            return NaN;\n        }\n        return time;\n    } catch (error) {\n        console.error('[LogChartViewHelper] Error parsing timestamp: \"'.concat(timestamp, '\"'), error);\n        return NaN;\n    }\n};\nconst processBlockData = (block)=>{\n    const dataPoints = [];\n    (block.glue_thickness_values || []).forEach((value)=>{\n        const time = parseTimestamp(value.timestamp);\n        if (!isNaN(time)) dataPoints.push({\n            time,\n            glueThickness: value.value,\n            collimationDiff: null\n        });\n    });\n    (block.collimation_diff_values || []).forEach((value)=>{\n        const time = parseTimestamp(value.timestamp);\n        if (!isNaN(time)) dataPoints.push({\n            time,\n            glueThickness: null,\n            collimationDiff: value.value\n        });\n    });\n    dataPoints.sort((a, b)=>a.time - b.time);\n    const mergedPoints = [];\n    dataPoints.forEach((point)=>{\n        const existingPoint = mergedPoints.find((p)=>p.time === point.time);\n        if (existingPoint) {\n            if (point.glueThickness !== null) existingPoint.glueThickness = point.glueThickness;\n            if (point.collimationDiff !== null) existingPoint.collimationDiff = point.collimationDiff;\n        } else {\n            mergedPoints.push({\n                ...point\n            });\n        }\n    });\n    return mergedPoints;\n};\nconst ChartRenderer = (param)=>{\n    let { chunk, isChartReady } = param;\n    _s();\n    const { chartDataForThisBlock, eventPointsForThisBlock, timeDomainForThisBlock, glueDomainForThisBlock, collimationDomainForThisBlock, hasDataForThisBlock } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChartRenderer.useMemo\": ()=>{\n            if (!chunk) {\n                return {\n                    chartDataForThisBlock: [],\n                    eventPointsForThisBlock: [],\n                    timeDomainForThisBlock: [\n                        Date.now() - 3600000,\n                        Date.now()\n                    ],\n                    glueDomainForThisBlock: [\n                        0,\n                        1000\n                    ],\n                    collimationDomainForThisBlock: [\n                        0,\n                        0.1\n                    ],\n                    hasDataForThisBlock: false\n                };\n            }\n            const processedDataPoints = processBlockData(chunk);\n            const currentEventPoints = (chunk.valve_open_events || []).map({\n                \"ChartRenderer.useMemo.currentEventPoints\": (event)=>{\n                    const eventTime = parseTimestamp(event.timestamp);\n                    return {\n                        time: eventTime,\n                        value: 0,\n                        label: \"打开放气阀 (\".concat(event.timestamp.split(' ')[1] || event.timestamp, \")\")\n                    };\n                }\n            }[\"ChartRenderer.useMemo.currentEventPoints\"]).filter({\n                \"ChartRenderer.useMemo.currentEventPoints\": (ep)=>!isNaN(ep.time)\n            }[\"ChartRenderer.useMemo.currentEventPoints\"]);\n            currentEventPoints.sort({\n                \"ChartRenderer.useMemo\": (a, b)=>a.time - b.time\n            }[\"ChartRenderer.useMemo\"]);\n            let timeDom = [\n                Date.now() - 3600000,\n                Date.now()\n            ];\n            if (processedDataPoints.length > 0) {\n                const times = processedDataPoints.map({\n                    \"ChartRenderer.useMemo.times\": (p)=>p.time\n                }[\"ChartRenderer.useMemo.times\"]).filter({\n                    \"ChartRenderer.useMemo.times\": (t)=>!isNaN(t)\n                }[\"ChartRenderer.useMemo.times\"]);\n                if (times.length > 0) {\n                    timeDom = [\n                        Math.min(...times),\n                        Math.max(...times)\n                    ];\n                }\n            }\n            if (timeDom[0] === timeDom[1]) timeDom[1] = timeDom[0] + 3600000;\n            const glueValues = processedDataPoints.map({\n                \"ChartRenderer.useMemo.glueValues\": (p)=>p.glueThickness\n            }[\"ChartRenderer.useMemo.glueValues\"]).filter({\n                \"ChartRenderer.useMemo.glueValues\": (v)=>v !== null && !isNaN(v)\n            }[\"ChartRenderer.useMemo.glueValues\"]);\n            let glueDom = [\n                0,\n                1000\n            ];\n            if (glueValues.length > 0) glueDom = [\n                Math.min(...glueValues),\n                Math.max(...glueValues)\n            ];\n            if (glueDom[0] === glueDom[1]) glueDom[1] = glueDom[0] + 10;\n            const collimValues = processedDataPoints.map({\n                \"ChartRenderer.useMemo.collimValues\": (p)=>p.collimationDiff\n            }[\"ChartRenderer.useMemo.collimValues\"]).filter({\n                \"ChartRenderer.useMemo.collimValues\": (v)=>v !== null && !isNaN(v)\n            }[\"ChartRenderer.useMemo.collimValues\"]);\n            let collimDom = [\n                0,\n                0.1\n            ];\n            if (collimValues.length > 0) collimDom = [\n                Math.min(...collimValues),\n                Math.max(...collimValues)\n            ];\n            if (collimDom[0] === collimDom[1]) collimDom[1] = collimDom[0] + 0.01;\n            return {\n                chartDataForThisBlock: processedDataPoints,\n                eventPointsForThisBlock: currentEventPoints,\n                timeDomainForThisBlock: timeDom,\n                glueDomainForThisBlock: glueDom,\n                collimationDomainForThisBlock: collimDom,\n                hasDataForThisBlock: processedDataPoints.length > 0\n            };\n        }\n    }[\"ChartRenderer.useMemo\"], [\n        chunk\n    ]);\n    const shouldRenderChartContent = hasDataForThisBlock && isChartReady;\n    if (!shouldRenderChartContent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full text-muted-foreground\",\n            children: !isChartReady ? \"图表加载中...\" : \"此数据块无有效图表数据。\"\n        }, void 0, false, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.ResponsiveContainer, {\n        width: \"100%\",\n        height: \"100%\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.ComposedChart, {\n            data: chartDataForThisBlock,\n            margin: {\n                top: 20,\n                right: 40,\n                left: 30,\n                bottom: 20\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.XAxis, {\n                    dataKey: \"time\",\n                    domain: timeDomainForThisBlock,\n                    type: \"number\",\n                    tickFormatter: (value)=>new Date(value).toLocaleTimeString(),\n                    allowDuplicatedCategory: false\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                    yAxisId: \"glue\",\n                    orientation: \"left\",\n                    domain: glueDomainForThisBlock,\n                    type: \"number\",\n                    stroke: \"#8884d8\",\n                    label: {\n                        value: '胶厚 (μm)',\n                        angle: -90,\n                        position: 'insideLeft',\n                        offset: -5,\n                        style: {\n                            fill: '#8884d8',\n                            textAnchor: 'middle'\n                        }\n                    },\n                    tickFormatter: (value)=>value.toFixed(2),\n                    width: 70\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                    yAxisId: \"collimation\",\n                    orientation: \"right\",\n                    domain: collimationDomainForThisBlock,\n                    type: \"number\",\n                    stroke: \"#82ca9d\",\n                    label: {\n                        value: '准直差',\n                        angle: 90,\n                        position: 'insideRight',\n                        offset: -15,\n                        style: {\n                            fill: '#82ca9d',\n                            textAnchor: 'middle'\n                        }\n                    },\n                    tickFormatter: (value)=>value.toFixed(3),\n                    width: 80\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                    labelFormatter: (value)=>new Date(value).toLocaleString(),\n                    formatter: (value, name)=>{\n                        if (typeof value !== 'number') return [\n                            value,\n                            name\n                        ];\n                        if (name === 'glueThickness') return [\n                            value.toFixed(2) + ' μm',\n                            '胶厚'\n                        ];\n                        if (name === 'collimationDiff') return [\n                            value.toFixed(3),\n                            '准直差'\n                        ];\n                        return [\n                            value,\n                            name\n                        ];\n                    },\n                    contentStyle: {\n                        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n                        border: '1px solid #ccc',\n                        borderRadius: '4px',\n                        padding: '8px'\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Legend, {\n                    verticalAlign: \"top\",\n                    height: 36,\n                    wrapperStyle: {\n                        paddingBottom: '10px'\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Line, {\n                    yAxisId: \"glue\",\n                    type: \"monotone\",\n                    dataKey: \"glueThickness\",\n                    name: \"胶厚\",\n                    stroke: \"#8884d8\",\n                    strokeWidth: 2,\n                    dot: {\n                        r: 2\n                    },\n                    activeDot: {\n                        r: 5\n                    },\n                    isAnimationActive: false,\n                    connectNulls: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Line, {\n                    yAxisId: \"collimation\",\n                    type: \"monotone\",\n                    dataKey: \"collimationDiff\",\n                    name: \"准直差\",\n                    stroke: \"#82ca9d\",\n                    strokeWidth: 2,\n                    dot: {\n                        r: 2\n                    },\n                    activeDot: {\n                        r: 5\n                    },\n                    isAnimationActive: false,\n                    connectNulls: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, undefined),\n                eventPointsForThisBlock.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.ReferenceLine, {\n                        x: event.time,\n                        stroke: \"rgba(255,0,0,0.7)\",\n                        yAxisId: \"glue\",\n                        strokeDasharray: \"4 4\",\n                        label: {\n                            value: event.label,\n                            position: 'insideTopRight',\n                            fill: 'rgba(255,0,0,0.7)',\n                            fontSize: 10\n                        }\n                    }, \"event-\".concat(chunk.block_id, \"-\").concat(index), false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChartRenderer, \"8mDZ0hNgDzpsS3GxcLMYCTN7JZg=\");\n_c = ChartRenderer;\nfunction LogChartView(param) {\n    let { dataChunks, selectedBlockIds, onBlockSelect } = param;\n    _s1();\n    const chartContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // For potential individual chart export button\n    const [isChartReady, setIsChartReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogChartView.useEffect\": ()=>{\n            setIsChartReady(false); // Reset on selection change\n            const timer = setTimeout({\n                \"LogChartView.useEffect.timer\": ()=>setIsChartReady(true)\n            }[\"LogChartView.useEffect.timer\"], 100); // Delay for container sizing\n            return ({\n                \"LogChartView.useEffect\": ()=>clearTimeout(timer)\n            })[\"LogChartView.useEffect\"];\n        }\n    }[\"LogChartView.useEffect\"], [\n        selectedBlockIds,\n        dataChunks\n    ]); // Also depend on dataChunks if it can change independently for selectedBlockIds\n    // Optional: Handler for individual chart export, if a button is added per card\n    const handleExportSingleChart = async (blockIdToExport)=>{\n        if (!chartContainerRef.current) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '图表容器未找到。'\n            });\n            return;\n        }\n        setIsExporting(true);\n        try {\n            await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_3__.exportElementAsImage)(chartContainerRef.current, \"log-chart-single-\".concat(blockIdToExport.substring(0, 8)), [\n                blockIdToExport\n            ] // Pass as array\n            );\n        } catch (error) {\n            console.error(\"Single chart export failed for block:\", blockIdToExport, error);\n        // Toast for error is likely handled in exportElementAsImage\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const hasChartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogChartView.useMemo[hasChartData]\": ()=>{\n            console.log('LogChartView - hasChartData check:', {\n                dataChunks\n            });\n            if (!dataChunks || !Array.isArray(dataChunks)) {\n                console.log('LogChartView - dataChunks is invalid:', dataChunks);\n                return false;\n            }\n            return dataChunks.length > 0;\n        }\n    }[\"LogChartView.useMemo[hasChartData]\"], [\n        dataChunks\n    ]);\n    const chartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogChartView.useMemo[chartData]\": ()=>{\n            console.log('LogChartView - chartData calculation:', {\n                dataChunks,\n                selectedBlockIds\n            });\n            if (!dataChunks || !Array.isArray(dataChunks)) {\n                console.log('LogChartView - dataChunks is invalid in chartData:', dataChunks);\n                return [];\n            }\n            return dataChunks.filter({\n                \"LogChartView.useMemo[chartData]\": (chunk)=>{\n                    console.log('Filtering chunk:', {\n                        chunk,\n                        selectedBlockIds\n                    });\n                    return selectedBlockIds.includes(chunk.block_id);\n                }\n            }[\"LogChartView.useMemo[chartData]\"]).flatMap({\n                \"LogChartView.useMemo[chartData]\": (chunk)=>{\n                    console.log('Processing chunk for chart data:', chunk);\n                    if (!chunk.data || !Array.isArray(chunk.data)) {\n                        console.log('Invalid chunk data:', chunk.data);\n                        return [];\n                    }\n                    return chunk.data.map({\n                        \"LogChartView.useMemo[chartData]\": (item)=>({\n                                name: item.name,\n                                value: item.value,\n                                type: item.type,\n                                block_id: chunk.block_id\n                            })\n                    }[\"LogChartView.useMemo[chartData]\"]);\n                }\n            }[\"LogChartView.useMemo[chartData]\"]);\n        }\n    }[\"LogChartView.useMemo[chartData]\"], [\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const handleBlockSelect = (blockId)=>{\n        console.log('LogChartView - handleBlockSelect called with:', blockId);\n        onBlockSelect(blockId);\n    };\n    const handleExport = async ()=>{\n        console.log('LogChartView - handleExport called');\n        console.log('chartContainerRef:', chartContainerRef.current);\n        console.log('selectedBlockIds:', selectedBlockIds);\n        if (!chartContainerRef.current) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '图表容器未找到。'\n            });\n            return;\n        }\n        if (!selectedBlockIds || selectedBlockIds.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '请至少选择一个数据块进行导出。'\n            });\n            return;\n        }\n        try {\n            await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_3__.exportElementAsImage)(chartContainerRef.current, \"log-analysis-\".concat(Date.now()), selectedBlockIds);\n        } catch (error) {\n            console.error(\"ChartView export failed:\", error);\n            toast({\n                variant: 'destructive',\n                title: '导出失败',\n                description: error instanceof Error ? error.message : '导出过程中发生错误。'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: chartContainerRef,\n        className: \"space-y-6 log-chart-container\",\n        children: dataChunks.map((chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                \"data-block-id\": chunk.block_id,\n                className: selectedBlockIds.includes(chunk.block_id) ? 'block' : 'hidden',\n                style: {\n                    display: selectedBlockIds.includes(chunk.block_id) ? 'block' : 'none',\n                    minHeight: selectedBlockIds.includes(chunk.block_id) ? '450px' : '0'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row justify-between items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: [\n                                \"数据块 \",\n                                chunk.block_id\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-[400px] min-h-[400px]\",\n                            style: {\n                                width: '100%',\n                                height: '400px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartRenderer, {\n                                chunk: chunk,\n                                isChartReady: isChartReady\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, chunk.block_id, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, this);\n}\n_s1(LogChartView, \"4zLGQo+Qm3la1mZoosUVbRUMgWY=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c1 = LogChartView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogChartView);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartRenderer\");\n$RefreshReg$(_c1, \"LogChartView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/LogChartView.tsx\n"));

/***/ })

});