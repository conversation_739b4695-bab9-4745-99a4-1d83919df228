"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx":
/*!****************************************************!*\
  !*** ./components/log-analysis/LogDisplayArea.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// LogChartView is removed as it will be handled by the parent page\n\n\n\n\n\n\nconst LogDisplayArea = (param)=>{\n    let { dataChunks, onSelectionChange, onStartExport } = param;\n    _s();\n    console.log('[LogDisplayArea] Rendering. ProcessedDataChunks count:', dataChunks.length);\n    const [selectedBlockId, setSelectedBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''); // 单选显示\n    const [exportBlockIds, setExportBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 多选导出\n    const displayAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const onSelectionChangeRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(onSelectionChange);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            onSelectionChangeRef.current = onSelectionChange;\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        onSelectionChange\n    ]);\n    // 当有新的数据块时，自动选择第一个\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            console.log('[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:', dataChunks.length, 'current selectedBlockId:', selectedBlockId);\n            if (dataChunks.length === 0) {\n                if (selectedBlockId !== '') {\n                    console.log('[LogDisplayArea] No data chunks, clearing selectedBlockId.');\n                    setSelectedBlockId('');\n                }\n            } else {\n                const currentSelectionIsValid = selectedBlockId && selectedBlockId.trim() !== '' && dataChunks.some({\n                    \"LogDisplayArea.useEffect\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect\"]);\n                if (!currentSelectionIsValid) {\n                    console.log('[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.');\n                    const firstValidBlock = dataChunks.find({\n                        \"LogDisplayArea.useEffect.firstValidBlock\": (chunk)=>chunk.block_id && typeof chunk.block_id === 'string' && chunk.block_id.trim() !== ''\n                    }[\"LogDisplayArea.useEffect.firstValidBlock\"]);\n                    if (firstValidBlock) {\n                        console.log('[LogDisplayArea] Found first valid block. Setting selectedBlockId to:', firstValidBlock.block_id);\n                        setSelectedBlockId(firstValidBlock.block_id);\n                    } else {\n                        if (selectedBlockId !== '') {\n                            console.warn('[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId.');\n                            setSelectedBlockId('');\n                        }\n                    }\n                } else {\n                    console.log('[LogDisplayArea] Current selection is still valid:', selectedBlockId);\n                }\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        dataChunks,\n        selectedBlockId\n    ]); // Keep selectedBlockId in dependencies to re-evaluate if it changes externally or becomes invalid\n    // 当显示选择改变时，通知父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            if (selectedBlockId && dataChunks.length > 0) {\n                const selected = dataChunks.filter({\n                    \"LogDisplayArea.useEffect.selected\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect.selected\"]);\n                onSelectionChangeRef.current(selected); // 传递筛选出的块\n            } else {\n                onSelectionChangeRef.current([]); // 如果没有选中的ID或没有数据块，传递空数组\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        selectedBlockId,\n        dataChunks\n    ]);\n    const handleBlockSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleBlockSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleBlockSelectionChange - START. blockId:', blockId);\n            setSelectedBlockId(blockId);\n            console.log('[LogDisplayArea] handleBlockSelectionChange - END.');\n        }\n    }[\"LogDisplayArea.useCallback[handleBlockSelectionChange]\"], []);\n    const handleExportSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleExportSelectionChange - START. blockId:', blockId);\n            setExportBlockIds({\n                \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (prevSelected)=>{\n                    const newSelection = prevSelected.includes(blockId) ? prevSelected.filter({\n                        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (id)=>id !== blockId\n                    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]) : [\n                        ...prevSelected,\n                        blockId\n                    ];\n                    console.log('[LogDisplayArea] handleExportSelectionChange - New selection:', newSelection);\n                    return newSelection;\n                }\n            }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]);\n        }\n    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"], []);\n    const selectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[selectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] selectAllForExport - START');\n            const allIds = dataChunks.map({\n                \"LogDisplayArea.useCallback[selectAllForExport].allIds\": (chunk)=>chunk.block_id\n            }[\"LogDisplayArea.useCallback[selectAllForExport].allIds\"]);\n            setExportBlockIds(allIds);\n            console.log('[LogDisplayArea] selectAllForExport - END. Selected all IDs:', allIds);\n        }\n    }[\"LogDisplayArea.useCallback[selectAllForExport]\"], [\n        dataChunks\n    ]);\n    const deselectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[deselectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] deselectAllForExport - START');\n            setExportBlockIds([]);\n            console.log('[LogDisplayArea] deselectAllForExport - END');\n        }\n    }[\"LogDisplayArea.useCallback[deselectAllForExport]\"], []);\n    const handleExportAllImages = async ()=>{\n        console.log('handleExportAllImages called');\n        console.log('exportBlockIds:', exportBlockIds);\n        if (!dataChunks || dataChunks.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '没有可导出的内容。'\n            });\n            return;\n        }\n        if (exportBlockIds.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '请至少选择一个数据块进行导出。'\n            });\n            return;\n        }\n        // 使用主页面的导出模式，这样可以正确渲染所有选中的图表\n        console.log('[LogDisplayArea] Triggering export via onStartExport callback');\n        onStartExport(exportBlockIds);\n    };\n    const hasContentToExport = dataChunks && dataChunks.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"h-[450px] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"选择数据块进行分析\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"从解析的日志文件中选择一个数据块以在图表中显示。\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleExportAllImages,\n                        disabled: !hasContentToExport || exportBlockIds.length === 0,\n                        size: \"sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined),\n                            \"导出选中图片\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: displayAreaRef,\n                    className: \"h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: selectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: deselectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"取消全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground ml-2\",\n                                    children: [\n                                        \"已选择导出: \",\n                                        exportBlockIds.length,\n                                        \" 项\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                isExportingAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 ml-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                        value: exportProgress,\n                                        className: \"h-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-2 border rounded-md\",\n                                children: dataChunks.map((chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroup, {\n                                                value: selectedBlockId,\n                                                onValueChange: handleBlockSelectionChange,\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                            value: chunk.block_id,\n                                                            id: \"display-\".concat(chunk.block_id)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                            htmlFor: \"display-\".concat(chunk.block_id),\n                                                            className: \"cursor-pointer\",\n                                                            children: \"显示\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"export-\".concat(chunk.block_id),\n                                                        checked: exportBlockIds.includes(chunk.block_id),\n                                                        onChange: ()=>handleExportSelectionChange(chunk.block_id),\n                                                        className: \"h-4 w-4 rounded border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"export-\".concat(chunk.block_id),\n                                                        className: \"cursor-pointer\",\n                                                        children: \"数据块 \".concat(chunk.block_id, \" (胶厚: \").concat(chunk.glue_thickness_values.length, \", 准直: \").concat(chunk.collimation_diff_values.length, \")\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, chunk.block_id, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined),\n                        (!dataChunks || dataChunks.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground p-2\",\n                            children: \"暂无数据块可供分析或导出。\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LogDisplayArea, \"ghX4kALUHkvRRCRMmi6hPcionB0=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = LogDisplayArea;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogDisplayArea);\nvar _c;\n$RefreshReg$(_c, \"LogDisplayArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\n"));

/***/ })

});