"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx":
/*!****************************************************!*\
  !*** ./components/log-analysis/LogDisplayArea.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// LogChartView is removed as it will be handled by the parent page\n\n\n\n\n\n\n\nconst LogDisplayArea = (param)=>{\n    let { dataChunks, onSelectionChange, onStartExport } = param;\n    _s();\n    console.log('[LogDisplayArea] Rendering. ProcessedDataChunks count:', dataChunks.length);\n    const [selectedBlockId, setSelectedBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''); // 单选显示\n    const [exportBlockIds, setExportBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 多选导出\n    const displayAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const onSelectionChangeRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(onSelectionChange);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            onSelectionChangeRef.current = onSelectionChange;\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        onSelectionChange\n    ]);\n    // 当有新的数据块时，自动选择第一个\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            console.log('[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:', dataChunks.length, 'current selectedBlockId:', selectedBlockId);\n            if (dataChunks.length === 0) {\n                if (selectedBlockId !== '') {\n                    console.log('[LogDisplayArea] No data chunks, clearing selectedBlockId.');\n                    setSelectedBlockId('');\n                }\n            } else {\n                const currentSelectionIsValid = selectedBlockId && selectedBlockId.trim() !== '' && dataChunks.some({\n                    \"LogDisplayArea.useEffect\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect\"]);\n                if (!currentSelectionIsValid) {\n                    console.log('[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.');\n                    const firstValidBlock = dataChunks.find({\n                        \"LogDisplayArea.useEffect.firstValidBlock\": (chunk)=>chunk.block_id && typeof chunk.block_id === 'string' && chunk.block_id.trim() !== ''\n                    }[\"LogDisplayArea.useEffect.firstValidBlock\"]);\n                    if (firstValidBlock) {\n                        console.log('[LogDisplayArea] Found first valid block. Setting selectedBlockId to:', firstValidBlock.block_id);\n                        setSelectedBlockId(firstValidBlock.block_id);\n                    } else {\n                        if (selectedBlockId !== '') {\n                            console.warn('[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId.');\n                            setSelectedBlockId('');\n                        }\n                    }\n                } else {\n                    console.log('[LogDisplayArea] Current selection is still valid:', selectedBlockId);\n                }\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        dataChunks,\n        selectedBlockId\n    ]); // Keep selectedBlockId in dependencies to re-evaluate if it changes externally or becomes invalid\n    // 当显示选择改变时，通知父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogDisplayArea.useEffect\": ()=>{\n            if (selectedBlockId && dataChunks.length > 0) {\n                const selected = dataChunks.filter({\n                    \"LogDisplayArea.useEffect.selected\": (chunk)=>chunk.block_id === selectedBlockId\n                }[\"LogDisplayArea.useEffect.selected\"]);\n                onSelectionChangeRef.current(selected); // 传递筛选出的块\n            } else {\n                onSelectionChangeRef.current([]); // 如果没有选中的ID或没有数据块，传递空数组\n            }\n        }\n    }[\"LogDisplayArea.useEffect\"], [\n        selectedBlockId,\n        dataChunks\n    ]);\n    const handleBlockSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleBlockSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleBlockSelectionChange - START. blockId:', blockId);\n            setSelectedBlockId(blockId);\n            console.log('[LogDisplayArea] handleBlockSelectionChange - END.');\n        }\n    }[\"LogDisplayArea.useCallback[handleBlockSelectionChange]\"], []);\n    const handleExportSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (blockId)=>{\n            console.log('[LogDisplayArea] handleExportSelectionChange - START. blockId:', blockId);\n            setExportBlockIds({\n                \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (prevSelected)=>{\n                    const newSelection = prevSelected.includes(blockId) ? prevSelected.filter({\n                        \"LogDisplayArea.useCallback[handleExportSelectionChange]\": (id)=>id !== blockId\n                    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]) : [\n                        ...prevSelected,\n                        blockId\n                    ];\n                    console.log('[LogDisplayArea] handleExportSelectionChange - New selection:', newSelection);\n                    return newSelection;\n                }\n            }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"]);\n        }\n    }[\"LogDisplayArea.useCallback[handleExportSelectionChange]\"], []);\n    const selectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[selectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] selectAllForExport - START');\n            const allIds = dataChunks.map({\n                \"LogDisplayArea.useCallback[selectAllForExport].allIds\": (chunk)=>chunk.block_id\n            }[\"LogDisplayArea.useCallback[selectAllForExport].allIds\"]);\n            setExportBlockIds(allIds);\n            console.log('[LogDisplayArea] selectAllForExport - END. Selected all IDs:', allIds);\n        }\n    }[\"LogDisplayArea.useCallback[selectAllForExport]\"], [\n        dataChunks\n    ]);\n    const deselectAllForExport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogDisplayArea.useCallback[deselectAllForExport]\": ()=>{\n            console.log('[LogDisplayArea] deselectAllForExport - START');\n            setExportBlockIds([]);\n            console.log('[LogDisplayArea] deselectAllForExport - END');\n        }\n    }[\"LogDisplayArea.useCallback[deselectAllForExport]\"], []);\n    const handleExportAllImages = async ()=>{\n        console.log('handleExportAllImages called');\n        console.log('exportBlockIds:', exportBlockIds);\n        if (!dataChunks || dataChunks.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '没有可导出的内容。'\n            });\n            return;\n        }\n        if (exportBlockIds.length === 0) {\n            toast({\n                variant: 'destructive',\n                title: '错误',\n                description: '请至少选择一个数据块进行导出。'\n            });\n            return;\n        }\n        // 使用主页面的导出模式，这样可以正确渲染所有选中的图表\n        console.log('[LogDisplayArea] Triggering export via onStartExport callback');\n        onStartExport(exportBlockIds);\n    };\n    const hasContentToExport = dataChunks && dataChunks.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"h-[450px] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"选择数据块进行分析\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"从解析的日志文件中选择一个数据块以在图表中显示。\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleExportAllImages,\n                        disabled: isExportingAll || !hasContentToExport || exportBlockIds.length === 0,\n                        size: \"sm\",\n                        children: isExportingAll ? '导出中...' : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"导出选中图片\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"flex-1 overflow-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: displayAreaRef,\n                    className: \"h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: selectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: deselectAllForExport,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    children: \"取消全选\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground ml-2\",\n                                    children: [\n                                        \"已选择导出: \",\n                                        exportBlockIds.length,\n                                        \" 项\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                isExportingAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 ml-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                        value: exportProgress,\n                                        className: \"h-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-2 border rounded-md\",\n                                children: dataChunks.map((chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__.RadioGroup, {\n                                                value: selectedBlockId,\n                                                onValueChange: handleBlockSelectionChange,\n                                                className: \"flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_7__.RadioGroupItem, {\n                                                            value: chunk.block_id,\n                                                            id: \"display-\".concat(chunk.block_id)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                            htmlFor: \"display-\".concat(chunk.block_id),\n                                                            className: \"cursor-pointer\",\n                                                            children: \"显示\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"export-\".concat(chunk.block_id),\n                                                        checked: exportBlockIds.includes(chunk.block_id),\n                                                        onChange: ()=>handleExportSelectionChange(chunk.block_id),\n                                                        className: \"h-4 w-4 rounded border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"export-\".concat(chunk.block_id),\n                                                        className: \"cursor-pointer\",\n                                                        children: \"数据块 \".concat(chunk.block_id, \" (胶厚: \").concat(chunk.glue_thickness_values.length, \", 准直: \").concat(chunk.collimation_diff_values.length, \")\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, chunk.block_id, true, {\n                                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined),\n                        (!dataChunks || dataChunks.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground p-2\",\n                            children: \"暂无数据块可供分析或导出。\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogDisplayArea.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LogDisplayArea, \"ghX4kALUHkvRRCRMmi6hPcionB0=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = LogDisplayArea;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogDisplayArea);\nvar _c;\n$RefreshReg$(_c, \"LogDisplayArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvbG9nLWFuYWx5c2lzL0xvZ0Rpc3BsYXlBcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXdFO0FBQ3hFLG1FQUFtRTtBQUM4QjtBQUVuRDtBQUNFO0FBQ1I7QUFFYTtBQUVEO0FBQ3FCO0FBb0J6RSxNQUFNaUIsaUJBQWdEO1FBQUMsRUFBRUMsVUFBVSxFQUFFQyxpQkFBaUIsRUFBRUMsYUFBYSxFQUFFOztJQUNyR0MsUUFBUUMsR0FBRyxDQUFDLDBEQUEwREosV0FBV0ssTUFBTTtJQUN2RixNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUd4QiwrQ0FBUUEsQ0FBUyxLQUFLLE9BQU87SUFDM0UsTUFBTSxDQUFDeUIsZ0JBQWdCQyxrQkFBa0IsR0FBRzFCLCtDQUFRQSxDQUFXLEVBQUUsR0FBRyxPQUFPO0lBQzNFLE1BQU0yQixpQkFBaUJ4Qiw2Q0FBTUEsQ0FBaUI7SUFDOUMsTUFBTSxFQUFFeUIsS0FBSyxFQUFFLEdBQUdoQixrRUFBUUE7SUFFMUIsTUFBTWlCLHVCQUF1QjlCLG1EQUFZLENBQUNtQjtJQUMxQ2pCLGdEQUFTQTtvQ0FBQztZQUNSNEIscUJBQXFCQyxPQUFPLEdBQUdaO1FBQ2pDO21DQUFHO1FBQUNBO0tBQWtCO0lBRXRCLG1CQUFtQjtJQUNyQmpCLGdEQUFTQTtvQ0FBQztZQUNSbUIsUUFBUUMsR0FBRyxDQUFDLHVGQUF1RkosV0FBV0ssTUFBTSxFQUFFLDRCQUE0QkM7WUFDbEosSUFBSU4sV0FBV0ssTUFBTSxLQUFLLEdBQUc7Z0JBQzNCLElBQUlDLG9CQUFvQixJQUFJO29CQUMxQkgsUUFBUUMsR0FBRyxDQUFDO29CQUNaRyxtQkFBbUI7Z0JBQ3JCO1lBQ0YsT0FBTztnQkFDTCxNQUFNTywwQkFBMEJSLG1CQUNGQSxnQkFBZ0JTLElBQUksT0FBTyxNQUMzQmYsV0FBV2dCLElBQUk7Z0RBQUNDLENBQUFBLFFBQVNBLE1BQU1DLFFBQVEsS0FBS1o7O2dCQUUxRSxJQUFJLENBQUNRLHlCQUF5QjtvQkFDNUJYLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixNQUFNZSxrQkFBa0JuQixXQUFXb0IsSUFBSTtvRUFDckMsQ0FBQ0gsUUFBVUEsTUFBTUMsUUFBUSxJQUFJLE9BQU9ELE1BQU1DLFFBQVEsS0FBSyxZQUFZRCxNQUFNQyxRQUFRLENBQUNILElBQUksT0FBTzs7b0JBRy9GLElBQUlJLGlCQUFpQjt3QkFDbkJoQixRQUFRQyxHQUFHLENBQUMseUVBQXlFZSxnQkFBZ0JELFFBQVE7d0JBQzdHWCxtQkFBbUJZLGdCQUFnQkQsUUFBUTtvQkFDN0MsT0FBTzt3QkFDTCxJQUFJWixvQkFBb0IsSUFBSTs0QkFDMUJILFFBQVFrQixJQUFJLENBQUM7NEJBQ2JkLG1CQUFtQjt3QkFDckI7b0JBQ0Y7Z0JBQ0YsT0FBTztvQkFDTEosUUFBUUMsR0FBRyxDQUFDLHNEQUFzREU7Z0JBQ3BFO1lBQ0Y7UUFDRjttQ0FBRztRQUFDTjtRQUFZTTtLQUFnQixHQUFHLGtHQUFrRztJQUVuSSxpQkFBaUI7SUFDbkJ0QixnREFBU0E7b0NBQUM7WUFDUixJQUFJc0IsbUJBQW1CTixXQUFXSyxNQUFNLEdBQUcsR0FBRztnQkFDNUMsTUFBTWlCLFdBQVd0QixXQUFXdUIsTUFBTTt5REFDaEMsQ0FBQ04sUUFBVUEsTUFBTUMsUUFBUSxLQUFLWjs7Z0JBRWhDTSxxQkFBcUJDLE9BQU8sQ0FBQ1MsV0FBVyxVQUFVO1lBQ3BELE9BQU87Z0JBQ0xWLHFCQUFxQkMsT0FBTyxDQUFDLEVBQUUsR0FBRyx3QkFBd0I7WUFDNUQ7UUFDRjttQ0FBRztRQUFDUDtRQUFpQk47S0FBVztJQUU5QixNQUFNd0IsNkJBQTZCdkMsa0RBQVdBO2tFQUFDLENBQUN3QztZQUM5Q3RCLFFBQVFDLEdBQUcsQ0FBQyxpRUFBaUVxQjtZQUM3RWxCLG1CQUFtQmtCO1lBQ25CdEIsUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7aUVBQUcsRUFBRTtJQUVMLE1BQU1zQiw4QkFBOEJ6QyxrREFBV0E7bUVBQUMsQ0FBQ3dDO1lBQy9DdEIsUUFBUUMsR0FBRyxDQUFDLGtFQUFrRXFCO1lBQzlFaEI7MkVBQWtCa0IsQ0FBQUE7b0JBQ2hCLE1BQU1DLGVBQWVELGFBQWFFLFFBQVEsQ0FBQ0osV0FDdkNFLGFBQWFKLE1BQU07bUZBQUNPLENBQUFBLEtBQU1BLE9BQU9MO29GQUNqQzsyQkFBSUU7d0JBQWNGO3FCQUFRO29CQUM5QnRCLFFBQVFDLEdBQUcsQ0FBQyxpRUFBaUV3QjtvQkFDN0UsT0FBT0E7Z0JBQ1Q7O1FBQ0Y7a0VBQUcsRUFBRTtJQUVMLE1BQU1HLHFCQUFxQjlDLGtEQUFXQTswREFBQztZQUNyQ2tCLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU00QixTQUFTaEMsV0FBV2lDLEdBQUc7eUVBQUNoQixDQUFBQSxRQUFTQSxNQUFNQyxRQUFROztZQUNyRFQsa0JBQWtCdUI7WUFDbEI3QixRQUFRQyxHQUFHLENBQUMsZ0VBQWdFNEI7UUFDOUU7eURBQUc7UUFBQ2hDO0tBQVc7SUFFZixNQUFNa0MsdUJBQXVCakQsa0RBQVdBOzREQUFDO1lBQ3ZDa0IsUUFBUUMsR0FBRyxDQUFDO1lBQ1pLLGtCQUFrQixFQUFFO1lBQ3BCTixRQUFRQyxHQUFHLENBQUM7UUFDZDsyREFBRyxFQUFFO0lBRUwsTUFBTStCLHdCQUF3QjtRQUM1QmhDLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsbUJBQW1CSTtRQUUvQixJQUFJLENBQUNSLGNBQWNBLFdBQVdLLE1BQU0sS0FBSyxHQUFHO1lBQzFDTSxNQUFNO2dCQUFFeUIsU0FBUztnQkFBZUMsT0FBTztnQkFBTUMsYUFBYTtZQUFZO1lBQ3RFO1FBQ0Y7UUFDQSxJQUFJOUIsZUFBZUgsTUFBTSxLQUFLLEdBQUc7WUFDL0JNLE1BQU07Z0JBQUV5QixTQUFTO2dCQUFlQyxPQUFPO2dCQUFNQyxhQUFhO1lBQWtCO1lBQzVFO1FBQ0Y7UUFFQSw2QkFBNkI7UUFDN0JuQyxRQUFRQyxHQUFHLENBQUM7UUFDWkYsY0FBY007SUFDaEI7SUFFQSxNQUFNK0IscUJBQXFCdkMsY0FBY0EsV0FBV0ssTUFBTSxHQUFHO0lBRTdELHFCQUNFLDhEQUFDbEIscURBQUlBO1FBQUNxRCxXQUFVOzswQkFDZCw4REFBQ2xELDJEQUFVQTtnQkFBQ2tELFdBQVU7O2tDQUNwQiw4REFBQ0M7OzBDQUNDLDhEQUFDbEQsMERBQVNBOzBDQUFDOzs7Ozs7MENBQ1gsOERBQUNGLGdFQUFlQTswQ0FBQzs7Ozs7Ozs7Ozs7O2tDQUluQiw4REFBQ0kseURBQU1BO3dCQUFDaUQsU0FBU1A7d0JBQXVCUSxVQUFVQyxrQkFBa0IsQ0FBQ0wsc0JBQXNCL0IsZUFBZUgsTUFBTSxLQUFLO3dCQUFHd0MsTUFBSztrQ0FDMUhELGlCQUNDLHlCQUVBOzs4Q0FDRSw4REFBQ2xELG9GQUFRQTtvQ0FBQzhDLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7Ozs7Ozs7OzBCQU03Qyw4REFBQ3BELDREQUFXQTtnQkFBQ29ELFdBQVU7MEJBQ3JCLDRFQUFDQztvQkFBSUssS0FBS3BDO29CQUFnQjhCLFdBQVU7O3NDQUNsQyw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDL0MseURBQU1BO29DQUFDaUQsU0FBU1g7b0NBQW9CYyxNQUFLO29DQUFLVCxTQUFROzhDQUFVOzs7Ozs7OENBQ2pFLDhEQUFDM0MseURBQU1BO29DQUFDaUQsU0FBU1I7b0NBQXNCVyxNQUFLO29DQUFLVCxTQUFROzhDQUFVOzs7Ozs7OENBQ25FLDhEQUFDVztvQ0FBS1AsV0FBVTs7d0NBQXFDO3dDQUFRaEMsZUFBZUgsTUFBTTt3Q0FBQzs7Ozs7OztnQ0FDbEZ1QyxnQ0FDQyw4REFBQ0g7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUM1Qyw2REFBUUE7d0NBQUNvRCxPQUFPQzt3Q0FBZ0JULFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlqRCw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNDO2dDQUFJRCxXQUFVOzBDQUNaeEMsV0FBV2lDLEdBQUcsQ0FBQyxDQUFDaEIsc0JBQ2YsOERBQUN3Qjt3Q0FBeUJELFdBQVU7OzBEQUNsQyw4REFBQzNDLGtFQUFVQTtnREFDVG1ELE9BQU8xQztnREFDUDRDLGVBQWUxQjtnREFDZmdCLFdBQVU7MERBRVYsNEVBQUNDO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQzFDLHNFQUFjQTs0REFBQ2tELE9BQU8vQixNQUFNQyxRQUFROzREQUFFWSxJQUFJLFdBQTBCLE9BQWZiLE1BQU1DLFFBQVE7Ozs7OztzRUFDcEUsOERBQUMxQix1REFBS0E7NERBQUMyRCxTQUFTLFdBQTBCLE9BQWZsQyxNQUFNQyxRQUFROzREQUFJc0IsV0FBVTtzRUFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUs1RSw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDWTt3REFDQ0MsTUFBSzt3REFDTHZCLElBQUksVUFBeUIsT0FBZmIsTUFBTUMsUUFBUTt3REFDNUJvQyxTQUFTOUMsZUFBZXFCLFFBQVEsQ0FBQ1osTUFBTUMsUUFBUTt3REFDL0NxQyxVQUFVLElBQU03Qiw0QkFBNEJULE1BQU1DLFFBQVE7d0RBQzFEc0IsV0FBVTs7Ozs7O2tFQUVaLDhEQUFDaEQsdURBQUtBO3dEQUFDMkQsU0FBUyxVQUF5QixPQUFmbEMsTUFBTUMsUUFBUTt3REFBSXNCLFdBQVU7a0VBQ25ELE9BQThCdkIsT0FBdkJBLE1BQU1DLFFBQVEsRUFBQyxVQUFtREQsT0FBM0NBLE1BQU11QyxxQkFBcUIsQ0FBQ25ELE1BQU0sRUFBQyxVQUE2QyxPQUFyQ1ksTUFBTXdDLHVCQUF1QixDQUFDcEQsTUFBTSxFQUFDOzs7Ozs7Ozs7Ozs7O3VDQXRCM0dZLE1BQU1DLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozt3QkE2QjVCLEVBQUNsQixjQUFjQSxXQUFXSyxNQUFNLEtBQUssb0JBQ3JDLDhEQUFDcUQ7NEJBQUVsQixXQUFVO3NDQUE0Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNckQ7R0FuTE16Qzs7UUFLY0osOERBQVFBOzs7S0FMdEJJO0FBcUxOLGlFQUFlQSxjQUFjQSxFQUFDIiwic291cmNlcyI6WyJEOlxccHljb2RlXFxzdXBwb3J0X2NoYXJ0MlxcaG90ZWwtZGFzaGJvYXJkXFxjb21wb25lbnRzXFxsb2ctYW5hbHlzaXNcXExvZ0Rpc3BsYXlBcmVhLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xyXG4vLyBMb2dDaGFydFZpZXcgaXMgcmVtb3ZlZCBhcyBpdCB3aWxsIGJlIGhhbmRsZWQgYnkgdGhlIHBhcmVudCBwYWdlXHJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiO1xyXG5pbXBvcnQgeyBDaGVja2JveCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2hlY2tib3hcIjtcclxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCI7XHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xyXG5pbXBvcnQgeyBEb3dubG9hZCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XHJcbmltcG9ydCB7IGV4cG9ydEVsZW1lbnRBc0ltYWdlIH0gZnJvbSAnLi4vLi4vbGliL2V4cG9ydFV0aWxzJzsgLy8g6LCD5pW06Lev5b6EXHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3VzZS10b2FzdCc7XHJcbmltcG9ydCB7IFByb2Nlc3NlZEJsb2NrIGFzIFdvcmtlclByb2Nlc3NlZEJsb2NrIH0gZnJvbSAnQC93b3JrZXJzL2xvZ1BhcnNlci5kZWZpbml0aW9ucyc7XHJcbmltcG9ydCB7IFByb2dyZXNzIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9wcm9ncmVzc1wiO1xyXG5pbXBvcnQgeyBSYWRpb0dyb3VwLCBSYWRpb0dyb3VwSXRlbSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvcmFkaW8tZ3JvdXBcIjtcclxuaW1wb3J0IHsgTG9nQ2hhcnRWaWV3IH0gZnJvbSAnLi9Mb2dDaGFydFZpZXcnO1xyXG5cclxuaW50ZXJmYWNlIENoYXJ0RGF0YUl0ZW0ge1xyXG4gIG5hbWU6IHN0cmluZztcclxuICB2YWx1ZTogbnVtYmVyO1xyXG4gIHR5cGU6IHN0cmluZztcclxuICBibG9ja19pZDogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgUHJvY2Vzc2VkQmxvY2sgZXh0ZW5kcyBXb3JrZXJQcm9jZXNzZWRCbG9jayB7XHJcbiAgZGF0YTogQ2hhcnREYXRhSXRlbVtdO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgTG9nRGlzcGxheUFyZWFQcm9wcyB7XHJcbiAgZGF0YUNodW5rczogUHJvY2Vzc2VkQmxvY2tbXTtcclxuICBvblNlbGVjdGlvbkNoYW5nZTogKHNlbGVjdGVkQmxvY2tzOiBQcm9jZXNzZWRCbG9ja1tdKSA9PiB2b2lkO1xyXG4gIG9uU3RhcnRFeHBvcnQ6IChleHBvcnRJZHM6IHN0cmluZ1tdKSA9PiB2b2lkO1xyXG59XHJcblxyXG5jb25zdCBMb2dEaXNwbGF5QXJlYTogUmVhY3QuRkM8TG9nRGlzcGxheUFyZWFQcm9wcz4gPSAoeyBkYXRhQ2h1bmtzLCBvblNlbGVjdGlvbkNoYW5nZSwgb25TdGFydEV4cG9ydCB9KSA9PiB7XHJcbiAgY29uc29sZS5sb2coJ1tMb2dEaXNwbGF5QXJlYV0gUmVuZGVyaW5nLiBQcm9jZXNzZWREYXRhQ2h1bmtzIGNvdW50OicsIGRhdGFDaHVua3MubGVuZ3RoKTtcclxuICBjb25zdCBbc2VsZWN0ZWRCbG9ja0lkLCBzZXRTZWxlY3RlZEJsb2NrSWRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7IC8vIOWNlemAieaYvuekulxyXG4gIGNvbnN0IFtleHBvcnRCbG9ja0lkcywgc2V0RXhwb3J0QmxvY2tJZHNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKTsgLy8g5aSa6YCJ5a+85Ye6XHJcbiAgY29uc3QgZGlzcGxheUFyZWFSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KCk7XHJcblxyXG4gIGNvbnN0IG9uU2VsZWN0aW9uQ2hhbmdlUmVmID0gUmVhY3QudXNlUmVmKG9uU2VsZWN0aW9uQ2hhbmdlKTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgb25TZWxlY3Rpb25DaGFuZ2VSZWYuY3VycmVudCA9IG9uU2VsZWN0aW9uQ2hhbmdlO1xyXG4gIH0sIFtvblNlbGVjdGlvbkNoYW5nZV0pO1xyXG5cclxuICAvLyDlvZPmnInmlrDnmoTmlbDmja7lnZfml7bvvIzoh6rliqjpgInmi6nnrKzkuIDkuKpcclxudXNlRWZmZWN0KCgpID0+IHtcclxuICBjb25zb2xlLmxvZygnW0xvZ0Rpc3BsYXlBcmVhXSB1c2VFZmZlY3QgZm9yIGF1dG8tc2VsZWN0aW9uIHRyaWdnZXJlZC4gcHJvY2Vzc2VkRGF0YUNodW5rcyBjb3VudDonLCBkYXRhQ2h1bmtzLmxlbmd0aCwgJ2N1cnJlbnQgc2VsZWN0ZWRCbG9ja0lkOicsIHNlbGVjdGVkQmxvY2tJZCk7XHJcbiAgaWYgKGRhdGFDaHVua3MubGVuZ3RoID09PSAwKSB7XHJcbiAgICBpZiAoc2VsZWN0ZWRCbG9ja0lkICE9PSAnJykge1xyXG4gICAgICBjb25zb2xlLmxvZygnW0xvZ0Rpc3BsYXlBcmVhXSBObyBkYXRhIGNodW5rcywgY2xlYXJpbmcgc2VsZWN0ZWRCbG9ja0lkLicpO1xyXG4gICAgICBzZXRTZWxlY3RlZEJsb2NrSWQoJycpO1xyXG4gICAgfVxyXG4gIH0gZWxzZSB7XHJcbiAgICBjb25zdCBjdXJyZW50U2VsZWN0aW9uSXNWYWxpZCA9IHNlbGVjdGVkQmxvY2tJZCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRCbG9ja0lkLnRyaW0oKSAhPT0gJycgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFDaHVua3Muc29tZShjaHVuayA9PiBjaHVuay5ibG9ja19pZCA9PT0gc2VsZWN0ZWRCbG9ja0lkKTtcclxuXHJcbiAgICBpZiAoIWN1cnJlbnRTZWxlY3Rpb25Jc1ZhbGlkKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdbTG9nRGlzcGxheUFyZWFdIEN1cnJlbnQgc2VsZWN0aW9uIGlzIGludmFsaWQgb3Igbm90IHNldC4gQXR0ZW1wdGluZyB0byBmaW5kIGZpcnN0IHZhbGlkIGJsb2NrLicpO1xyXG4gICAgICBjb25zdCBmaXJzdFZhbGlkQmxvY2sgPSBkYXRhQ2h1bmtzLmZpbmQoXHJcbiAgICAgICAgKGNodW5rKSA9PiBjaHVuay5ibG9ja19pZCAmJiB0eXBlb2YgY2h1bmsuYmxvY2tfaWQgPT09ICdzdHJpbmcnICYmIGNodW5rLmJsb2NrX2lkLnRyaW0oKSAhPT0gJydcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGlmIChmaXJzdFZhbGlkQmxvY2spIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnW0xvZ0Rpc3BsYXlBcmVhXSBGb3VuZCBmaXJzdCB2YWxpZCBibG9jay4gU2V0dGluZyBzZWxlY3RlZEJsb2NrSWQgdG86JywgZmlyc3RWYWxpZEJsb2NrLmJsb2NrX2lkKTtcclxuICAgICAgICBzZXRTZWxlY3RlZEJsb2NrSWQoZmlyc3RWYWxpZEJsb2NrLmJsb2NrX2lkKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBpZiAoc2VsZWN0ZWRCbG9ja0lkICE9PSAnJykge1xyXG4gICAgICAgICAgY29uc29sZS53YXJuKCdbTG9nRGlzcGxheUFyZWFdIE5vIHZhbGlkIGJsb2NrX2lkIGZvdW5kIGluIGFueSBwcm9jZXNzZWQgY2h1bmtzLiBDbGVhcmluZyBzZWxlY3RlZEJsb2NrSWQuJyk7XHJcbiAgICAgICAgICBzZXRTZWxlY3RlZEJsb2NrSWQoJycpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5sb2coJ1tMb2dEaXNwbGF5QXJlYV0gQ3VycmVudCBzZWxlY3Rpb24gaXMgc3RpbGwgdmFsaWQ6Jywgc2VsZWN0ZWRCbG9ja0lkKTtcclxuICAgIH1cclxuICB9XHJcbn0sIFtkYXRhQ2h1bmtzLCBzZWxlY3RlZEJsb2NrSWRdKTsgLy8gS2VlcCBzZWxlY3RlZEJsb2NrSWQgaW4gZGVwZW5kZW5jaWVzIHRvIHJlLWV2YWx1YXRlIGlmIGl0IGNoYW5nZXMgZXh0ZXJuYWxseSBvciBiZWNvbWVzIGludmFsaWRcclxuXHJcbiAgLy8g5b2T5pi+56S66YCJ5oup5pS55Y+Y5pe277yM6YCa55+l54i257uE5Lu2XHJcbnVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgaWYgKHNlbGVjdGVkQmxvY2tJZCAmJiBkYXRhQ2h1bmtzLmxlbmd0aCA+IDApIHtcclxuICAgIGNvbnN0IHNlbGVjdGVkID0gZGF0YUNodW5rcy5maWx0ZXIoXHJcbiAgICAgIChjaHVuaykgPT4gY2h1bmsuYmxvY2tfaWQgPT09IHNlbGVjdGVkQmxvY2tJZFxyXG4gICAgKTtcclxuICAgIG9uU2VsZWN0aW9uQ2hhbmdlUmVmLmN1cnJlbnQoc2VsZWN0ZWQpOyAvLyDkvKDpgJLnrZvpgInlh7rnmoTlnZdcclxuICB9IGVsc2Uge1xyXG4gICAgb25TZWxlY3Rpb25DaGFuZ2VSZWYuY3VycmVudChbXSk7IC8vIOWmguaenOayoeaciemAieS4reeahElE5oiW5rKh5pyJ5pWw5o2u5Z2X77yM5Lyg6YCS56m65pWw57uEXHJcbiAgfVxyXG59LCBbc2VsZWN0ZWRCbG9ja0lkLCBkYXRhQ2h1bmtzXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUJsb2NrU2VsZWN0aW9uQ2hhbmdlID0gdXNlQ2FsbGJhY2soKGJsb2NrSWQ6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ1tMb2dEaXNwbGF5QXJlYV0gaGFuZGxlQmxvY2tTZWxlY3Rpb25DaGFuZ2UgLSBTVEFSVC4gYmxvY2tJZDonLCBibG9ja0lkKTtcclxuICAgIHNldFNlbGVjdGVkQmxvY2tJZChibG9ja0lkKTtcclxuICAgIGNvbnNvbGUubG9nKCdbTG9nRGlzcGxheUFyZWFdIGhhbmRsZUJsb2NrU2VsZWN0aW9uQ2hhbmdlIC0gRU5ELicpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRXhwb3J0U2VsZWN0aW9uQ2hhbmdlID0gdXNlQ2FsbGJhY2soKGJsb2NrSWQ6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ1tMb2dEaXNwbGF5QXJlYV0gaGFuZGxlRXhwb3J0U2VsZWN0aW9uQ2hhbmdlIC0gU1RBUlQuIGJsb2NrSWQ6JywgYmxvY2tJZCk7XHJcbiAgICBzZXRFeHBvcnRCbG9ja0lkcyhwcmV2U2VsZWN0ZWQgPT4ge1xyXG4gICAgICBjb25zdCBuZXdTZWxlY3Rpb24gPSBwcmV2U2VsZWN0ZWQuaW5jbHVkZXMoYmxvY2tJZClcclxuICAgICAgICA/IHByZXZTZWxlY3RlZC5maWx0ZXIoaWQgPT4gaWQgIT09IGJsb2NrSWQpXHJcbiAgICAgICAgOiBbLi4ucHJldlNlbGVjdGVkLCBibG9ja0lkXTtcclxuICAgICAgY29uc29sZS5sb2coJ1tMb2dEaXNwbGF5QXJlYV0gaGFuZGxlRXhwb3J0U2VsZWN0aW9uQ2hhbmdlIC0gTmV3IHNlbGVjdGlvbjonLCBuZXdTZWxlY3Rpb24pO1xyXG4gICAgICByZXR1cm4gbmV3U2VsZWN0aW9uO1xyXG4gICAgfSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBzZWxlY3RBbGxGb3JFeHBvcnQgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnW0xvZ0Rpc3BsYXlBcmVhXSBzZWxlY3RBbGxGb3JFeHBvcnQgLSBTVEFSVCcpO1xyXG4gICAgY29uc3QgYWxsSWRzID0gZGF0YUNodW5rcy5tYXAoY2h1bmsgPT4gY2h1bmsuYmxvY2tfaWQpO1xyXG4gICAgc2V0RXhwb3J0QmxvY2tJZHMoYWxsSWRzKTtcclxuICAgIGNvbnNvbGUubG9nKCdbTG9nRGlzcGxheUFyZWFdIHNlbGVjdEFsbEZvckV4cG9ydCAtIEVORC4gU2VsZWN0ZWQgYWxsIElEczonLCBhbGxJZHMpO1xyXG4gIH0sIFtkYXRhQ2h1bmtzXSk7XHJcblxyXG4gIGNvbnN0IGRlc2VsZWN0QWxsRm9yRXhwb3J0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ1tMb2dEaXNwbGF5QXJlYV0gZGVzZWxlY3RBbGxGb3JFeHBvcnQgLSBTVEFSVCcpO1xyXG4gICAgc2V0RXhwb3J0QmxvY2tJZHMoW10pO1xyXG4gICAgY29uc29sZS5sb2coJ1tMb2dEaXNwbGF5QXJlYV0gZGVzZWxlY3RBbGxGb3JFeHBvcnQgLSBFTkQnKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUV4cG9ydEFsbEltYWdlcyA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdoYW5kbGVFeHBvcnRBbGxJbWFnZXMgY2FsbGVkJyk7XHJcbiAgICBjb25zb2xlLmxvZygnZXhwb3J0QmxvY2tJZHM6JywgZXhwb3J0QmxvY2tJZHMpO1xyXG5cclxuICAgIGlmICghZGF0YUNodW5rcyB8fCBkYXRhQ2h1bmtzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICB0b2FzdCh7IHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsIHRpdGxlOiAn6ZSZ6K+vJywgZGVzY3JpcHRpb246ICfmsqHmnInlj6/lr7zlh7rnmoTlhoXlrrnjgIInIH0pO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcbiAgICBpZiAoZXhwb3J0QmxvY2tJZHMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgIHRvYXN0KHsgdmFyaWFudDogJ2Rlc3RydWN0aXZlJywgdGl0bGU6ICfplJnor68nLCBkZXNjcmlwdGlvbjogJ+ivt+iHs+WwkemAieaLqeS4gOS4quaVsOaNruWdl+i/m+ihjOWvvOWHuuOAgicgfSk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyDkvb/nlKjkuLvpobXpnaLnmoTlr7zlh7rmqKHlvI/vvIzov5nmoLflj6/ku6XmraPnoa7muLLmn5PmiYDmnInpgInkuK3nmoTlm77ooahcclxuICAgIGNvbnNvbGUubG9nKCdbTG9nRGlzcGxheUFyZWFdIFRyaWdnZXJpbmcgZXhwb3J0IHZpYSBvblN0YXJ0RXhwb3J0IGNhbGxiYWNrJyk7XHJcbiAgICBvblN0YXJ0RXhwb3J0KGV4cG9ydEJsb2NrSWRzKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYXNDb250ZW50VG9FeHBvcnQgPSBkYXRhQ2h1bmtzICYmIGRhdGFDaHVua3MubGVuZ3RoID4gMDtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxDYXJkIGNsYXNzTmFtZT1cImgtWzQ1MHB4XSBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8Q2FyZFRpdGxlPumAieaLqeaVsOaNruWdl+i/m+ihjOWIhuaekDwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAg5LuO6Kej5p6Q55qE5pel5b+X5paH5Lu25Lit6YCJ5oup5LiA5Liq5pWw5o2u5Z2X5Lul5Zyo5Zu+6KGo5Lit5pi+56S644CCXHJcbiAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUV4cG9ydEFsbEltYWdlc30gZGlzYWJsZWQ9e2lzRXhwb3J0aW5nQWxsIHx8ICFoYXNDb250ZW50VG9FeHBvcnQgfHwgZXhwb3J0QmxvY2tJZHMubGVuZ3RoID09PSAwfSBzaXplPVwic21cIj5cclxuICAgICAgICAgIHtpc0V4cG9ydGluZ0FsbCA/IChcclxuICAgICAgICAgICAgJ+WvvOWHuuS4rS4uLidcclxuICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAg5a+85Ye66YCJ5Lit5Zu+54mHXHJcbiAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LWhpZGRlbiBwLTRcIj5cclxuICAgICAgICA8ZGl2IHJlZj17ZGlzcGxheUFyZWFSZWZ9IGNsYXNzTmFtZT1cImgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi00XCI+XHJcbiAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17c2VsZWN0QWxsRm9yRXhwb3J0fSBzaXplPVwic21cIiB2YXJpYW50PVwib3V0bGluZVwiPuWFqOmAiTwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2Rlc2VsZWN0QWxsRm9yRXhwb3J0fSBzaXplPVwic21cIiB2YXJpYW50PVwib3V0bGluZVwiPuWPlua2iOWFqOmAiTwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtbC0yXCI+5bey6YCJ5oup5a+85Ye6OiB7ZXhwb3J0QmxvY2tJZHMubGVuZ3RofSDpobk8L3NwYW4+XHJcbiAgICAgICAgICAgIHtpc0V4cG9ydGluZ0FsbCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWwtNFwiPlxyXG4gICAgICAgICAgICAgICAgPFByb2dyZXNzIHZhbHVlPXtleHBvcnRQcm9ncmVzc30gY2xhc3NOYW1lPVwiaC0xXCIgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIG1pbi1oLTBcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgcC0yIGJvcmRlciByb3VuZGVkLW1kXCI+XHJcbiAgICAgICAgICAgICAge2RhdGFDaHVua3MubWFwKChjaHVuaykgPT4gKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2NodW5rLmJsb2NrX2lkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgICAgICAgPFJhZGlvR3JvdXBcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRCbG9ja0lkfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9e2hhbmRsZUJsb2NrU2VsZWN0aW9uQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8UmFkaW9Hcm91cEl0ZW0gdmFsdWU9e2NodW5rLmJsb2NrX2lkfSBpZD17YGRpc3BsYXktJHtjaHVuay5ibG9ja19pZH1gfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9e2BkaXNwbGF5LSR7Y2h1bmsuYmxvY2tfaWR9YH0gY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAg5pi+56S6XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L1JhZGlvR3JvdXA+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgaWQ9e2BleHBvcnQtJHtjaHVuay5ibG9ja19pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17ZXhwb3J0QmxvY2tJZHMuaW5jbHVkZXMoY2h1bmsuYmxvY2tfaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IGhhbmRsZUV4cG9ydFNlbGVjdGlvbkNoYW5nZShjaHVuay5ibG9ja19pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTQgdy00IHJvdW5kZWQgYm9yZGVyLWdyYXktMzAwXCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPXtgZXhwb3J0LSR7Y2h1bmsuYmxvY2tfaWR9YH0gY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtg5pWw5o2u5Z2XICR7Y2h1bmsuYmxvY2tfaWR9ICjog7bljpo6ICR7Y2h1bmsuZ2x1ZV90aGlja25lc3NfdmFsdWVzLmxlbmd0aH0sIOWHhuebtDogJHtjaHVuay5jb2xsaW1hdGlvbl9kaWZmX3ZhbHVlcy5sZW5ndGh9KWB9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIHsoIWRhdGFDaHVua3MgfHwgZGF0YUNodW5rcy5sZW5ndGggPT09IDApICYmIChcclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHAtMlwiPuaaguaXoOaVsOaNruWdl+WPr+S+m+WIhuaekOaIluWvvOWHuuOAgjwvcD5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICA8L0NhcmQ+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IExvZ0Rpc3BsYXlBcmVhOyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiTGFiZWwiLCJCdXR0b24iLCJEb3dubG9hZCIsInVzZVRvYXN0IiwiUHJvZ3Jlc3MiLCJSYWRpb0dyb3VwIiwiUmFkaW9Hcm91cEl0ZW0iLCJMb2dEaXNwbGF5QXJlYSIsImRhdGFDaHVua3MiLCJvblNlbGVjdGlvbkNoYW5nZSIsIm9uU3RhcnRFeHBvcnQiLCJjb25zb2xlIiwibG9nIiwibGVuZ3RoIiwic2VsZWN0ZWRCbG9ja0lkIiwic2V0U2VsZWN0ZWRCbG9ja0lkIiwiZXhwb3J0QmxvY2tJZHMiLCJzZXRFeHBvcnRCbG9ja0lkcyIsImRpc3BsYXlBcmVhUmVmIiwidG9hc3QiLCJvblNlbGVjdGlvbkNoYW5nZVJlZiIsImN1cnJlbnQiLCJjdXJyZW50U2VsZWN0aW9uSXNWYWxpZCIsInRyaW0iLCJzb21lIiwiY2h1bmsiLCJibG9ja19pZCIsImZpcnN0VmFsaWRCbG9jayIsImZpbmQiLCJ3YXJuIiwic2VsZWN0ZWQiLCJmaWx0ZXIiLCJoYW5kbGVCbG9ja1NlbGVjdGlvbkNoYW5nZSIsImJsb2NrSWQiLCJoYW5kbGVFeHBvcnRTZWxlY3Rpb25DaGFuZ2UiLCJwcmV2U2VsZWN0ZWQiLCJuZXdTZWxlY3Rpb24iLCJpbmNsdWRlcyIsImlkIiwic2VsZWN0QWxsRm9yRXhwb3J0IiwiYWxsSWRzIiwibWFwIiwiZGVzZWxlY3RBbGxGb3JFeHBvcnQiLCJoYW5kbGVFeHBvcnRBbGxJbWFnZXMiLCJ2YXJpYW50IiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImhhc0NvbnRlbnRUb0V4cG9ydCIsImNsYXNzTmFtZSIsImRpdiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImlzRXhwb3J0aW5nQWxsIiwic2l6ZSIsInJlZiIsInNwYW4iLCJ2YWx1ZSIsImV4cG9ydFByb2dyZXNzIiwib25WYWx1ZUNoYW5nZSIsImh0bWxGb3IiLCJpbnB1dCIsInR5cGUiLCJjaGVja2VkIiwib25DaGFuZ2UiLCJnbHVlX3RoaWNrbmVzc192YWx1ZXMiLCJjb2xsaW1hdGlvbl9kaWZmX3ZhbHVlcyIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\n"));

/***/ })

});