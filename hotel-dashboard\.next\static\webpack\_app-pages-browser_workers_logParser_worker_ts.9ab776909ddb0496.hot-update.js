"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // V1 blocks are now strictly defined from a start phrase to an end phrase.\n    const BLOCK_REGEX_V1 = /(?:开始|打开)抽真空[\\s\\S]*?insert into g_support/g;\n    // V2 blocks have their own distinct start and end markers.\n    const BLOCK_REGEX_V2 = /轴停止运动[\\s\\S]*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        // 1. Find all possible V1 and V2 blocks independently.\n        const v1Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V1));\n        const v2Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V2));\n        // 2. Combine and sort all found blocks by their starting position in the log file.\n        const allMatches = [\n            ...v1Matches,\n            ...v2Matches\n        ].sort((a, b)=>(a.index || 0) - (b.index || 0));\n        if (allMatches.length === 0) {\n            if (DEBUG) console.log(\"[Worker] No V1 or V2 blocks found in log content.\");\n            return [];\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // 3. Process each block in the correct order.\n        allMatches.forEach((match, index)=>{\n            const blockContent = match[0];\n            if (!blockContent) return;\n            let sn = null;\n            // SN is always expected inside the block now, due to the new regex definitions.\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            }\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(index + 1) : \"block_\".concat(index + 1);\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Every matched block is valid and must be kept.\n                localProcessedBlocks.push(processedBlock);\n            }\n        });\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the full log content for searching, and parse it into blocks.\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"抽真空\";\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        // With the blocks now correctly defined, this search logic will work.\n                        for (const block of processedBlocks){\n                            if (matchedBlockIds.has(block.block_id)) continue;\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                // Check if the line contains the keyword first for efficiency\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            // Check if the timestamp of the line is within the desired search window\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {}\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("418d921c0219c55e")
/******/ })();
/******/ 
/******/ }
);