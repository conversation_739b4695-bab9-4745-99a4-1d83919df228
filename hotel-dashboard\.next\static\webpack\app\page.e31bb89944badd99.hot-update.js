"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/log-analysis/LogChartView.tsx":
/*!**************************************************!*\
  !*** ./components/log-analysis/LogChartView.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogChartView: () => (/* binding */ LogChartView),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/ComposedChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,ComposedChart,Legend,Line,ReferenceLine,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/ReferenceLine.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ LogChartView,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Helper functions (parseTimestamp, processBlockData) defined at the top-level\n// so they are accessible by both LogChartView and ChartRenderer.\nconst parseTimestamp = (timestamp)=>{\n    if (!timestamp) return NaN;\n    try {\n        let standardizedTimestamp = timestamp.replace(\",\", \".\");\n        if (!standardizedTimestamp.includes(\"T\") && standardizedTimestamp.includes(\" \")) {\n            const parts = standardizedTimestamp.split(\" \");\n            if (parts.length > 1 && parts[0].includes(\"-\") && parts[1].includes(\":\")) {\n                standardizedTimestamp = parts[0] + \"T\" + parts.slice(1).join(\" \");\n            }\n        }\n        let date = new Date(standardizedTimestamp);\n        let time = date.getTime();\n        if (isNaN(time)) {\n            const slightlyLessStandardized = timestamp.replace(\",\", \".\");\n            date = new Date(slightlyLessStandardized);\n            time = date.getTime();\n        }\n        if (isNaN(time)) {\n            date = new Date(timestamp);\n            time = date.getTime();\n        }\n        if (isNaN(time)) {\n            console.warn('[LogChartViewHelper] Failed to parse timestamp: \"'.concat(timestamp, '\"'));\n            return NaN;\n        }\n        return time;\n    } catch (error) {\n        console.error('[LogChartViewHelper] Error parsing timestamp: \"'.concat(timestamp, '\"'), error);\n        return NaN;\n    }\n};\nconst processBlockData = (block)=>{\n    const dataPoints = [];\n    (block.glue_thickness_values || []).forEach((value)=>{\n        const time = parseTimestamp(value.timestamp);\n        if (!isNaN(time)) dataPoints.push({\n            time,\n            glueThickness: value.value,\n            collimationDiff: null\n        });\n    });\n    (block.collimation_diff_values || []).forEach((value)=>{\n        const time = parseTimestamp(value.timestamp);\n        if (!isNaN(time)) dataPoints.push({\n            time,\n            glueThickness: null,\n            collimationDiff: value.value\n        });\n    });\n    dataPoints.sort((a, b)=>a.time - b.time);\n    const mergedPoints = [];\n    dataPoints.forEach((point)=>{\n        const existingPoint = mergedPoints.find((p)=>p.time === point.time);\n        if (existingPoint) {\n            if (point.glueThickness !== null) existingPoint.glueThickness = point.glueThickness;\n            if (point.collimationDiff !== null) existingPoint.collimationDiff = point.collimationDiff;\n        } else {\n            mergedPoints.push({\n                ...point\n            });\n        }\n    });\n    return mergedPoints;\n};\nconst ChartRenderer = (param)=>{\n    let { chunk, isChartReady } = param;\n    _s();\n    const { chartDataForThisBlock, eventPointsForThisBlock, timeDomainForThisBlock, glueDomainForThisBlock, collimationDomainForThisBlock, hasDataForThisBlock } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChartRenderer.useMemo\": ()=>{\n            if (!chunk) {\n                return {\n                    chartDataForThisBlock: [],\n                    eventPointsForThisBlock: [],\n                    timeDomainForThisBlock: [\n                        Date.now() - 3600000,\n                        Date.now()\n                    ],\n                    glueDomainForThisBlock: [\n                        0,\n                        1000\n                    ],\n                    collimationDomainForThisBlock: [\n                        0,\n                        0.1\n                    ],\n                    hasDataForThisBlock: false\n                };\n            }\n            const processedDataPoints = processBlockData(chunk);\n            const currentEventPoints = (chunk.valve_open_events || []).map({\n                \"ChartRenderer.useMemo.currentEventPoints\": (event)=>{\n                    const eventTime = parseTimestamp(event.timestamp);\n                    return {\n                        time: eventTime,\n                        value: 0,\n                        label: \"打开放气阀 (\".concat(event.timestamp.split(' ')[1] || event.timestamp, \")\")\n                    };\n                }\n            }[\"ChartRenderer.useMemo.currentEventPoints\"]).filter({\n                \"ChartRenderer.useMemo.currentEventPoints\": (ep)=>!isNaN(ep.time)\n            }[\"ChartRenderer.useMemo.currentEventPoints\"]);\n            currentEventPoints.sort({\n                \"ChartRenderer.useMemo\": (a, b)=>a.time - b.time\n            }[\"ChartRenderer.useMemo\"]);\n            let timeDom = [\n                Date.now() - 3600000,\n                Date.now()\n            ];\n            // Use the block's overall start and end times for the X-axis domain, as requested.\n            const blockStartTime = parseTimestamp(chunk.start_time);\n            const blockEndTime = parseTimestamp(chunk.end_time);\n            if (!isNaN(blockStartTime) && !isNaN(blockEndTime)) {\n                timeDom = [\n                    blockStartTime,\n                    blockEndTime\n                ];\n            } else if (processedDataPoints.length > 0) {\n                // Fallback to data points if block times are invalid\n                const times = processedDataPoints.map({\n                    \"ChartRenderer.useMemo.times\": (p)=>p.time\n                }[\"ChartRenderer.useMemo.times\"]).filter({\n                    \"ChartRenderer.useMemo.times\": (t)=>!isNaN(t)\n                }[\"ChartRenderer.useMemo.times\"]);\n                if (times.length > 0) {\n                    timeDom = [\n                        Math.min(...times),\n                        Math.max(...times)\n                    ];\n                }\n            }\n            if (timeDom[0] === timeDom[1]) timeDom[1] = timeDom[0] + 3600000;\n            const glueValues = processedDataPoints.map({\n                \"ChartRenderer.useMemo.glueValues\": (p)=>p.glueThickness\n            }[\"ChartRenderer.useMemo.glueValues\"]).filter({\n                \"ChartRenderer.useMemo.glueValues\": (v)=>v !== null && !isNaN(v)\n            }[\"ChartRenderer.useMemo.glueValues\"]);\n            let glueDom = [\n                0,\n                1000\n            ];\n            if (glueValues.length > 0) glueDom = [\n                Math.min(...glueValues),\n                Math.max(...glueValues)\n            ];\n            if (glueDom[0] === glueDom[1]) glueDom[1] = glueDom[0] + 10;\n            const collimValues = processedDataPoints.map({\n                \"ChartRenderer.useMemo.collimValues\": (p)=>p.collimationDiff\n            }[\"ChartRenderer.useMemo.collimValues\"]).filter({\n                \"ChartRenderer.useMemo.collimValues\": (v)=>v !== null && !isNaN(v)\n            }[\"ChartRenderer.useMemo.collimValues\"]);\n            let collimDom = [\n                0,\n                0.1\n            ];\n            if (collimValues.length > 0) collimDom = [\n                Math.min(...collimValues),\n                Math.max(...collimValues)\n            ];\n            if (collimDom[0] === collimDom[1]) collimDom[1] = collimDom[0] + 0.01;\n            return {\n                chartDataForThisBlock: processedDataPoints,\n                eventPointsForThisBlock: currentEventPoints,\n                timeDomainForThisBlock: timeDom,\n                glueDomainForThisBlock: glueDom,\n                collimationDomainForThisBlock: collimDom,\n                hasDataForThisBlock: processedDataPoints.length > 0\n            };\n        }\n    }[\"ChartRenderer.useMemo\"], [\n        chunk\n    ]);\n    const shouldRenderChartContent = hasDataForThisBlock && isChartReady;\n    if (!shouldRenderChartContent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full text-muted-foreground\",\n            children: !isChartReady ? \"图表加载中...\" : \"此数据块无有效图表数据。\"\n        }, void 0, false, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.ResponsiveContainer, {\n        width: \"100%\",\n        height: \"100%\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.ComposedChart, {\n            data: chartDataForThisBlock,\n            margin: {\n                top: 20,\n                right: 40,\n                left: 30,\n                bottom: 20\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.XAxis, {\n                    dataKey: \"time\",\n                    domain: timeDomainForThisBlock,\n                    type: \"number\",\n                    tickFormatter: (value)=>new Date(value).toLocaleTimeString(),\n                    allowDuplicatedCategory: false\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.YAxis, {\n                    yAxisId: \"glue\",\n                    orientation: \"left\",\n                    domain: glueDomainForThisBlock,\n                    type: \"number\",\n                    stroke: \"#8884d8\",\n                    label: {\n                        value: '胶厚 (μm)',\n                        angle: -90,\n                        position: 'insideLeft',\n                        offset: -5,\n                        style: {\n                            fill: '#8884d8',\n                            textAnchor: 'middle'\n                        }\n                    },\n                    tickFormatter: (value)=>value.toFixed(2),\n                    width: 70\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.YAxis, {\n                    yAxisId: \"collimation\",\n                    orientation: \"right\",\n                    domain: collimationDomainForThisBlock,\n                    type: \"number\",\n                    stroke: \"#82ca9d\",\n                    label: {\n                        value: '准直差',\n                        angle: 90,\n                        position: 'insideRight',\n                        offset: -15,\n                        style: {\n                            fill: '#82ca9d',\n                            textAnchor: 'middle'\n                        }\n                    },\n                    tickFormatter: (value)=>value.toFixed(3),\n                    width: 80\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                    labelFormatter: (value)=>new Date(value).toLocaleString(),\n                    formatter: (value, name)=>{\n                        if (typeof value !== 'number') return [\n                            value,\n                            name\n                        ];\n                        if (name === 'glueThickness') return [\n                            value.toFixed(2) + ' μm',\n                            '胶厚'\n                        ];\n                        if (name === 'collimationDiff') return [\n                            value.toFixed(3),\n                            '准直差'\n                        ];\n                        return [\n                            value,\n                            name\n                        ];\n                    },\n                    contentStyle: {\n                        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n                        border: '1px solid #ccc',\n                        borderRadius: '4px',\n                        padding: '8px'\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Legend, {\n                    verticalAlign: \"top\",\n                    height: 36,\n                    wrapperStyle: {\n                        paddingBottom: '10px'\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Line, {\n                    yAxisId: \"glue\",\n                    type: \"monotone\",\n                    dataKey: \"glueThickness\",\n                    name: \"胶厚\",\n                    stroke: \"#8884d8\",\n                    strokeWidth: 2,\n                    dot: {\n                        r: 2\n                    },\n                    activeDot: {\n                        r: 5\n                    },\n                    isAnimationActive: false,\n                    connectNulls: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Line, {\n                    yAxisId: \"collimation\",\n                    type: \"monotone\",\n                    dataKey: \"collimationDiff\",\n                    name: \"准直差\",\n                    stroke: \"#82ca9d\",\n                    strokeWidth: 2,\n                    dot: {\n                        r: 2\n                    },\n                    activeDot: {\n                        r: 5\n                    },\n                    isAnimationActive: false,\n                    connectNulls: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined),\n                eventPointsForThisBlock.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_ComposedChart_Legend_Line_ReferenceLine_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.ReferenceLine, {\n                        x: event.time,\n                        stroke: \"rgba(255,0,0,0.7)\",\n                        yAxisId: \"glue\",\n                        strokeDasharray: \"4 4\",\n                        label: {\n                            value: event.label,\n                            position: 'insideTopRight',\n                            fill: 'rgba(255,0,0,0.7)',\n                            fontSize: 10\n                        }\n                    }, \"event-\".concat(chunk.block_id, \"-\").concat(index), false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChartRenderer, \"8mDZ0hNgDzpsS3GxcLMYCTN7JZg=\");\n_c = ChartRenderer;\nfunction LogChartView(param) {\n    let { dataChunks, selectedBlockIds, onBlockSelect, isHighlighted = false } = param;\n    _s1();\n    const chartContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isChartReady, setIsChartReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogChartView.useEffect\": ()=>{\n            setIsChartReady(false); // Reset on selection change\n            const timer = setTimeout({\n                \"LogChartView.useEffect.timer\": ()=>setIsChartReady(true)\n            }[\"LogChartView.useEffect.timer\"], 100); // Delay for container sizing\n            return ({\n                \"LogChartView.useEffect\": ()=>clearTimeout(timer)\n            })[\"LogChartView.useEffect\"];\n        }\n    }[\"LogChartView.useEffect\"], [\n        selectedBlockIds,\n        dataChunks\n    ]); // Also depend on dataChunks if it can change independently for selectedBlockIds\n    const hasChartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogChartView.useMemo[hasChartData]\": ()=>{\n            console.log('LogChartView - hasChartData check:', {\n                dataChunks\n            });\n            if (!dataChunks || !Array.isArray(dataChunks)) {\n                console.log('LogChartView - dataChunks is invalid:', dataChunks);\n                return false;\n            }\n            return dataChunks.length > 0;\n        }\n    }[\"LogChartView.useMemo[hasChartData]\"], [\n        dataChunks\n    ]);\n    const chartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogChartView.useMemo[chartData]\": ()=>{\n            console.log('LogChartView - chartData calculation:', {\n                dataChunks,\n                selectedBlockIds\n            });\n            if (!dataChunks || !Array.isArray(dataChunks)) {\n                console.log('LogChartView - dataChunks is invalid in chartData:', dataChunks);\n                return [];\n            }\n            return dataChunks.filter({\n                \"LogChartView.useMemo[chartData]\": (chunk)=>{\n                    console.log('Filtering chunk:', {\n                        chunk,\n                        selectedBlockIds\n                    });\n                    return selectedBlockIds.includes(chunk.block_id);\n                }\n            }[\"LogChartView.useMemo[chartData]\"]).flatMap({\n                \"LogChartView.useMemo[chartData]\": (chunk)=>{\n                    console.log('Processing chunk for chart data:', chunk);\n                    if (!chunk.data || !Array.isArray(chunk.data)) {\n                        console.log('Invalid chunk data:', chunk.data);\n                        return [];\n                    }\n                    return chunk.data.map({\n                        \"LogChartView.useMemo[chartData]\": (item)=>({\n                                name: item.name,\n                                value: item.value,\n                                type: item.type,\n                                block_id: chunk.block_id\n                            })\n                    }[\"LogChartView.useMemo[chartData]\"]);\n                }\n            }[\"LogChartView.useMemo[chartData]\"]);\n        }\n    }[\"LogChartView.useMemo[chartData]\"], [\n        dataChunks,\n        selectedBlockIds\n    ]);\n    const handleBlockSelect = (blockId)=>{\n        console.log('LogChartView - handleBlockSelect called with:', blockId);\n        onBlockSelect(blockId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: chartContainerRef,\n        className: \"space-y-6 log-chart-container\",\n        children: dataChunks.map((chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                \"data-block-id\": chunk.block_id,\n                className: \"\".concat(selectedBlockIds.includes(chunk.block_id) ? 'block' : 'hidden', \" \").concat(isHighlighted ? 'ring-2 ring-offset-2 ring-blue-500' : ''),\n                style: {\n                    display: selectedBlockIds.includes(chunk.block_id) ? 'block' : 'none',\n                    minHeight: selectedBlockIds.includes(chunk.block_id) ? '450px' : '0'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row justify-between items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: [\n                                \"数据块 \",\n                                chunk.block_id\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-[400px] min-h-[400px]\",\n                            style: {\n                                width: '100%',\n                                height: '400px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartRenderer, {\n                                chunk: chunk,\n                                isChartReady: isChartReady\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, chunk.block_id, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\LogChartView.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n_s1(LogChartView, \"HjaQbVhm0E28jgmHJb3zubxsEFg=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c1 = LogChartView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogChartView);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartRenderer\");\n$RefreshReg$(_c1, \"LogChartView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/LogChartView.tsx\n"));

/***/ })

});