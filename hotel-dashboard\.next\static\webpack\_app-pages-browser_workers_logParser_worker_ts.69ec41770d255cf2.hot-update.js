"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // The regexes need to be aware of each other's starting delimiters to prevent overlap.\n    const BLOCK_REGEX_V1 = /(?:开始|打开)抽真空(?:(?!打开真空泵|开始抽真空|轴停止运动)[\\s\\S])*?insert into g_support/g;\n    const BLOCK_REGEX_V2 = /轴停止运动(?:(?!轴停止运动|打开真空泵|开始抽真空)[\\s\\S])*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \": startTime=\").concat(startTimeStr, \", endTime=\").concat(endTimeStr));\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        const allMatches = [];\n        let lastIndex = 0;\n        const MAX_ITERATIONS_OUTER = 50000; // Safety break\n        let outerIteration = 0;\n        // Iteratively find all V1 and V2 blocks in the correct order\n        while(lastIndex < logContent.length){\n            outerIteration++;\n            if (outerIteration > MAX_ITERATIONS_OUTER) {\n                console.error(\"Outer loop safety break triggered during block finding.\");\n                break;\n            }\n            BLOCK_REGEX_V1.lastIndex = lastIndex;\n            BLOCK_REGEX_V2.lastIndex = lastIndex;\n            const matchV1 = BLOCK_REGEX_V1.exec(logContent);\n            const matchV2 = BLOCK_REGEX_V2.exec(logContent);\n            let firstMatch = null;\n            // Determine which match (if any) comes first\n            if (matchV1 && matchV2) {\n                firstMatch = matchV1.index < matchV2.index ? matchV1 : matchV2;\n            } else {\n                firstMatch = matchV1 || matchV2;\n            }\n            if (firstMatch && typeof firstMatch.index !== 'undefined') {\n                allMatches.push(firstMatch);\n                // Advance lastIndex past the end of the found block to continue searching\n                lastIndex = firstMatch.index + firstMatch[0].length;\n            } else {\n                break; // No more matches found\n            }\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // Process all the matches that were found in order\n        allMatches.forEach((match, index)=>{\n            const blockContent = match[0];\n            if (!blockContent || typeof match.index === 'undefined') return;\n            let sn = null;\n            // 1. Try to find SN inside the current block\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            } else {\n                // 2. If not found, search for the next SN *after* this block but *before* the next one\n                const endOfCurrentBlock = match.index + blockContent.length;\n                const nextMatch = allMatches[index + 1];\n                const searchArea = logContent.substring(endOfCurrentBlock);\n                const snMatchOutside = snRegex.exec(searchArea);\n                if (snMatchOutside) {\n                    // Check if the found SN is located *before* the start of the next block.\n                    if (!nextMatch || endOfCurrentBlock + snMatchOutside.index < (nextMatch.index || 0)) {\n                        sn = snMatchOutside[1];\n                    }\n                }\n            }\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(index + 1) : \"block_\".concat(index + 1);\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Only add blocks that contain meaningful data\n                if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0) {\n                    localProcessedBlocks.push(processedBlock);\n                }\n            }\n        });\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the parsed blocks globally in the worker\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    const keyword = \"抽真空\"; // Keyword is kept generic. The main fix is the block regex.\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        const lowerBound = targetTime - 1000; // -1 second\n                        const upperBound = targetTime + 1000; // +1 second\n                        for (const block of processedBlocks){\n                            // Avoid re-checking a block that already has a match\n                            if (matchedBlockIds.has(block.block_id)) {\n                                continue;\n                            }\n                            const lines = block.raw_content.split('\\n');\n                            for (const line of lines){\n                                if (line.includes(keyword)) {\n                                    const lineTsStr = _extractTimestampFromLine(line);\n                                    if (lineTsStr) {\n                                        try {\n                                            // Timestamps are in 'YYYY-MM-DD HH:mm:ss,SSS' format\n                                            const lineTime = new Date(lineTsStr.replace(',', '.')).getTime();\n                                            if (lineTime >= lowerBound && lineTime <= upperBound) {\n                                                matchedBlockIds.add(block.block_id);\n                                                break;\n                                            }\n                                        } catch (e) {\n                                        // Ignore lines with invalid date formats\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("930d201124f28ccd")
/******/ })();
/******/ 
/******/ }
);