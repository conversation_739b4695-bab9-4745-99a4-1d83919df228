"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// hotel-dashboard/workers/logParser.worker.ts\n/// <reference lib=\"webworker\" />\n// Import types from the definitions file\n// 新增：日志版本枚举\nvar LogVersion = /*#__PURE__*/ function(LogVersion) {\n    LogVersion[\"V1\"] = \"V1\";\n    LogVersion[\"V2\"] = \"V2\";\n    LogVersion[\"UNKNOWN\"] = \"UNKNOWN\";\n    return LogVersion;\n}(LogVersion || {});\n// Check if running in a Web Worker context\nif (typeof DedicatedWorkerGlobalScope === \"undefined\" || !(self instanceof DedicatedWorkerGlobalScope) || typeof self.postMessage !== 'function') {} else {\n    // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.\n    const DEBUG = false; // Set to true to enable detailed logs\n    let processedBlocks = []; // Store processed blocks in memory\n    let originalLogContent = ''; // Store the full original log content\n    // Regex for extracting timestamp from a log line\n    const TIMESTAMP_REGEX = /^\\w+\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n    // --- V1 正则表达式 ---\n    // V1 blocks are now strictly defined from a start phrase to an end phrase.\n    const BLOCK_REGEX_V1 = /(?:开始|打开)抽真空[\\s\\S]*?insert into g_support/g;\n    // V2 blocks have their own distinct start and end markers.\n    const BLOCK_REGEX_V2 = /轴停止运动[\\s\\S]*?SetSpeed:2, 85, result:/g;\n    function _extractTimestampFromLine(line) {\n        const match = TIMESTAMP_REGEX.exec(line);\n        return match ? match[1] : null;\n    }\n    function calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str) {\n        try {\n            const x1 = parseFloat(x1Str);\n            const y1 = parseFloat(y1Str);\n            const x2 = parseFloat(x2Str);\n            const y2 = parseFloat(y2Str);\n            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {\n                return null;\n            }\n            return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * NEW UNIFIED FUNCTION\r\n   * Processes a raw block of log content, checking each line against all known data formats (V1 and V2).\r\n   */ function processRawBlock(blockId, blockContent) {\n        if (DEBUG) console.log(\"[Worker] Processing block \".concat(blockId, \" with unified parser. Content snippet: \").concat(blockContent.substring(0, 100), \"...\"));\n        if (!blockContent) return null;\n        const blockLines = blockContent.split('\\n');\n        if (!blockLines.length) return null;\n        let startTimeStr = null;\n        let endTimeStr = null;\n        // Find first and last timestamps in the block\n        for(let i = 0; i < blockLines.length; i++){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                startTimeStr = ts;\n                break;\n            }\n        }\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const ts = _extractTimestampFromLine(blockLines[i]);\n            if (ts) {\n                endTimeStr = ts;\n                break;\n            }\n        }\n        if (!endTimeStr && startTimeStr) {\n            endTimeStr = startTimeStr;\n        }\n        // Get timestamps for specific start/end sentences for debugging, as requested by the user.\n        const startSentenceTs = _extractTimestampFromLine(blockLines[0]);\n        let endSentenceTs = null;\n        // Find the last line containing a known end-of-block marker to get its timestamp.\n        for(let i = blockLines.length - 1; i >= 0; i--){\n            const line = blockLines[i];\n            if (line.includes('insert into g_support') || line.includes('SetSpeed:2, 85, result:')) {\n                endSentenceTs = _extractTimestampFromLine(line);\n                break; // Found the last marker line\n            }\n        }\n        if (DEBUG) {\n            // Enhanced logging as requested by the user.\n            console.log(\"[Worker] Block \".concat(blockId, \": \") + \"StartLineTS=\".concat(startSentenceTs, \", EndLineTS=\").concat(endSentenceTs, \", \") + \"OverallStartTS=\".concat(startTimeStr, \", OverallEndTS=\").concat(endTimeStr));\n        }\n        const valveOpenEventsList = [];\n        const glueThicknessValuesList = [];\n        const collimationDiffValuesList = [];\n        blockLines.forEach((line, index)=>{\n            const currentLineTimestamp = _extractTimestampFromLine(line);\n            const timestampForValue = currentLineTimestamp || startTimeStr;\n            // Check for V1 Glue\n            const glueMatchV1 = /####### 胶厚值:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV1) {\n                try {\n                    const valueFloat = parseFloat(glueMatchV1[1]);\n                    if (timestampForValue) {\n                        glueThicknessValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V1 Diff\n            const diffMatchV1 = /####### 准直diff:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV1) {\n                try {\n                    const valueFloat = parseFloat(diffMatchV1[1]);\n                    if (timestampForValue && Math.abs(valueFloat) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: valueFloat\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V1 Diff: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                    }\n                } catch (e) {}\n            }\n            // Check for V2 Glue\n            const glueMatchV2 = /Thickness:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (glueMatchV2) {\n                const valueFloat = parseFloat(glueMatchV2[1]);\n                if (timestampForValue && !isNaN(valueFloat)) {\n                    glueThicknessValuesList.push({\n                        timestamp: timestampForValue,\n                        value: valueFloat\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found V2 Glue: \").concat(valueFloat, \" at \").concat(timestampForValue));\n                }\n            }\n            // Check for V2 Diff\n            const diffMatchV2 = /点13均值x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点13均值y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2x:([+-]?(?:\\d+\\.?\\d*|\\.\\d+)),\\s*点2y:([+-]?(?:\\d+\\.?\\d*|\\.\\d+))/.exec(line);\n            if (diffMatchV2) {\n                if (timestampForValue) {\n                    const [, x1Str, y1Str, x2Str, y2Str] = diffMatchV2;\n                    const diffValue = calculateV2CollimationDiff(x1Str, y1Str, x2Str, y2Str);\n                    if (diffValue !== null && Math.abs(diffValue) <= 100) {\n                        collimationDiffValuesList.push({\n                            timestamp: timestampForValue,\n                            value: diffValue\n                        });\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found and kept V2 Diff: \").concat(diffValue, \" at \").concat(timestampForValue));\n                    } else if (diffValue !== null) {\n                        if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found but discarded V2 Diff: \").concat(diffValue, \" (abs > 100) at \").concat(timestampForValue));\n                    }\n                }\n            }\n            // Check for Valve Open Event (Generic)\n            if (line.includes(\"打开放气阀\")) {\n                if (currentLineTimestamp) {\n                    valveOpenEventsList.push({\n                        timestamp: currentLineTimestamp,\n                        line_content: line.trim()\n                    });\n                    if (DEBUG) console.log(\"[Worker] Block \".concat(blockId, \", Line \").concat(index + 1, \": Found '打开放气阀' at \").concat(currentLineTimestamp));\n                }\n            }\n        });\n        if (DEBUG) {\n            console.log(\"[Worker] Block \".concat(blockId, \" processing finished. Glue values: \").concat(glueThicknessValuesList.length, \", Diff values: \").concat(collimationDiffValuesList.length));\n        }\n        return {\n            block_id: blockId,\n            start_time: startTimeStr,\n            end_time: endTimeStr,\n            lines_count: blockLines.length,\n            valve_open_events: valveOpenEventsList,\n            glue_thickness_values: glueThicknessValuesList,\n            collimation_diff_values: collimationDiffValuesList,\n            raw_content: blockContent,\n            sns: []\n        };\n    }\n    /**\r\n   * UPDATED parseLogContent\r\n   * Parses the entire log content, using version detection ONLY for block boundaries,\r\n   * but ALWAYS using the unified processRawBlock for content parsing.\r\n   */ function parseLogContent(logContent) {\n        if (!logContent) return [];\n        // 1. Find all possible V1 and V2 blocks independently.\n        const v1Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V1));\n        const v2Matches = Array.from(logContent.matchAll(BLOCK_REGEX_V2));\n        // 2. Combine and sort all found blocks by their starting position in the log file.\n        const allMatches = [\n            ...v1Matches,\n            ...v2Matches\n        ].sort((a, b)=>(a.index || 0) - (b.index || 0));\n        if (allMatches.length === 0) {\n            if (DEBUG) console.log(\"[Worker] No V1 or V2 blocks found in log content.\");\n            return [];\n        }\n        const localProcessedBlocks = [];\n        const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n        // 3. Process each block in the correct order.\n        allMatches.forEach((match, index)=>{\n            const blockContent = match[0];\n            if (!blockContent) return;\n            let sn = null;\n            // SN is always expected inside the block now, due to the new regex definitions.\n            const snMatchInside = snRegex.exec(blockContent);\n            if (snMatchInside) {\n                sn = snMatchInside[1];\n            }\n            const blockId = sn ? \"\".concat(sn, \"_\").concat(index + 1) : \"block_\".concat(index + 1);\n            const processedBlock = processRawBlock(blockId, blockContent);\n            if (processedBlock) {\n                if (sn) {\n                    processedBlock.sns.push(sn);\n                }\n                // Every matched block is valid and must be kept.\n                localProcessedBlocks.push(processedBlock);\n            }\n        });\n        return localProcessedBlocks;\n    }\n    // Worker message handling\n    self.onmessage = function(event) {\n        if (DEBUG) console.log(\"[Worker] Message received: \".concat(event.data.type));\n        const { type, payload } = event.data;\n        switch(type){\n            case 'PARSE_LOG':\n                try {\n                    const logContent = payload;\n                    if (!logContent || typeof logContent !== 'string') {\n                        throw new Error('Invalid log content received by worker.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Received log content. Length: \".concat(logContent.length, \". Starting parsing...\"));\n                    // Store the full log content for searching, and parse it into blocks.\n                    originalLogContent = logContent;\n                    processedBlocks = parseLogContent(logContent);\n                    // Create a version of the blocks to send to the main thread that does NOT include the raw_content\n                    const blocksForFrontend = processedBlocks.map((block)=>{\n                        const { raw_content, ...rest } = block;\n                        return rest;\n                    });\n                    if (blocksForFrontend.length > 0) {\n                        if (DEBUG) console.log(\"[Worker] Sending \".concat(blocksForFrontend.length, \" processed blocks to main thread.\"));\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: blocksForFrontend,\n                            message: \"Successfully processed \".concat(blocksForFrontend.length, \" blocks.\")\n                        });\n                    } else {\n                        if (DEBUG) console.log('[Worker] Parsing successful, but no relevant data blocks were found.');\n                        self.postMessage({\n                            type: 'PARSE_LOG_RESULT',\n                            success: true,\n                            allBlocks: [],\n                            message: 'No relevant data blocks were found in the log file.'\n                        });\n                    }\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Critical error during log processing:', error);\n                    self.postMessage({\n                        type: 'PARSE_LOG_RESULT',\n                        success: false,\n                        error: error.message || 'An unknown error occurred in the worker.'\n                    });\n                }\n                break;\n            case 'MATCH_BY_TIMESTAMP':\n                try {\n                    const { timestamps } = payload;\n                    if (!Array.isArray(timestamps)) {\n                        throw new Error('Invalid timestamps payload received.');\n                    }\n                    if (DEBUG) console.log(\"[Worker] Starting timestamp range match for \".concat(timestamps.length, \" timestamps.\"));\n                    const matchedBlockIds = new Set();\n                    for (const targetTs of timestamps){\n                        const targetTime = new Date(targetTs).getTime();\n                        for (const block of processedBlocks){\n                            // Check if the block has valid start and end times.\n                            if (block.start_time && block.end_time) {\n                                try {\n                                    const blockStartTime = new Date(block.start_time.replace(',', '.')).getTime();\n                                    const blockEndTime = new Date(block.end_time.replace(',', '.')).getTime();\n                                    // The correct logic, as per user instruction:\n                                    // Check if the image's timestamp falls within the block's time range.\n                                    if (targetTime >= blockStartTime && targetTime <= blockEndTime) {\n                                        if (DEBUG) console.log(\"[Worker] Timestamp \".concat(targetTime, \" falls within block \").concat(block.block_id, \" range [\").concat(blockStartTime, \" - \").concat(blockEndTime, \"]. Match found.\"));\n                                        matchedBlockIds.add(block.block_id);\n                                    }\n                                } catch (e) {\n                                    if (DEBUG) console.error(\"[Worker] Could not parse timestamp for block \".concat(block.block_id, \".\"), e);\n                                }\n                            }\n                        }\n                    }\n                    const resultIds = Array.from(matchedBlockIds);\n                    if (DEBUG) console.log(\"[Worker] Timestamp match finished. Found \".concat(resultIds.length, \" matching blocks.\"));\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        matchedBlockIds: resultIds\n                    });\n                } catch (error) {\n                    if (DEBUG) console.error('[Worker] Error during timestamp matching:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: error.message || 'An unknown error occurred during matching.'\n                    });\n                }\n                break;\n            default:\n                if (DEBUG) console.warn(\"[Worker] Unknown message type received: \".concat(type));\n                break;\n        }\n    };\n    self.onerror = function(errorEvent) {};\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("b30190adecd2e7d9")
/******/ })();
/******/ 
/******/ }
);