"use client";

import React, { useMemo, useState, useRef, useEffect } from 'react';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  ComposedChart
} from 'recharts'; 
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { exportElementAsImage } from '../../lib/exportUtils';
import { useToast } from '@/components/ui/use-toast';
import { ProcessedBlock as WorkerProcessedBlock } from '@/workers/logParser.definitions';

interface ChartDataItem {
  name: string;
  value: number;
  type: string;
  block_id: string;
}

interface ProcessedBlock extends WorkerProcessedBlock {
  data: ChartDataItem[];
}

interface LogChartViewProps {
  dataChunks: ProcessedBlock[];
  selectedBlockIds: string[];
  onBlockSelect: (blockId: string) => void;
}

interface ChartDataPoint {
  time: number; 
  glueThickness: number | null;
  collimationDiff: number | null;
}

interface EventPoint { 
  time: number;
  value: number;
  label: string;
}

// Helper functions (parseTimestamp, processBlockData) defined at the top-level
// so they are accessible by both LogChartView and ChartRenderer.
const parseTimestamp = (timestamp: string | null | undefined): number => {
  if (!timestamp) return NaN;
  try {
    let standardizedTimestamp = timestamp.replace(",", ".");
    if (!standardizedTimestamp.includes("T") && standardizedTimestamp.includes(" ")) {
      const parts = standardizedTimestamp.split(" ");
      if (parts.length > 1 && parts[0].includes("-") && parts[1].includes(":")) {
        standardizedTimestamp = parts[0] + "T" + parts.slice(1).join(" ");
      }
    }
    let date = new Date(standardizedTimestamp);
    let time = date.getTime();
    if (isNaN(time)) {
      const slightlyLessStandardized = timestamp.replace(",", ".");
      date = new Date(slightlyLessStandardized);
      time = date.getTime();
    }
    if (isNaN(time)) {
      date = new Date(timestamp);
      time = date.getTime();
    }
    if (isNaN(time)) {
      console.warn(`[LogChartViewHelper] Failed to parse timestamp: "${timestamp}"`);
      return NaN;
    }
    return time;
  } catch (error) {
    console.error(`[LogChartViewHelper] Error parsing timestamp: "${timestamp}"`, error);
    return NaN;
  }
};

const processBlockData = (block: ProcessedBlock): ChartDataPoint[] => {
  const dataPoints: ChartDataPoint[] = [];
  (block.glue_thickness_values || []).forEach(value => {
    const time = parseTimestamp(value.timestamp);
    if (!isNaN(time)) dataPoints.push({ time, glueThickness: value.value, collimationDiff: null });
  });
  (block.collimation_diff_values || []).forEach(value => {
    const time = parseTimestamp(value.timestamp);
    if (!isNaN(time)) dataPoints.push({ time, glueThickness: null, collimationDiff: value.value });
  });
  dataPoints.sort((a, b) => a.time - b.time);
  const mergedPoints: ChartDataPoint[] = [];
  dataPoints.forEach(point => {
    const existingPoint = mergedPoints.find(p => p.time === point.time);
    if (existingPoint) {
      if (point.glueThickness !== null) existingPoint.glueThickness = point.glueThickness;
      if (point.collimationDiff !== null) existingPoint.collimationDiff = point.collimationDiff;
    } else {
      mergedPoints.push({ ...point });
    }
  });
  return mergedPoints;
};

interface ChartRendererProps {
  chunk: ProcessedBlock;
  isChartReady: boolean;
}

const ChartRenderer: React.FC<ChartRendererProps> = ({ chunk, isChartReady }) => {
  const {
    chartDataForThisBlock,
    eventPointsForThisBlock,
    timeDomainForThisBlock,
    glueDomainForThisBlock,
    collimationDomainForThisBlock,
    hasDataForThisBlock
  } = useMemo(() => {
    if (!chunk) {
      return {
        chartDataForThisBlock: [], eventPointsForThisBlock: [],
        timeDomainForThisBlock: [Date.now() - 3600000, Date.now()] as [number, number],
        glueDomainForThisBlock: [0, 1000] as [number, number], collimationDomainForThisBlock: [0, 0.1] as [number, number],
        hasDataForThisBlock: false,
      };
    }
    const processedDataPoints = processBlockData(chunk);
    const currentEventPoints: EventPoint[] = (chunk.valve_open_events || []).map(event => {
      const eventTime = parseTimestamp(event.timestamp);
      return { time: eventTime, value: 0, label: `打开放气阀 (${event.timestamp.split(' ')[1] || event.timestamp})` };
    }).filter(ep => !isNaN(ep.time));
    currentEventPoints.sort((a, b) => a.time - b.time);

    let timeDom: [number, number] = [Date.now() - 3600000, Date.now()];
    if (processedDataPoints.length > 0) {
        const times = processedDataPoints.map(p => p.time).filter(t => !isNaN(t));
        if (times.length > 0) {
            timeDom = [Math.min(...times), Math.max(...times)];
        }
    }
    if (timeDom[0] === timeDom[1]) timeDom[1] = timeDom[0] + 3600000;


    const glueValues = processedDataPoints.map(p => p.glueThickness).filter((v): v is number => v !== null && !isNaN(v));
    let glueDom: [number, number] = [0, 1000];
    if (glueValues.length > 0) glueDom = [Math.min(...glueValues), Math.max(...glueValues)];
    if (glueDom[0] === glueDom[1]) glueDom[1] = glueDom[0] + 10;
    
    const collimValues = processedDataPoints.map(p => p.collimationDiff).filter((v): v is number => v !== null && !isNaN(v));
    let collimDom: [number, number] = [0, 0.1];
    if (collimValues.length > 0) collimDom = [Math.min(...collimValues), Math.max(...collimValues)];
    if (collimDom[0] === collimDom[1]) collimDom[1] = collimDom[0] + 0.01;

    return {
      chartDataForThisBlock: processedDataPoints, eventPointsForThisBlock: currentEventPoints,
      timeDomainForThisBlock: timeDom, glueDomainForThisBlock: glueDom,
      collimationDomainForThisBlock: collimDom, hasDataForThisBlock: processedDataPoints.length > 0,
    };
  }, [chunk]);

  const shouldRenderChartContent = hasDataForThisBlock && isChartReady;

  if (!shouldRenderChartContent) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        {!isChartReady ? "图表加载中..." : "此数据块无有效图表数据。"}
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <ComposedChart data={chartDataForThisBlock} margin={{ top: 20, right: 40, left: 30, bottom: 20 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="time" domain={timeDomainForThisBlock} type="number" tickFormatter={(value) => new Date(value).toLocaleTimeString()} allowDuplicatedCategory={false} />
        <YAxis yAxisId="glue" orientation="left" domain={glueDomainForThisBlock} type="number" stroke="#8884d8" label={{ value: '胶厚 (μm)', angle: -90, position: 'insideLeft', offset: -5, style: { fill: '#8884d8', textAnchor: 'middle' } }} tickFormatter={(value) => value.toFixed(2)} width={70} />
        <YAxis yAxisId="collimation" orientation="right" domain={collimationDomainForThisBlock} type="number" stroke="#82ca9d" label={{ value: '准直差', angle: 90, position: 'insideRight', offset: -15, style: { fill: '#82ca9d', textAnchor: 'middle' } }} tickFormatter={(value) => value.toFixed(3)} width={80} />
        <Tooltip labelFormatter={(value) => new Date(value).toLocaleString()} formatter={(value: any, name: string) => { if (typeof value !== 'number') return [value, name]; if (name === 'glueThickness') return [value.toFixed(2) + ' μm', '胶厚']; if (name === 'collimationDiff') return [value.toFixed(3), '准直差']; return [value, name]; }} contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', border: '1px solid #ccc', borderRadius: '4px', padding: '8px' }} />
        <Legend verticalAlign="top" height={36} wrapperStyle={{ paddingBottom: '10px' }} />
        <Line yAxisId="glue" type="monotone" dataKey="glueThickness" name="胶厚" stroke="#8884d8" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 5 }} isAnimationActive={false} connectNulls={true} />
        <Line yAxisId="collimation" type="monotone" dataKey="collimationDiff" name="准直差" stroke="#82ca9d" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 5 }} isAnimationActive={false} connectNulls={true} />
        {eventPointsForThisBlock.map((event, index) => (
          <ReferenceLine key={`event-${chunk.block_id}-${index}`} x={event.time} stroke="rgba(255,0,0,0.7)" yAxisId="glue" strokeDasharray="4 4" label={{ value: event.label, position: 'insideTopRight', fill: 'rgba(255,0,0,0.7)', fontSize: 10 }} />
        ))}
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export function LogChartView({ dataChunks, selectedBlockIds, onBlockSelect }: LogChartViewProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false); // For potential individual chart export button
  const [isChartReady, setIsChartReady] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    setIsChartReady(false); // Reset on selection change
    const timer = setTimeout(() => setIsChartReady(true), 100); // Delay for container sizing
    return () => clearTimeout(timer);
  }, [selectedBlockIds, dataChunks]); // Also depend on dataChunks if it can change independently for selectedBlockIds

  // Optional: Handler for individual chart export, if a button is added per card
  const handleExportSingleChart = async (blockIdToExport: string) => {
    if (!chartContainerRef.current) {
      toast({ variant: 'destructive', title: '错误', description: '图表容器未找到。' });
      return;
    }
    setIsExporting(true);
    try {
      await exportElementAsImage(
        chartContainerRef.current, // Parent container
        `log-chart-single-${blockIdToExport.substring(0,8)}`,
        [blockIdToExport] // Pass as array
      );
    } catch (error) {
      console.error("Single chart export failed for block:", blockIdToExport, error);
      // Toast for error is likely handled in exportElementAsImage
    } finally {
      setIsExporting(false);
    }
  };

  const hasChartData = useMemo(() => {
    console.log('LogChartView - hasChartData check:', { dataChunks });
    if (!dataChunks || !Array.isArray(dataChunks)) {
      console.log('LogChartView - dataChunks is invalid:', dataChunks);
      return false;
    }
    return dataChunks.length > 0;
  }, [dataChunks]);

  const chartData = useMemo(() => {
    console.log('LogChartView - chartData calculation:', { dataChunks, selectedBlockIds });
    if (!dataChunks || !Array.isArray(dataChunks)) {
      console.log('LogChartView - dataChunks is invalid in chartData:', dataChunks);
      return [];
    }
    return dataChunks
      .filter(chunk => {
        console.log('Filtering chunk:', { chunk, selectedBlockIds });
        return selectedBlockIds.includes(chunk.block_id);
      })
      .flatMap(chunk => {
        console.log('Processing chunk for chart data:', chunk);
        if (!chunk.data || !Array.isArray(chunk.data)) {
          console.log('Invalid chunk data:', chunk.data);
          return [];
        }
        return chunk.data.map(item => ({
          name: item.name,
          value: item.value,
          type: item.type,
          block_id: chunk.block_id
        }));
      });
  }, [dataChunks, selectedBlockIds]);

  const handleBlockSelect = (blockId: string) => {
    console.log('LogChartView - handleBlockSelect called with:', blockId);
    onBlockSelect(blockId);
  };

  const handleExport = async () => {
    console.log('LogChartView - handleExport called');
    console.log('chartContainerRef:', chartContainerRef.current);
    console.log('selectedBlockIds:', selectedBlockIds);

    if (!chartContainerRef.current) {
      toast({ variant: 'destructive', title: '错误', description: '图表容器未找到。' });
      return;
    }
    if (!selectedBlockIds || selectedBlockIds.length === 0) {
      toast({ variant: 'destructive', title: '错误', description: '请至少选择一个数据块进行导出。' });
      return;
    }
    try {
      await exportElementAsImage(chartContainerRef.current, `log-analysis-${Date.now()}`, selectedBlockIds);
    } catch (error) {
      console.error("ChartView export failed:", error);
      toast({ variant: 'destructive', title: '导出失败', description: error instanceof Error ? error.message : '导出过程中发生错误。' });
    }
  };

  return (
    <div ref={chartContainerRef} className="space-y-6 log-chart-container">
      {dataChunks.map((chunk) => (
        <Card
          key={chunk.block_id}
          data-block-id={chunk.block_id}
          className={selectedBlockIds.includes(chunk.block_id) ? 'block' : 'hidden'}
          style={{
            display: selectedBlockIds.includes(chunk.block_id) ? 'block' : 'none',
            minHeight: selectedBlockIds.includes(chunk.block_id) ? '450px' : '0'
          }}
        >
          <CardHeader className="flex flex-row justify-between items-center">
            <CardTitle>数据块 {chunk.block_id}</CardTitle>
            {/*
            Example for an individual export button per card:
            <Button
              onClick={() => handleExportSingleChart(chunk.block_id)}
              size="sm"
              variant="outline"
              disabled={isExporting || !isChartReady}
            >
              <Download className="mr-2 h-4 w-4" />
              导出此图
            </Button>
            */}
          </CardHeader>
          <CardContent className="p-0">
            <div className="w-full h-[400px] min-h-[400px]" style={{ width: '100%', height: '400px' }}>
              <ChartRenderer chunk={chunk} isChartReady={isChartReady} />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default LogChartView;